.d-inline-block {
  display: inline-block !important;
}
.d-block {
  display: block !important;
}
.d-none {
  display: none !important;
}
.d-flex {
  display: flex !important;
}
.align-center {
  align-items: center !important;
}
.justify-center {
  justify-content: center !important;
}
.ml-1 {
  margin-left: 0.5rem;
}

.rounded {
  border-radius: 0.25rem !important;
}

.box-shadow {
  box-shadow: 0 1px 1px 0 rgba(60, 75, 100, 0.14),
    0 2px 1px -1px rgba(60, 75, 100, 0.12), 0 1px 3px 0 rgba(60, 75, 100, 0.2);
}

.h-42p {
  height: 42px;
}
.mt-1 {
  margin-top: 0.25rem;
}
.mr-1 {
  margin-right: 0.25rem;
}
.mt-3 {
  margin-top: 1rem;
}
.mb-3 {
  margin-bottom: 1rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.p-3 {
  padding: 1rem;
}

.pt-0 {
  padding-top: 0;
}

.py-1 {
  padding-top: 0.25rem !important;
  padding-bottom: 0.25rem !important;
}

.w-100 {
  width: 100% !important;
}

.min-w-150 {
  min-width: 150px;
}

.w-150 {
  width: 150px;
}

.w-160 {
  width: 160px;
}

.pointer {
  cursor: pointer !important;
}

.badge-yellow {
  color: #fff;
  background-color: #efb540;
}

.badge-orange {
  color: #fff;
  background-color: #ff6601;
}

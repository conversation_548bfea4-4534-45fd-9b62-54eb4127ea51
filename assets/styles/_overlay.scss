#sportaabe-overlay-96cb7974a9_mtg6mtm6ntu {
  position: absolute;
  z-index: 2147483647;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;

  transform: translate3d(0, 0, 0);

  background-color: black;
  background-color: rgba(0, 0, 0, 0.8);
  background: radial-gradient(
    50% 50%,
    ellipse closest-corner,
    rgba(0, 0, 0, 0.6) 1%,
    rgba(0, 0, 0, 0.8) 100%
  );

  color: #fff;
}

#sportaabe-overlay-96cb7974a9_mtg6mtm6ntu a {
  color: #fff;
}

#sportaabe-overlay-96cb7974a9_mtg6mtm6ntu .sportaabe-checkout-close:before,
#sportaabe-overlay-96cb7974a9_mtg6mtm6ntu .sportaabe-checkout-close:after {
  background-color: #fff;
}

#sportaabe-overlay-96cb7974a9_mtg6mtm6ntu.sportaabe-overlay-context-popup {
  cursor: pointer;
}

#sportaabe-overlay-96cb7974a9_mtg6mtm6ntu a {
  text-decoration: none;
}

#sportaabe-overlay-96cb7974a9_mtg6mtm6ntu .sportaabe-checkout-modal {
  font-family: "HelveticaNeue", "HelveticaNeue-Light", "Helvetica Neue Light",
    helvetica, arial, sans-serif;
  font-size: 14px;
  text-align: center;

  box-sizing: border-box;
  max-width: 350px;
  top: 50%;
  left: 50%;
  position: absolute;
  transform: translateX(-50%) translateY(-50%);
  cursor: pointer;
  text-align: center;
}

#sportaabe-overlay-96cb7974a9_mtg6mtm6ntu.sportaabe-overlay-loading
  .sportaabe-checkout-message,
#sportaabe-overlay-96cb7974a9_mtg6mtm6ntu.sportaabe-overlay-loading
  .sportaabe-checkout-continue {
  display: none;
}

.sportaabe-checkout-loader {
  display: none;
}

#sportaabe-overlay-96cb7974a9_mtg6mtm6ntu.sportaabe-overlay-loading
  .sportaabe-checkout-loader {
  display: block;
}

#sportaabe-overlay-96cb7974a9_mtg6mtm6ntu
  .sportaabe-checkout-modal
  .sportaabe-checkout-logo {
  cursor: pointer;
  margin-bottom: 30px;
  display: inline-block;
}

#sportaabe-overlay-96cb7974a9_mtg6mtm6ntu
  .sportaabe-checkout-modal
  .sportaabe-checkout-logo
  img {
  height: 36px;
}

#sportaabe-overlay-96cb7974a9_mtg6mtm6ntu
  .sportaabe-checkout-modal
  .sportaabe-checkout-logo
  img.sportaabe-checkout-logo-pp {
  margin-right: 10px;
}

#sportaabe-overlay-96cb7974a9_mtg6mtm6ntu
  .sportaabe-checkout-modal
  .sportaabe-checkout-message {
  font-size: 15px;
  line-height: 1.5;
  padding: 10px 0;
}

#sportaabe-overlay-96cb7974a9_mtg6mtm6ntu.sportaabe-overlay-context-iframe
  .sportaabe-checkout-message,
#sportaabe-overlay-96cb7974a9_mtg6mtm6ntu.sportaabe-overlay-context-iframe
  .sportaabe-checkout-continue {
  display: none;
}

#sportaabe-overlay-96cb7974a9_mtg6mtm6ntu
  .sportaabe-checkout-modal
  .sportaabe-checkout-continue {
  font-size: 15px;
  line-height: 1.35;
  padding: 10px 0;
  font-weight: bold;
}

#sportaabe-overlay-96cb7974a9_mtg6mtm6ntu
  .sportaabe-checkout-modal
  .sportaabe-checkout-continue
  a {
  border-bottom: 1px solid white;
}

#sportaabe-overlay-96cb7974a9_mtg6mtm6ntu .sportaabe-checkout-close {
  position: absolute;
  right: 16px;
  top: 16px;
  width: 16px;
  height: 16px;
  opacity: 0.6;
}

#sportaabe-overlay-96cb7974a9_mtg6mtm6ntu.sportaabe-overlay-loading
  .sportaabe-checkout-close {
  display: none;
}

#sportaabe-overlay-96cb7974a9_mtg6mtm6ntu .sportaabe-checkout-close:hover {
  opacity: 1;
}

#sportaabe-overlay-96cb7974a9_mtg6mtm6ntu .sportaabe-checkout-close:before,
.sportaabe-checkout-close:after {
  position: absolute;
  left: 8px;
  content: " ";
  height: 16px;
  width: 2px;
}

#sportaabe-overlay-96cb7974a9_mtg6mtm6ntu .sportaabe-checkout-close:before {
  transform: rotate(45deg);
}

#sportaabe-overlay-96cb7974a9_mtg6mtm6ntu .sportaabe-checkout-close:after {
  transform: rotate(-45deg);
}

#sportaabe-overlay-96cb7974a9_mtg6mtm6ntu .sportaabe-checkout-iframe-container {
  display: none;
}

#sportaabe-overlay-96cb7974a9_mtg6mtm6ntu.sportaabe-overlay-context-iframe
  .sportaabe-checkout-iframe-container,
#sportaabe-overlay-96cb7974a9_mtg6mtm6ntu.sportaabe-overlay-context-iframe
  .sportaabe-checkout-iframe-container
  > .outlet,
#sportaabe-overlay-96cb7974a9_mtg6mtm6ntu.sportaabe-overlay-context-iframe
  .sportaabe-checkout-iframe-container
  > .outlet
  > iframe {
  max-height: 95vh;
  max-width: 95vw;
}

#sportaabe-overlay-96cb7974a9_mtg6mtm6ntu.sportaabe-overlay-context-iframe
  .sportaabe-checkout-iframe-container {
  display: block;

  position: absolute;

  top: 50%;
  left: 50%;

  min-width: 450px;

  transform: translate(-50%, -50%);
  transform: translate3d(-50%, -50%, 0);

  border-radius: 10px;
  overflow: hidden;
}

#sportaabe-overlay-96cb7974a9_mtg6mtm6ntu.sportaabe-overlay-context-iframe
  .outlet {
  position: relative;

  transition: all 0.3s ease;
  animation-duration: 0.3s;
  animation-fill-mode: forwards !important;

  min-width: 450px;
  max-width: 450px;
  width: 450px;
  height: 535px;

  background-color: white;

  overflow: auto;

  opacity: 0;
  transform: scale3d(0.3, 0.3, 0.3);

  -webkit-overflow-scrolling: touch;
}

#sportaabe-overlay-96cb7974a9_mtg6mtm6ntu.sportaabe-overlay-context-iframe
  .outlet
  > iframe {
  position: absolute;
  top: 0;
  left: 0;
  transition: opacity 0.4s ease-in-out;
}

#sportaabe-overlay-96cb7974a9_mtg6mtm6ntu.sportaabe-overlay-context-iframe
  .outlet
  > iframe.component-frame {
  z-index: 100;
}

#sportaabe-overlay-96cb7974a9_mtg6mtm6ntu.sportaabe-overlay-context-iframe
  .outlet
  > iframe.prerender-frame {
  z-index: 200;
}

#sportaabe-overlay-96cb7974a9_mtg6mtm6ntu.sportaabe-overlay-context-iframe
  .outlet
  > iframe.visible {
  opacity: 1;
  z-index: 200;
}

#sportaabe-overlay-96cb7974a9_mtg6mtm6ntu.sportaabe-overlay-context-iframe
  .outlet
  > iframe.invisible {
  opacity: 0;
  z-index: 100;
}

@media screen and (max-width: 470px) {
  #sportaabe-overlay-96cb7974a9_mtg6mtm6ntu.sportaabe-overlay-context-iframe
    .sportaabe-checkout-iframe-container,
  #sportaabe-overlay-96cb7974a9_mtg6mtm6ntu.sportaabe-overlay-context-iframe
    .outlet {
    min-width: 100%;
    min-width: calc(100% - 20px);

    max-width: 100%;
    max-width: calc(100% - 20px);
  }
}

#sportaabe-overlay-96cb7974a9_mtg6mtm6ntu.sportaabe-overlay-context-iframe
  .outlet
  iframe {
  width: 1px;
  min-width: 100%;
  height: 100%;
}

@keyframes show-component {
  from {
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3);
  }

  to {
    opacity: 1;
    transform: scale3d(1, 1, 1);
  }
}

@keyframes hide-component {
  from {
    opacity: 1;
    transform: scale3d(1, 1, 1);
  }

  to {
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3);
  }
}

.sportaabe-spinner {
  height: 30px;
  width: 30px;
  display: inline-block;
  box-sizing: content-box;
  opacity: 1;
  filter: alpha(opacity=100);
  animation: rotation 0.7s infinite linear;
  border-left: 8px solid rgba(0, 0, 0, 0.2);
  border-right: 8px solid rgba(0, 0, 0, 0.2);
  border-bottom: 8px solid rgba(0, 0, 0, 0.2);
  border-top: 8px solid #fff;
  border-radius: 100%;
}

@keyframes rotation {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(359deg);
  }
}

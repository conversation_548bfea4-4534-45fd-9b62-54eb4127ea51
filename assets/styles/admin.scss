$fa-font-path: "~@fortawesome/fontawesome-free/webfonts";
$hamburger-padding-x: 0px !default;
$hamburger-padding-y: 0px !default;
$hamburger-layer-width: 25px !default;
$hamburger-layer-height: 2px !default;
$hamburger-layer-spacing: 5px !default;
$hamburger-layer-color: #9b9ea2 !default;
$hamburger-layer-border-radius: 4px !default;
$hamburger-hover-opacity: 0.7 !default;

@import "~bootstrap/scss/bootstrap";

@import "~@fortawesome/fontawesome-free/scss/fontawesome";
@import "~@fortawesome/fontawesome-free/scss/solid";
@import "~@fortawesome/fontawesome-free/scss/regular";
@import "~@fortawesome/fontawesome-free/scss/brands";

@import "./partials/variables";
@import "./partials/navbar";
@import "./partials/navbar.scss";
@import "./partials/sidebar";
@import "./partials/themes";

@import "./partials/dashboard";

.icon-style {
  width: 35px;
  height: 35px;
  text-align: center;
  line-height: 35px;
  border-radius: 50%;
  border: 1px solid;
  font-size: 0.9rem;
}

body {
  height: 100vh;
  margin: 0;
}

.admin-wrapper {
  height: 100%;

  main {
    display: flex;
    height: 100%;

    .pages {
      overflow: auto;
    }

    .overlay {
      position: fixed;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: 100;
      opacity: 0.7;
      display: none;

      &.show {
        display: block;

        @media (min-width: 768px) {
          display: none;
        }
      }
    }
  }

  &.toggeled-sidebar {
    main {
      .overlay {
        display: block;

        @media (min-width: 768px) {
          display: none;
        }
      }
    }
  }
}

.toast {
  opacity: 1;
}

.admin-wrapper.default-theme {
  background: #f2f3f8;
}
td {
  border: none;
}
.admin-wrapper.default-theme .table th,
.admin-wrapper.default-theme .table td {
  border-top: 1px solid #ebedf4;
}

.fa-check,
.fa-times {
  cursor: pointer !important;
}

@import "./_vars.scss";
@import "./_reset.scss";
@import "./_text.scss";
@import "./_utils.scss";
@import "./_table.scss";
@import "./_main.scss";
@import "./_overlay.scss";

body {
  //background-color: #f5f5f5;
  background: linear-gradient(
    90deg
    , rgba(255,0,255,1) 0% , rgba(253,240,1,1) 100%);
}

header:before {
  content: "";
  background-color: #27848E;
  height: 70px;
  width: 100%;
  text-align: center;
  position: fixed;
  top: 0;
  z-index: 100;
  box-shadow: 0 1px 3px rgba(0, 0, 0,  .12), 0 1px 2px rgba(0, 0, 0, .24);
}

header:before {
  background: linear-gradient(
90deg
, rgba(253,240,1,1) 0%, rgba(255,0,255,1) 100%);
}

header #logo {
  position: fixed;
  top: 35px;
  right: 40px;
  z-index: 102;
  transform: translateY(-50%);
}

header #logo img {
  background-color: transparent;
  height: 45px;
}

.wrapper {
  margin-top: 20px;
}

.wrapword {
  white-space: -moz-pre-wrap !important;  /* Mozilla, since 1999 */
  white-space: -pre-wrap;      /* Opera 4-6 */
  white-space: -o-pre-wrap;    /* Opera 7 */
  white-space: pre-wrap;       /* css-3 */
  word-wrap: break-word;       /* Internet Explorer 5.5+ */
  white-space: -webkit-pre-wrap; /* Newer versions of Chrome/Safari*/
  word-break: break-all;
  white-space: normal;
}

@media (max-width: 768px) {
  .wrapper {
    width: 95% !important;
  }  
}
declare var $: any;
export const countDown = function () {
  const domContainer = document.getElementById("countDown");
  if (domContainer) {
    toggleAuthLinks();
    const countDownDate = new Date("Feb 10, 2021 00:00:00").getTime();
    const x = setInterval(function () {
      const now = new Date().getTime();
      const distance = countDownDate - now;
      const days = Math.floor(distance / (1000 * 60 * 60 * 24));
      const hours = Math.floor(
        (distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
      );
      const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((distance % (1000 * 60)) / 1000);
      domContainer.innerHTML =
        days + "d " + hours + "h " + minutes + "m " + seconds + "s ";
      if (distance < 0) {
        clearInterval(x);
        domContainer.innerHTML = "EXPIRED";
      }
    }, 1000);
  }
};

function toggleAuthLinks() {
  const isLoggedIn = sessionStorage.getItem("jwttoken");
  if (isLoggedIn) {
    $("#login-link").addClass("d-none");
    $("#logout-link").removeClass("d-none");
    $("#logout-link").on("click", function (e) {
      e.preventDefault();
      logout();
    });
  } else {
    $("#login-link").removeClass("d-none");
    $("#logout-link").addClass("d-none");
  }
}

function logout() {
  sessionStorage.removeItem("jwttoken");
  window.location.assign("/auth/login");
}

import React, { Component } from "react";
import { RouteComponentProps } from "react-router-dom";
import {
  PayBtn,
  PayerInfos,
  PayHeader,
  PayMethod,
  PayStatus,
} from "../_components";
import { utils } from "../_helpers";
import { PaymentState } from "../_interfaces";
import { PaymentModel } from "../_models";
import { connector, PropsFromRedux } from "../_reducers";

class Payment extends Component<
  PropsFromRedux & RouteComponentProps,
  PaymentState
> {
  constructor(props: any) {
    super(props);
    this.state = {
      payModel: this.getPaymodel(),
    };
    this.handleSubmit = this.handleSubmit.bind(this);
  }

  getPaymodel() {
    const token = (this.props.match.params as any).token;
    return PaymentModel.getInstance({
      token,
      freeze: false,
      component: this,
    });
  }

  componentDidMount() {
    utils().changePageTitle("Paiements");
    this.state.payModel.initPaymentRequest().then(() => {
      this.refresh();
    });
  }

  refresh(newState?: PaymentState) {
    this.setState(newState ? { ...newState } : {}, () => {
      this.state.payModel.component = this;
    });
  }

  handleSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();
    this.state.payModel.submitPayment();
  }

  render() {
    const {
      payModel: { freeze, step, paymentRequest },
    } = this.state;
    return (
      <div className="wrapper box-shadow rounded">
        <form
          method="POST"
          onSubmit={this.handleSubmit}
          className={freeze ? "freeze" : ""}
        >
          <PayHeader {...this.state} />
          {step === 2 && <PayStatus {...this.state} />}
          {step === 1 && (
            <>
              <PayerInfos paymentRequest={paymentRequest} />
              <PayMethod {...this.state} />
              <PayBtn {...this.state} />
            </>
          )}
        </form>
      </div>
    );
  }
}

export default connector<any>(Payment);

import { SubmitBtnProps } from "../../_interfaces";

export function SubmitBtn(props: SubmitBtnProps) {
  const { freeze, label, freezeLabel } = props;
  const attr: any = {
    type: "submit",
    className: "w-100",
  };
  if (freeze) {
    attr.disabled = true;
    attr.style = { background: "#ccc", cursor: "progress" };
  }
  return (
    <div className="input-group">
      <div className="input-box">
        <button {...attr}>
          {freeze &&
            (freezeLabel || "Traitement en cours. Merci de patienter ... ")}
          {!freeze && label}
        </button>
      </div>
    </div>
  );
}

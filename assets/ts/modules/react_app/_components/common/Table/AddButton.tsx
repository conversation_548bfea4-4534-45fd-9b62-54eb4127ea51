import { history } from "../../../_helpers";

export function AddButton({ title, path }) {
  const onClick = () => {
    history.push(path);
  };

  return (
    <button
      type="button"
      onClick={onClick}
      className="btn btn-primary btn-sm d-flex actionBtn align-items-center py-1 box-shadow"
      style={{ float: "right" }}
    >
      <i className="fa fa-plus mr-1"></i>
      <p
        className="d-flex align-items-center"
        style={{
          textTransform: "none",
          float: "right",
          margin: 0,
        }}
      >
        <small style={{ fontWeight: 600 }}>{title}</small>
      </p>
    </button>
  );
}

import React, { useEffect } from "react";
import ReactD<PERSON>, { render } from "react-dom";
import { appConstants } from "../../../_constants";
import { history, utils } from "../../../_helpers";
import { BaseModel } from "../../../_models";
import { TableLoader } from "./TableLoader";
const { FULL_LOADING_ACTION } = appConstants.keys.eventKeys;

declare var $: any;

export function TableContent(props: any) {
  const { tabRef, searchRef, retrieveTable } = props;
  const id = props?.id || "admin_table";
  const styles = { id };

  console.log(props);
  useEffect(() => {
    const datatable = createDataTable(props, tabRef);
    $(searchRef.current).on("keyup", (e) => {
      datatable.search(e.target.value).draw();
    });
    console.log("searchRef ", searchRef);
    return () => {
      if (!retrieveTable) {
        destroyDataTable(datatable, tabRef);
      }
    };
  });
  return (
    <table
      {...styles}
      ref={tabRef}
      className="table table-hover text-center datatable"
    />
  );
}

const createDataTable = (props: any, ref: any) => {
  const { columns } = props;
  let retrieveTable = false;
  if (!($ as any).fn.DataTable.isDataTable($(ref.current) as any)) {
    retrieveTable = true;
  }
  let tab = ($(ref.current) as any).DataTable({
    columns,
    ...getOptions(props),
  });
  refreshDataTableData(tab, props);
  if (retrieveTable) {
    appendLoader(props);
  }
  return tab;
};

const destroyDataTable = (datatable: any, ref: any) => {
  const lg = document.getElementById("loader-group");
  if (lg) {
    ReactDOM.unmountComponentAtNode(lg);
  }
  datatable.clear();
  datatable.destroy();
  $(ref.current).empty();
};

const refreshDataTableData = (datatable: any, props: any) => {
  const { data } = props;
  if (!datatable || !data) {
    return;
  }
  datatable.clear();
  datatable.rows.add(data);
  datatable.draw();
};

const appendLoader = (props: any) => {
  const id = props?.id || "admin_table";
  const filter = $(`#${id}_filter`);
  if (filter) {
    filter.parent().prepend($("<div id='loader-group'></div>"));
    render(<TableLoader {...props} />, document.getElementById("loader-group"));
  }
};

const getOptions = (props: any) => {
  const { retrieveTable, pageLength } = props;
  const opts = {
    pageLength: pageLength || 10,
    ordering: false,
    retrieve: typeof retrieveTable !== "undefined" ? retrieveTable : false,
    lengthMenu: [
      [10, 25, 50, 100, -1],
      [10, 25, 50, 100, "All"],
    ],
    language: {
      lengthMenu: "Afficher _MENU_",
      zeroRecords: "Tableau vide",
      info: "Page _PAGE_ sur _PAGES_",
      infoEmpty: "Aucune donnée disponible.",
      infoFiltered: "(filtrage de _MAX_ enregistrements.)",
      sSearch: "Rechercher",
      oPaginate: {
        sFirst: "Premier",
        sPrevious: "Précédent",
        sNext: "Suivant",
        sLast: "Dernier",
      },
    },
    createdRow: (row: any, data: any) => {
      $(row).on("click", ".actionBtn", (e: any) => {
        e.preventDefault();
        const path = $(e.currentTarget).data("path");
        const action = $(e.currentTarget).data("action");
        if (action === "remove") {
          const r = window.confirm(
            "Voulez-vous vraiment supprimer cet élément ? Cette action est irreversible !"
          );
          if (r) {
            if (data?.removeElement) {
              utils().emitter.emit(FULL_LOADING_ACTION, true);
              data?.removeElement().then((r: any) => {
                if (r) {
                  window.location.reload();
                } else {
                  utils().emitter.emit(FULL_LOADING_ACTION, false);
                  window.alert(
                    "Erreur lors de la suppression. Merci de reéssayer plus tard !"
                  );
                }
              });
            }
          }
        } else if (action === "activate") {
          if (data?.switchStatus) {
            utils().emitter.emit(FULL_LOADING_ACTION, true);
            data?.switchStatus().then((r: any) => {
              if (r) {
                window.location.reload();
              } else {
                utils().emitter.emit(FULL_LOADING_ACTION, false);
                window.alert(
                  "Erreur lors de l'activation. Merci de reéssayer plus tard !"
                );
              }
            });
          }
        } else if (action === "deactivate") {
          const r = window.confirm(
            "Voulez-vous vraiment désactiver cet élément ?"
          );
          if (r) {
            if (data?.switchStatus) {
              utils().emitter.emit(FULL_LOADING_ACTION, true);
              data?.switchStatus().then((r: any) => {
                if (r) {
                  window.location.reload();
                } else {
                  utils().emitter.emit(FULL_LOADING_ACTION, false);
                  window.alert(
                    "Erreur lors de la désactivation. Merci de reéssayer plus tard !"
                  );
                }
              });
            }
          }
        } else {
          if (path) {
            const prefix = BaseModel.getInstance().userPath;
            history.push(`${prefix}/${path}`);
          }
        }
      });
    },
  };
  const columns = props.columns.map((item: any, i: number) => i);
  columns.pop();
  Object.assign(opts, {
    dom: "Bfrtip",
    buttons: [
      {
        extend: "pdf",
        text: "Télécharger en PDF",
        className: "px-2 py-2 mb-3 rounded ",
        footer: false,
        exportOptions: { columns },
      },
    ],
  });
  return opts;
};

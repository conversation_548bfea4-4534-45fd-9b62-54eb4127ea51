import React, { useEffect, useRef, useState } from "react";
import { appConstants } from "../../../_constants";
import { restClient, utils } from "../../../_helpers";
import { TableContent } from "./TableContent";

const {
  TABLE_DATA_CHANGE,
  TABLE_DATA_FILTER,
  TABLE_DATA_LOADING,
  TABLE_DATA_ERROR,
} = appConstants.keys.eventKeys;

export function Table(props: any) {
  const { searchRef, settings } = props;
  const ref = useRef(null);
  const loaderRef = useRef(null);
  const errorRef = useRef(null);

  const [config, setConfig] = useState(settings);

  const onRetry = () => {
    if (errorRef.current) {
      $(errorRef.current as any).css("display", "none");
    }
    if (loaderRef.current) {
      $(loaderRef.current as any).css("display", "flex");
    }
  };

  useEffect(() => {
    const handleTableDataChangeEvent = (res: any) => {
      const data =
        res?.data && settings?.dataPathCallback
          ? settings?.dataPathCallback(res?.data)
          : res?.data;
      setConfig({ ...settings, ...res, data, loading: false });
      if (!Array.isArray(res?.data)) {
        utils().emitter.emit(TABLE_DATA_ERROR, true);
      }
      utils().emitter.emit(TABLE_DATA_LOADING, false);
    };

    const handleTableDataFilterEvent = (res: any) => {
      setConfig({ settings, ...res, loading: true });
    };

    const handleTableDataLoadingEvent = (res: any) => {
      setTimeout(() => {
        if (loaderRef.current) {
          $(loaderRef.current as any).css("display", res ? "flex" : "none");
        }
      }, 400);
    };

    const handleTableDataErrorEvent = (res: any) => {
      setTimeout(() => {
        if (errorRef.current) {
          $(errorRef.current as any).css("display", res ? "flex" : "none");
        }
      }, 400);
    };

    utils().emitter.on(TABLE_DATA_LOADING, (res) => {
      handleTableDataLoadingEvent(res);
    });
    utils().emitter.on(TABLE_DATA_ERROR, (res) => {
      handleTableDataErrorEvent(res);
    });
    utils().emitter.on(TABLE_DATA_FILTER, (res) => {
      handleTableDataFilterEvent(res);
    });
    utils().emitter.on(TABLE_DATA_CHANGE, (res) => {
      handleTableDataChangeEvent(res);
    });
    if (config.dataPath) {
      restClient.get(config.dataPath).then((res) => {
        utils().emitter.emit(TABLE_DATA_CHANGE, res);
      });
    } else {
      utils().emitter.emit(TABLE_DATA_CHANGE, null);
    }

    return () => {
      utils().emitter.off(TABLE_DATA_CHANGE, handleTableDataChangeEvent);
      utils().emitter.off(TABLE_DATA_FILTER, handleTableDataChangeEvent);
      utils().emitter.off(TABLE_DATA_LOADING, handleTableDataLoadingEvent);
      utils().emitter.off(TABLE_DATA_ERROR, handleTableDataErrorEvent);
    };
  }, []);

  return (
    <>
      {!config && <div />}
      {config && (
        <TableContent
          {...props}
          tabRef={ref}
          searchRef={searchRef}
          loaderRef={loaderRef}
          errorRef={errorRef}
          {...config}
          onRetry={onRetry}
        />
      )}
    </>
  );
}

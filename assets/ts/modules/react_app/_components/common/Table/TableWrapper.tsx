import React, { useRef } from "react";
import { Table } from "./Table";
import { AddButton } from "./AddButton";

export function TableWrapper({ settings }) {
  const { addPath, addTitle, searchPlaceHolder } = settings;
  const showAdd = addPath && addTitle;
  const searchRef = useRef(null);
  return (
    <div className="row mb-4" style={{ minHeight: "80vh" }}>
      <div className="col-md-12">
        <div className="card border-0 rounded-0">
          <div className="card-title mb-1 p-3 d-flex flex-row justify-content-between">
            <h5>{settings.listTitle}</h5>
            {showAdd && <AddButton title={addTitle} path={addPath} />}
          </div>
          <div
            id="search_group"
            className="w-100 pb-1"
            style={{ borderBottom: "1px solid #f2f3f8" }}
          >
            <div
              className="flex-column align-items-center"
              style={{ margin: "0px 10px" }}
            >
              <div className="row">
                <div className="col-md-12">
                  <input
                    type="text"
                    ref={searchRef}
                    name="search_input"
                    id="search_input"
                    className="form-control"
                    placeholder={searchPlaceHolder || "Rechercher ..."}
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="card-body">
            <div className="table-responsive-md">
              <Table settings={settings} searchRef={searchRef} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export function Details({ data }) {
  return (
    <table className="table table-striped table-fixed">
      {data.map((item: any, i: number) => {
        const datas: any[] = [];
        if (item.title) {
          datas.push(<Header title={item.title} key={"h" + i} />);
        }
        datas.push(<Body rows={item.rows} key={"b" + i} />);
        return datas;
      })}
    </table>
  );
}

export function Header(props: any) {
  return (
    <thead className="thead-dark">
      <tr>
        <th scope="col" colSpan={2} style={{ padding: "0.4rem" }}>
          <h6 className="text-center" style={{ margin: 0 }}>
            {props.title}
          </h6>
        </th>
      </tr>
    </thead>
  );
}

export function Body(props: any) {
  return (
    <tbody>
      {props.rows.map((row, j) => {
        return <Row label={row.label} value={row.value} key={j} />;
      })}
    </tbody>
  );
}

export function Row(props: any) {
  if (props.label === "img") {
    return (
      <tr className="detail-row">
        <td colSpan={2} className="value-detail-row">
          <img
            src={"data:image/png;base64," + props.value}
            alt="IMG"
            style={{ maxWidth: "538px", margin: "auto" }}
          />
        </td>
      </tr>
    );
  }
  return props.label !== false ? (
    <tr className="detail-row">
      <td className="label-detail-row">{props.label}</td>
      <td
        className="value-detail-row"
        dangerouslySetInnerHTML={{ __html: props.value }}
      ></td>
    </tr>
  ) : (
    <tr className="detail-row">
      <td colSpan={2} className="value-detail-row">
        {props.value}
      </td>
    </tr>
  );
}

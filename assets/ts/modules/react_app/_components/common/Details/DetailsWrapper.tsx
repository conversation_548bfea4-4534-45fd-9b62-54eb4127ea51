import { useEffect, useState } from "react";
import { restClient, history } from "../../../_helpers";
import { Details } from "./Details";

export function DetailsWrapper(props: any) {
  const {
    renderFooter,
    settings: { dataPath, toView },
  } = props;
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState([]);

  useEffect(() => {
    if (props.settings?.data) {
      setData(props.settings?.data);
      setLoading(false);
    } else {
      restClient.get(dataPath).then((res) => {
        if (res?.data?.id) {
          setData(toView(res.data));
        }
        setLoading(false);
      });
    }
  }, [dataPath]);

  return (
    <div className="row mb-4" style={{ minHeight: "80vh" }}>
      <div className="col-md-12">
        <div className="card border-0 rounded-0">
          <div className="card-title mb-1 p-3 d-flex justify-content-between">
            {loading && <h5>Chargement en cours. Merci de patienter ...</h5>}
            {!loading && <h5>{props.settings.listTitle}</h5>}
            <span
              className="btn btn-sm btn-info box-shadow"
              onClick={() => history.goBack()}
            >
              <small>
                <i className="fa fa-reply mr-1" aria-hidden="true"></i>
                Précédent
              </small>
            </span>
          </div>
          <div className="card-body">
            <div className="table-responsive-md">
              {!loading && <Details data={data} />}
            </div>
          </div>
          {renderFooter && renderFooter({ data, loading })}
        </div>
      </div>
    </div>
  );
}

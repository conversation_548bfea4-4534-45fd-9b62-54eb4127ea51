import { useState } from "react";
import { InputBoxProps } from "../../../_interfaces";

export function InputBox(props: InputBoxProps) {
  const { icon, initialValue, onChange, inputProps } = props;
  const [value, setValue] = useState(initialValue || "");

  const onInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { type, checked, value } = e.target;
    const val = type === "checkbox" ? checked : value;
    setValue(val);
    if (onChange) {
      onChange(val);
    }
  };

  return (
    <div className="input-box">
      <input
        {...inputProps}
        className="name"
        value={value}
        onChange={onInputChange}
      />
      <i className={`${icon} icon`}></i>
    </div>
  );
}

import { InputGroupProps } from "../../../_interfaces";
import { InputGroup } from "../../common";

interface InputGroupWrapperProps {
  title?: string;
  rows: InputGroupProps[];
}

export function InputGroupWrapper(props: InputGroupWrapperProps) {
  const { rows, title } = props;

  return (
    <>
      <h4 className="text-center">{title || ""}</h4>
      {rows.map((item, i) => (
        <InputGroup {...item} key={i} />
      ))}
    </>
  );
}

export function PayoutForm(props: any) {
  const { tel, amount, setTel, setAmount, loading } = props;

  return (
    <>
      <div className="input-group">
        <div className="input-box">
          <input
            placeholder="Téléphone de paiement"
            type="tel"
            name="tel"
            className="name"
            value={tel}
            disabled={loading}
            onChange={(e) => setTel(e.target.value)}
          />
          <i className="fa fa-phone icon"></i>
        </div>
      </div>
      <div className="input-group">
        <div className="input-box">
          <input
            placeholder="Montant à retirer"
            type="number"
            name="amount"
            className="name"
            value={amount}
            disabled={loading}
            onChange={(e) => setAmount(e.target.value)}
          />
          <i className="fas fa-money icon"></i>
        </div>
      </div>
    </>
  );
}

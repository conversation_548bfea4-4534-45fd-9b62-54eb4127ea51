import { history } from "../../../_helpers";
import { SubmitBtn } from "../SubmitBtn";

export function FormWrapper(props: any) {
  const { renderForm, onFormSubmit, loading, error } = props;

  const onSubmit = (e: any) => {
    e.preventDefault();
    if (onFormSubmit) {
      onFormSubmit();
    }
  };

  return (
    <div className="row mb-4" style={{ minHeight: "80vh" }}>
      <div className="col-md-12">
        <div className="card border-0 rounded-0">
          <div className="card-title mb-1 p-3 d-flex justify-content-between">
            {loading && <h5>Chargement en cours. Merci de patienter ...</h5>}
            {!loading && <h5>{props.settings.listTitle}</h5>}
            <span
              className="btn btn-sm btn-info"
              onClick={() => history.goBack()}
            >
              <small>
                <i className="fa fa-reply mr-1" aria-hidden="true"></i>{" "}
                Précédent
              </small>
            </span>
          </div>
          <div className="card-body">
            <div className="table-responsive-md">
              {!loading && renderForm && (
                <form onSubmit={onSubmit}>
                  {renderForm({ loading })}
                  {!loading && error?.length > 0 && (
                    <small className="text-danger d-block py-2">{error}</small>
                  )}
                  <div style={{ maxWidth: "200px" }}>
                    <SubmitBtn label={"Enregistrer"} freeze={false} />
                  </div>
                </form>
              )}
              {!loading && !renderForm && (
                <span className="text-danger">Erreur lors du chargement !</span>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

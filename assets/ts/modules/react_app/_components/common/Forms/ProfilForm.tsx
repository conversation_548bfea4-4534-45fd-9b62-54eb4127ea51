export function ProfilForm(props: any) {
  const { profil, setProfil, loading } = props;

  return (
    <>
      <div className="row">
        <div className="col-md-6">
          <div className="input-group">
            <div className="input-box">
              <input
                placeholder="Noms"
                type="text"
                name="firstName"
                className="name"
                value={profil.firstName || ""}
                disabled={loading}
                onChange={(e) => {
                  profil.firstName = e.target.value;
                  setProfil(profil.clone());
                }}
              />
              <i className="fa fa-user icon"></i>
            </div>
          </div>
        </div>
        <div className="col-md-6">
          <div className="input-group ">
            <div className="input-box">
              <input
                placeholder="Prénoms"
                type="text"
                name="lastName"
                className="name"
                value={profil.lastName || ""}
                disabled={loading}
                onChange={(e) => {
                  profil.lastName = e.target.value;
                  setProfil(profil.clone());
                }}
              />
              <i className="fa fa-user icon"></i>
            </div>
          </div>
        </div>
      </div>
      <div className="row">
        <div className="col-md-6">
          <div className="input-group">
            <div className="input-box">
              <input
                placeholder="Téléphone"
                type="tel"
                name="phoneNumber"
                className="name"
                value={profil.phoneNumber || ""}
                disabled={loading}
                onChange={(e) => {
                  profil.phoneNumber = e.target.value;
                  setProfil(profil.clone());
                }}
              />
              <i className="fa fa-phone icon"></i>
            </div>
          </div>
        </div>
        <div className="col-md-6">
          <div className="input-group">
            <div className="input-box">
              <input
                placeholder="Email"
                type="email"
                name="email"
                className="name"
                value={profil.email || ""}
                disabled={loading}
                onChange={(e) => {
                  profil.email = e.target.value;
                  setProfil(profil.clone());
                }}
              />
              <i className="fa fa-envelope icon"></i>
            </div>
          </div>
        </div>
      </div>
      <div className="row">
        <div className="col-md-12">
          <div className="input-group">
            <div className="input-box">
              <input
                placeholder="Nom d'utilisateur"
                type="tel"
                name="username"
                className="name"
                value={profil.username || ""}
                disabled={loading}
                onChange={(e) => {
                  profil.username = e.target.value;
                  setProfil(profil.clone());
                }}
              />
              <i className="fa fa-user icon"></i>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

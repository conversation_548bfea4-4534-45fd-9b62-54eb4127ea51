import React from "react";

const colors = {
  0: "#ffc107",
  1: "#1e7e34",
  14: "#1e7e34",
  2: "#dc3545",
  3: "badge-success",
  4: "badge-warning",
  5: "badge-info",
  6: "badge-success",
};
export function RenderAmountTransaction(props: any) {
  const { data, id } = props;

  const color = (colors as any)[id] || "#333";
  return <span style={{ margin: 0, color, fontWeight: "bold" }}>{data}</span>;
}

import React from "react";

const statuses = {
  WITHDRAWN: "<PERSON><PERSON><PERSON>",
  WAITING_WITHDRAW: "En attente de retrait",
  PENDING: "En cours",
  SUCCESS: "Succ<PERSON>",
  TRAITÉ: "Traité",
  FAILED: "<PERSON><PERSON><PERSON>",
};
const colors = {
  WITHDRAWN: "dark",
  WAITING_WITHDRAW: "secondary",
  PENDING: "warning",
  SUCCESS: "success",
  TRAITÉ: "info",
  FAILED: "danger",
};

export function RenderStatusOperation(props: any) {
  const { data } = props;
  const status = (statuses as any)[data] || data;
  const color = (colors as any)[data] || data;
  return <span className={`text-${color}`}>{status}</span>;
}

import { BaseModel } from "../../_models";

export const RenderActionPayment = ({ data, path }) => {
  if (BaseModel.getInstance().user.isSuperAdminOrAdmin) {
    return (
      <>
        <a
          className="btn btn-sm btn-outline-lightning pointer rounded-0 mr-2 actionBtn"
          href={path}
          data-id={data}
          data-path={path}
          data-action={"show"}
        >
          <i className="fa fa-eye"></i>
        </a>
        <a
          className="btn btn-sm btn-outline-lightning  pointer rounded-0 actionBtn"
          data-id={data}
          data-path={path}
          data-action={"remove"}
        >
          <i className="far fa-trash-alt"></i>
        </a>
      </>
    );
  }
  return (
    <a
      className="btn btn-sm btn-outline-lightning pointer rounded-0 mr-2 actionBtn"
      href={path}
      data-id={data}
      data-path={path}
      data-action={"show"}
    >
      <i className="fa fa-eye"></i>
    </a>
  );
};

export const RenderActionAll = ({ data, path }) => {
  return (
    <>
      <a
        className="btn btn-sm btn-outline-lightning pointer rounded-0 mr-2 actionBtn"
        data-id={data}
        data-path={path}
        data-action={"show"}
      >
        <i className="fa fa-eye"></i>
      </a>
      <a
        className="btn btn-sm btn-outline-lightning  pointer rounded-0  mr-2 actionBtn"
        data-id={data}
        data-path={path}
        data-action={"edit"}
      >
        <i className="fa fa-edit"></i>
      </a>
      <a
        className="btn btn-sm btn-outline-lightning  pointer rounded-0 actionBtn"
        data-id={data}
        data-action={"remove"}
      >
        <i className="far fa-trash-alt"></i>
      </a>
    </>
  );
};

export const RenderDetailOnly = ({ data, path }) => {
  return (
    <>
      <a
        className="btn btn-sm btn-outline-lightning pointer rounded-0 mr-2 actionBtn"
        href={path}
        data-id={data}
        data-path={path}
        data-action={"show"}
      >
        <i className="fa fa-eye"></i>
      </a>
    </>
  );
};

export const RenderEditOnly = ({ data, path }) => {
  return (
    <>
      <a
        className="btn btn-sm btn-outline-lightning pointer rounded-0 mr-2 actionBtn"
        href={path}
        data-id={data}
        data-path={path}
        data-action={"edit"}
      >
        <i className="fa fa-edit"></i>
      </a>
    </>
  );
};

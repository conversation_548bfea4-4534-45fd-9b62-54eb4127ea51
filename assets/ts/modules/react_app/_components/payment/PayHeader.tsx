import { PaymentState } from "../../_interfaces";
import { PayOverlay } from "./PayOverlay";
import { Header } from "../common/Header";

export function PayHeader(props: PaymentState) {
  const {
    popupWindow,
    payModel: { fatalError, step },
  } = props;
  return (
    <>
      <Header />
      {step === 0 && (
        <p className={`text-center ${fatalError ? "text-danger" : ""}`}>
          {!fatalError && "Initialisation en cours ..."}
          {fatalError && fatalError}
        </p>
      )}
      {popupWindow && <PayOverlay {...props} />}
    </>
  );
}

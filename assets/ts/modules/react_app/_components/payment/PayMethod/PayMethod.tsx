import { PaymentState } from "../../../_interfaces";
import { PayMethodDetails } from "./PayMethodDetails";
import { PayMethodItem } from "./PayMethodItem";

export function PayMethod(props: PaymentState) {
  const { payModel } = props;
  const { payMethodItems, selectedPayMethodItem, freeze, payError } = payModel;

  const onClick = (methodId: string) => {
    payModel.changeSelectedPayMethodItemId(methodId);
  };

  return (
    <>
      <div className="input-group">
        <div className="input-box">
          <h4 className="text-center">Methode de paiement</h4>
          {payMethodItems.map((item, i) => (
            <PayMethodItem
              {...item}
              key={i}
              onClick={() => onClick(item.methodId)}
            />
          ))}
        </div>
      </div>
      {!freeze && payError && (
        <p className="text-center mb-3">
          <small className="text-danger">{payError}</small>
        </p>
      )}
      {selectedPayMethodItem && <PayMethodDetails {...selectedPayMethodItem} />}
    </>
  );
}

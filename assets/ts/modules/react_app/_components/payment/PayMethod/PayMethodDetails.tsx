import components from ".";
import { ErrorContentProps, PayMethodItemProps } from "../../../_interfaces";
import { ErrorContent } from "../../common";

export function PayMethodDetails(
  props: PayMethodItemProps & ErrorContentProps
) {
  let Component: React.FC<PayMethodItemProps & ErrorContentProps> =
    components[props.detailedContent];
  const isError = !Component;
  const message = {
    message: isError ? undefined : undefined,
  };
  if (isError) {
    Component = ErrorContent;
  }
  return <Component {...props} {...message} />;
}

import { PayMethodItemProps } from "../../../_interfaces";

export function PayMethodItem(props: PayMethodItemProps) {
  const { inputProps, onClick, checked, label, labelProps } = props;

  return (
    <>
      <input
        type="radio"
        name="pay"
        className="radio"
        {...inputProps}
        defaultChecked={checked}
      />
      <label
        {...labelProps}
        onClick={() => {
          if (!inputProps.disabled) {
            onClick();
          }
        }}
      >
        <span>{label}</span>
      </label>
    </>
  );
}

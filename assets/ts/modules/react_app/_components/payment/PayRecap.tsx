import { PaymentState } from "../../_interfaces";

export function PayRecap(props: PaymentState) {
  const { payModel } = props;
  const {
    paymentRequest: {
      financialTransactionId,
        formattedAmount,
        paymentMethodLibelle,
    },
  } = payModel;
  return (
    <div className="table-responsive py-1">
      <table className="text-center table-fixed w-100">
        <tbody>
          <tr>
            <td>Reférence transactionnel</td>
            <td>{financialTransactionId}</td>
          </tr>
          <tr>
            <td>Opérateur</td>
            <td>{paymentMethodLibelle}</td>
          </tr>
          <tr>
            <td>Montant</td>
            <td>{formattedAmount}</td>
          </tr>
        </tbody>
      </table>
    </div>
  );
}

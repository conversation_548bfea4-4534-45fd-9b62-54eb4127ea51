import { utils } from "../../../_helpers";
import { PanierItemProps } from "../../../_interfaces";

export function PanierItem(props: PanierItemProps) {
  const {
    isTotal,
    item: { libelle, price },
  } = props;
  return (
    <>
      {isTotal && <hr />}
      <div className="input-group">
        <div className="input-box">
          <h4>{libelle}</h4>
        </div>
        <div className="input-box">
          <span>{utils().formatAmount("FCFA", price)}</span>
        </div>
      </div>
    </>
  );
}

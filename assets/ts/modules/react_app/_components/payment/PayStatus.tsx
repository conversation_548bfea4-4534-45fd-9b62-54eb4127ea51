import { PaymentState } from "../../_interfaces";
import { PayRecap } from "./PayRecap";

import useInterval from "react-useinterval";

export function PayStatus(props: PaymentState) {
  const { payModel } = props;
  const {
    statusCallout,
    initialTimer,
    statusLabel,
    statusSubLabel,
    statusSubLabelClass,
    statusLabelClass,
    isPaymentSuccess,
    paymentRequest,
  } = payModel;
  useInterval(() => payModel.checkPaymentStatus(), initialTimer);
  return (
    <>
      <div className="input-group">
        <div className="input-box">
          <h4 className={`text-center ${statusLabelClass}`}>{statusLabel}</h4>
          {statusCallout.length > 0 && (
            <p className="text-center">
              {statusCallout}
              {paymentRequest.paymentMethod === "orange" &&
                paymentRequest.orangePayUrl && (
                  <small
                    className={"text-primary pointer text-underline d-block"}
                    style={{ marginBottom: "5px", marginTop: "5px" }}
                    onClick={() => payModel.launchOrangeMoneyPopup()}
                  >
                    Ouvrir la fenêtre Orange Money
                  </small>
                )}
            </p>
          )}
          {isPaymentSuccess && <PayRecap {...props} />}
          <small className={`text-center ${statusSubLabelClass} d-block mt-1"`}>
            {statusSubLabel}
          </small>
        </div>
      </div>
      {/* <div className="input-group">
        <div className="input-box">
          <button className="w-100" onClick={(e) => onClick(e, returnUrl)}>
            <i className="fa fa-arrow-left mr-1" aria-hidden="true"></i>
            Retour au site marchand
          </button>
        </div>
      </div> */}
    </>
  );
}

const onClick = (
  e: React.MouseEvent<HTMLButtonElement, MouseEvent>,
  returnUrl: string
) => {
  e.preventDefault();
  window.location.assign(returnUrl);
};

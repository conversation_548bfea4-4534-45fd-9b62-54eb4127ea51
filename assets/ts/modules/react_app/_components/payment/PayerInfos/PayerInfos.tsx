import { utils } from "../../../_helpers";

export function PayerInfos(props: any) {
  const { rows, paymentRequest } = props;
  const data = { rows, title: "Informations sur le paiement" };
  return (
    <>
     <h4 className="text-center">{data.title}</h4>
    <div className="table-responsive py-1">
      <table className="text-center table-fixed w-100">
        <tbody>
          <tr>
            <td><b>Order Id</b></td>
          </tr>
          <tr>            
            <td className="wrapword">{paymentRequest.orderId}</td>
          </tr>
          <tr>
            <td><b>Status</b></td>
          </tr>
          <tr>
            <td>{paymentRequest.status}</td>
          </tr>
          <tr>
            <td><b>Montant</b></td>
          </tr>
          <tr>
            <td>{utils().formatAmount("FCFA", paymentRequest.amount)}</td>
          </tr>
        </tbody>
      </table>
    </div>
    </>
  );
}

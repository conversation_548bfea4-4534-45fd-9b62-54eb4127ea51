import useInterval from "react-useinterval";
import { PaymentState } from "../../_interfaces";

export function PayOverlay(props: PaymentState) {
  const { popupWindow, payModel } = props;

  const onClick = (e: any) => {
    e.preventDefault();
    popupWindow?.focus();
  };

  const onClose = (e: any) => {
    e.preventDefault();
    popupWindow?.close();
  };

  useInterval(() => {
    if (popupWindow?.closed) {
      payModel.rebuildView({ popupWindow: null, payModel });
    }
  }, 1000);
  return (
    <div
      id="sportaabe-overlay-96cb7974a9_mtg6mtm6ntu"
      className="sportaabe-overlay-context-popup sportaabe-checkout-overlay"
    >
      <a
        href="#"
        className="sportaabe-checkout-close"
        aria-label="close"
        role="button"
        onClick={onClose}
      ></a>
      <div className="sportaabe-checkout-modal">
        <div className="sportaabe-checkout-logo">
          <h3>sportaabe Mobile Pay</h3>
        </div>
        <div className="sportaabe-checkout-message">
          Vous ne voyez pas la fenetre de paiement? Nous allons le relancer pour
          vous permettre de finaliser votre paiement.
        </div>
        <div className="sportaabe-checkout-continue">
          <a onClick={onClick} href="#">
            Cliquez pour continuer
          </a>
        </div>
        <div className="sportaabe-checkout-loader">
          <div className="sportaabe-spinner"></div>
        </div>
      </div>
      <div className="sportaabe-checkout-iframe-container"></div>
    </div>
  );
}

export function Navbar(props: any) {
  const { isCollapsed, toggleSidebar, toggleSidebarPin } = props;
  return (
    <nav className="navbar navbar-expand-md fixed-top navbar-dark flex-fill">
      <div className="navbar-header pl-2 pr-2 ml-3 text-center">
        <a
          id="logo"
          href="/"
          className="d-flex justify-center align-center navbar-brand m-0 w-100 text-decoration-none"
        >
          <img
            src="/build/images/panier7.png"
            alt="Logo"
            style={{ maxWidth: "30px" }}
          />
          <span className="nav-brand text-white ml-1 font-weight-bold">
            sportaabe Mobile Pay
          </span>
        </a>
      </div>

      <div className="sidebar-toggler ml-auto mr-3">
        <a className="btn nav-link" onClick={() => toggleSidebar()}>
          <i className="fa fa-bars"></i>
        </a>
      </div>

      <div className="nav-toggler-right mr-4 d-md-none">
        <button
          className=""
          type="button"
          onClick={() => toggleSidebar()}
          aria-expanded={!isCollapsed}
          aria-controls="collapseBasic"
        >
          <img src="/build/images/user.jpg" className="img-fluid" alt="" />
        </button>
      </div>

      <ul className="navbar-nav flex-fill mt-1 align-items-center left-nav">
        <li className="nav-item navicon">
          <a className="btn nav-link" onClick={() => toggleSidebarPin()}>
            <i className="fa fa-bars"></i>
          </a>
        </li>
        {/* <li className="nav-item flex-fill">
          <input
            className="form-control navbar-search"
            type="text"
            placeholder="Search . . ."
          />
        </li> */}
      </ul>

      {/*  <div className="collapse navbar-collapse right-nav" id="collapseBasic">
        <ul className="navbar-nav ">
          <li className="nav-item">
            <a className="btn nav-link">
              <i className="far fa-bell"></i>
              <span className="link-text">Alerts</span>
              <span className="nav-alert notifications"></span>
            </a>
          </li>
          <li className="nav-item">
            <a className="btn nav-link">
              <i className="far fa-envelope"></i>
              <span className="link-text">Messages</span>
              <span className="nav-alert messages"></span>
            </a>
          </li>
          <li className="nav-item">
            <a className="btn nav-link">
              <i className="far fa-user"></i>
              <span className="link-text">Profile</span>
            </a>
          </li>
          <li className="nav-item">
            <a className="btn nav-link">
              <i className="fas fa-cog"></i>
              <span className="link-text">Setting</span>
            </a>
          </li>
        </ul>
      </div> */}
    </nav>
  );
}

import { PaymentRequestItem } from "../_entities";
import { AdminModel, LoginModel, PaymentModel } from "../_models";

interface PaymentState {
  token?: string;
  step?: number;
  freeze?: boolean;
  payModel: PaymentModel;
  popupWindow?: Window | null;
}

interface LoginState {
  freeze?: boolean;
  loginModel: LoginModel;
}

interface AdminState {
  isSidebarPinned: boolean;
  isSidebarToggeled: boolean;
  freeze?: boolean;
  adminModel: AdminModel;
}

interface PanierItemProps {
  isTotal?: boolean;
  item: PaymentRequestItem;
}

interface InputBoxProps {
  icon?: string;
  initialValue?: any;
  onChange?: (value: any) => void;
  inputProps: React.HTMLAttributes<HTMLInputElement> &
    React.InputHTMLAttributes<HTMLInputElement>;
}

interface InputGroupProps {
  inputs: InputBoxProps[];
}

interface PayMethodItemProps {
  methodId: string;
  label: string;
  checked?: boolean;
  onClick: () => void;
  detailedContent: string;
  inputProps: React.HTMLAttributes<HTMLInputElement> &
    React.InputHTMLAttributes<HTMLInputElement>;
  labelProps: React.LabelHTMLAttributes<HTMLLabelElement>;
  paymentMethodInputs?: InputGroupProps[];
}

interface ErrorContentProps {
  message?: string;
}

interface FieldsProps {
  rows: InputGroupProps[];
}

interface ModelOptions {
  token?: string;
  freeze?: boolean;
  step?: number;
  component?: any;
  pathname?: string;
}

interface AlertInterface {
  id: string;
  type: string;
  message: string;
}

interface SubmitBtnProps {
  freeze: boolean;
  label: string;
  freezeLabel?: string;
}

export type {
  PaymentState,
  PanierItemProps,
  InputBoxProps,
  InputGroupProps,
  PayMethodItemProps,
  ErrorContentProps,
  FieldsProps,
  ModelOptions,
  AlertInterface,
  LoginState,
  SubmitBtnProps,
  AdminState,
};

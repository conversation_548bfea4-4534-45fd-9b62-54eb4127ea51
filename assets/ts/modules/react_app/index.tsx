import React from "react";
import ReactDOM from "react-dom";
import { Provider } from "react-redux";
import { store } from "./_helpers";
import * as serviceWorkerRegistration from "./serviceWorkerRegistration";
import reportWebVitals from "./reportWebVitals";
import { App } from "./app/App";

export const reactApp = {
  run: function () {
    const domContainer = document.getElementById("countDown");
    if (!domContainer) {
      ReactDOM.render(
        <React.StrictMode>
          <Provider store={store}>
            <App />
          </Provider>
        </React.StrictMode>,
        document.getElementById("root")
      );
      serviceWorkerRegistration.unregister();
      reportWebVitals(console.log);
    }
  },
};

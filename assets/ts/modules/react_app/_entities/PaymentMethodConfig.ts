import { BaseEntity } from "./BaseEntity";

export interface PaymentMethodConfigInterface {
  id: number;
  libelle: string;
  description: string;
  active: boolean;
  createdAt: string;
  updatedAt: string;
}

export class PaymentMethodConfig extends BaseEntity {
  id: number;
  libelle: string;
  description: string;
  active: boolean;
  createdAt: string;
  updatedAt: string;

  fromJson(json: PaymentMethodConfigInterface): PaymentMethodConfig {
    this.id = json.id;
    this.libelle = json.libelle;
    this.description = json.description;
    this.active = json.active;
    this.createdAt = json.createdAt;
    this.updatedAt = json.updatedAt;
    return this;
  }
}

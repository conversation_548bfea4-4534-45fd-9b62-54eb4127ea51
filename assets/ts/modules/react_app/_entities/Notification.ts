import { BaseEntity } from "./BaseEntity";

export interface NotificationInterface {
  id: number;
  delay: number;
  content: string;
  type: string;
  receiver: string;
  status: string;
  createdAt: string;
  updatedAt: string;
}

export class Notification extends BaseEntity {
  id: number;
  delay: number;
  content: string;
  type: string;
  receiver: string;
  status: string;
  createdAt: string;
  updatedAt: string;

  fromJson(json: NotificationInterface): Notification {
    this.id = json.id;
    this.delay = json.delay;
    this.content = json.content;
    this.type = json.type;
    this.receiver = json.receiver;
    this.status = json.status;
    this.createdAt = json.createdAt;
    this.updatedAt = json.updatedAt;
    return this;
  }
}

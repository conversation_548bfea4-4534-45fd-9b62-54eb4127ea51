import { utils } from "../_helpers";
import { BaseEntity } from "./BaseEntity";

export interface SoldeOperationInterface {
  id: number;
  amount: number;
  type: string;
  comment: string;
  createdAt: string;
}

export class SoldeOperation extends BaseEntity {
  id: number;
  amount: number;
  type: string;
  comment: string;
  createdAt: string;

  from<PERSON><PERSON>(json: SoldeOperationInterface): SoldeOperation {
    if (json?.id) {
      this.id = json.id;
      this.amount = json.amount;
      this.type = json.type;
      this.comment = json.comment;
      this.createdAt = json.createdAt;
    }

    return this;
  }

  toView() {
    return [
      {
        title: "Informations générales",
        rows: [
          { label: "Solde Operation ID", value: this.id },
          {
            label: "Montant",
            value: utils().formatAmount("FCFA", this.amount),
          },
          {
            label: "Type d'opération",
            value: this.type,
          },
          {
            label: "Observations",
            value: this.comment,
          },
          {
            label: "Date de création",
            value: utils().formatDate("DD/MM/YYYY [à] H:mm:ss", this.createdAt),
          },
        ],
      },
    ];
  }
}

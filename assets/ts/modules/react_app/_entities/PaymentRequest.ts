import { BaseEntity } from "./BaseEntity";
import { Payment, PaymentCreateInterface, PaymentInterface } from "./Payment";
import {
  PaymentRequestItem,
  PaymentRequestItemInterface,
} from "./PaymenRequestItem";
import { utils } from "../_helpers";
import { Site, SiteInterface } from "./Site";

export class PaymentRequestInterface {
  id: number;
  retry: number;
  reference: string;
  amount: number;
  status: string;
  notifToken: string;
  returnUrl: string;
  cancelUrl: string;
  notifUrl: string;
  payUrl: string;
  createdAt: string;
  updatedAt: string;
  site: SiteInterface;
  payment: PaymentInterface;
  paymentMethod: string;
  orangePayUrl: string;
  orderId: string;
  financialTransactionId: string;
  paymentRequestItems: PaymentRequestItemInterface[];
}

export class PaymentRequest extends BaseEntity {
  id: number;
  orderId: string;
  retry: number;
  reference: string;
  amount: number;
  status: string;
  notifToken: string;
  returnUrl: string;
  cancelUrl: string;
  notifUrl: string;
  payUrl: string;
  orangePayUrl: string;
  createdAt: string;
  updatedAt: string;
  payment: Payment;
  site: Site;
  financialTransactionId: string;
  paymentMethod: string;
  paymentRequestItems: PaymentRequestItem[];

  fromJson(json: any): PaymentRequest {
    if (json?.id) {
      this.id = json.id;
      this.orderId = json.order_id;
      this.retry = json.retry;
      this.reference = json.reference;
      this.amount = json.amount;
      this.status = json.status;
      this.notifToken = json.notif_token;
      this.returnUrl = json.return_url;
      this.notifUrl = json.notif_url;
      this.payUrl = json.payUrl;
      this.createdAt = json.created_at;
      this.updatedAt = json.updated_at;
      this.paymentMethod = json.payment_method;
      this.orangePayUrl = json.orange_pay_url;
      this.financialTransactionId = json.financial_transaction_id;
      this.payment = new Payment().fromJson(json.payment);
      this.site = new Site().fromJson(json.site);
      this.paymentRequestItems = [];
    }

    return this;
  }

  public get formattedAmount(): string {
    return utils().formatAmount("FCFA", this.amount);
  }

  public get paymentMethodLibelle(): string {
    if (this.paymentMethod === "mtn") return "MTN Mobile";
    if (this.paymentMethod === "orange") return "Orange Money";
    return "Inconnu";
  }

  /**
   * loadFromToken
   */
  public async loadFromToken(token: string): Promise<PaymentRequest> {
    const res = await this.get(`/public/payments/${token}`);
    if (res?.data?.length) {
      return this.fromJson(res.data[0]);
    }
    throw new Error("Impossible d'initialiser la requete de paiement");
  }

  /**
   * processPayment
   */
  public async processPayment(payload: PaymentCreateInterface) {
    return this.post(
      `/public/payments/process`,
      payload
    ).then((res) => {
      console.log("===> res DONNEES ICI", res.data);
      if (res?.data?.id) {
        this.fromJson(res.data);
        console.log("===> res DONNEES ICI BON", res.data);
        return this;
      }
      throw new Error("Erreur lors du paiement");
    });
  }

  /**
   * checkPaymentStatus
   */
  public async checkPaymentStatus() {
    const res = await this.get(
      `/public/payments/check/${this.id}`
    );
    if (res?.data?.id) {
      this.fromJson(res.data);
      return this;
    }
  }
}

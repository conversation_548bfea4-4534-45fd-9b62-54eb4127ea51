import { utils } from "../_helpers";
import { BaseEntity } from "./BaseEntity";
import {
  PaymentMethodConfig,
  PaymentMethodConfigInterface,
} from "./PaymentMethodConfig";

export interface PaymentMethodInterface {
  id: number;
  libelle: string;
  code: string;
  description: string;
  active: boolean;
  createdAt: string;
  updatedAt: string;
  paymentMethodConfigs: PaymentMethodConfigInterface[];
}

export class PaymentMethod extends BaseEntity {
  id: number;
  libelle: string;
  code: string;
  description: string;
  active: boolean;
  createdAt: string;
  updatedAt: string;
  paymentMethodConfigs: PaymentMethodConfig[];

  fromJson(json: PaymentMethodInterface): PaymentMethod {
    if (json?.id) {
      this.id = json.id;
      this.libelle = json.libelle;
      this.code = json.code;
      this.description = json.description;
      this.active = json.active;
      this.createdAt = json.createdAt;
      this.updatedAt = json.updatedAt;
      this.paymentMethodConfigs = json.paymentMethodConfigs?.map((p) =>
        new PaymentMethodConfig().fromJson(p)
      );
    }

    return this;
  }

  public async update() {
    const res = await this.patch(
      `/paymentMethods/${this.id}`,
      this.toJson("update")
    );
    if (res?.data?.id) {
      return this;
    }
    throw new Error("Erreur lors de la mise à jour du profil");
  }

  toJson(context: string = "create") {
    if (context === "update") {
      return {
        libelle: this.libelle,
        description: this.description,
      };
    }
    return {};
  }

  toView() {
    return [
      {
        title: "Informations générales",
        rows: [
          { label: "Payment method ID", value: this.id },
          {
            label: "Libellé",
            value: this.libelle,
          },
          {
            label: "Date de création",
            value: utils().formatDate("DD/MM/YYYY [à] H:mm:ss", this.createdAt),
          },
        ],
      },
      {
        title: "Description",
        rows: [{ label: false, value: this.description }],
      },
    ];
  }
}

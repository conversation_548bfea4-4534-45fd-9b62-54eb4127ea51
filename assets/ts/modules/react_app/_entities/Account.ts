import { utils } from "../_helpers";
import { BaseEntity } from "./BaseEntity";
import { <PERSON>chier } from "./Fichier";
import { PaymentMethod } from "./PaymentMethod";
import { Role } from "./Role";

export interface AccountInterface {
  id: number;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  email: string;
  active: string;
  username: string;
  status: string;
  lastLogin: string;
  createdAt: string;
  updatedAt: string;
  site: any;
  photo: Fichier;
  permissions: PaymentMethod;
  userRoles: Role[];
}

export class Account extends BaseEntity {
  id: number;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  email: string;
  active: string;
  username: string;
  status: string;
  lastLogin: string;
  createdAt: string;
  updatedAt: string;
  siteId: number;
  site: any;
  photo: Fichier;
  permissions: PaymentMethod;
  userRoles: Role[];

  password: string;
  password1: string;
  password2: string;

  public get fullname(): string {
    return `${this.firstName} ${this.lastName}`;
  }

  public get role(): string {
    if (this.isSuperAdmin || this.isAdmin) {
      return "Adminitrateur";
    }
    return "Utilisateur";
  }

  public get isSuperAdmin(): boolean {
    if (this.userRoles?.length) {
      const i = this.userRoles.findIndex(
        (r) => r.libelle === "ROLE_SUPER_ADMIN"
      );
      if (i > -1) {
        return true;
      }
    }
    return false;
  }

  public get isAdmin(): boolean {
    if (this.userRoles?.length) {
      const i = this.userRoles.findIndex((r) => r.libelle === "ROLE_ADMIN");
      if (i > -1) {
        return true;
      }
    }
    return false;
  }

  public get isSuperAdminOrAdmin(): boolean {
    return this.isAdmin || this.isSuperAdmin;
  }

  public get userPath(): string {
    if (this.isSuperAdminOrAdmin) {
      return "/admin";
    }
    return "/dashboard";
  }

  fromJson(json: AccountInterface): Account {
    if (json?.id) {
      this.id = json.id;
      this.status = json.status;
      this.firstName = json.firstName;
      this.lastName = json.lastName;
      this.phoneNumber = json.phoneNumber;
      this.email = json.email;
      this.username = json.username;
      this.active = json.active;
      this.createdAt = json.createdAt;
      this.updatedAt = json.updatedAt;
      this.siteId = json?.site?.id ? json?.site?.id : 0;
      this.site = json?.site ? json?.site : null;
      if (json.userRoles?.length) {
        this.userRoles = json.userRoles.map((r) => new Role().fromJson(r));
      }
    }

    return this;
  }

  toView() {
    const data = [
      {
        title: "Informations générales",
        rows: [
          { label: "User ID", value: this.id },
          {
            label: "Nom",
            value: this.firstName,
          },
          {
            label: "Prénom",
            value: this.lastName,
          },
          {
            label: "Email",
            value: this.email || "-",
          },
          {
            label: "Téléphone",
            value: this.phoneNumber,
          },
          {
            label: "Nom d'utilisateur",
            value: this.username,
          },
          {
            label: "Statut",
            value: this.status,
          },
          {
            label: "Type d'utilisateur",
            value: this.site ? "Client professionnel" : "Administrateur",
          },
        ],
      },
    ];
    if (this.site) {
      data[0].rows.push(
        {
          label: "Nom de l'entreprise",
          value: this.site.name,
        },
        {
          label: "Fonction",
          value: "Administrateur",
        }
      );
    }
    data[0].rows.push(
      {
        label: "Date de création",
        value: utils().formatDate("DD/MM/YYYY [à] H:mm:ss", this.createdAt),
      },
      {
        label: "Dernière mise à jour",
        value: this.updatedAt
          ? utils().formatDate("DD/MM/YYYY [à] H:mm:ss", this.updatedAt)
          : "Jamais modifié",
      }
    );
    return data;
  }

  public async processLogin(username: string, password: string) {
    const payload = { username, password };
    return await this.post("/auth/login", payload);
  }

  public async updateProfil() {
    const res = await this.patch(`/accounts/${this.id}`, this.toJson("update"));
    if (res?.data?.id) {
      return this;
    }
    throw new Error("Erreur lors de la mise à jour du profil");
  }

  public async switchStatus() {
    const path = this.active
      ? `/accounts/${this.id}/deactivate`
      : `/accounts/${this.id}/activate`;
    const res = await this.patch(path, {});
    if (res?.data?.id) {
      return this;
    }
    return false;
  }

  public async updatePassword() {
    const res = await this.patch(
      `/accounts/${this.id}/password`,
      this.toJson("password")
    );
    if (res?.data?.id) {
      return this;
    }
    throw new Error("Erreur lors de la mise à jour du profil");
  }

  toJson(context: string = "create") {
    if (context === "update") {
      return {
        firstName: this.firstName,
        lastName: this.lastName,
        phoneNumber: this.phoneNumber,
        username: this.username,
        email: this.email,
        password: "_",
      };
    } else if (context === "password") {
      return {
        password: this.password1,
      };
    }
    return {};
  }

  public async loadFromSession() {
    const res = await this.get("/auth/status");
    if (res?.data?.id) {
      return this.fromJson(res.data);
    }
    return this;
  }
}

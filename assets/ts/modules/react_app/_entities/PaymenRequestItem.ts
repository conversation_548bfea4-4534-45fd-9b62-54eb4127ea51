import { BaseEntity } from "./BaseEntity";

export interface PaymentRequestItemInterface {
  id: number;
  libelle: string;
  quantity: number;
  price: number;
}

export class PaymentRequestItem extends BaseEntity {
  id: number;
  libelle: string;
  quantity: number;
  price: number;

  isTotal?: boolean;

  formJson(json: PaymentRequestItemInterface) {
    this.id = json.id;
    this.libelle = json.libelle;
    this.quantity = json.quantity;
    this.price = json.price;
    return this;
  }
}

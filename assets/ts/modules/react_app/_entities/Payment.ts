import { utils } from "../_helpers";
import { BaseEntity } from "./BaseEntity";
import {
  Customer,
  CustomerCreateInterface,
  CustomerInterface,
} from "./Customer";
import { NotificationInterface, Notification } from "./Notification";
import { PaymentMethod, PaymentMethodInterface } from "./PaymentMethod";

export interface PaymentInterface {
  id: number;
  status: string;
  amount: number;
  payUrl: string;
  fees: number;
  transactionId: string;
  financialTransactionId: string;
  createdAt: string;
  updatedAt: string;
  customer: CustomerInterface;
  paymentRequest: any;
  paymentMethod: PaymentMethodInterface;
  notifications: NotificationInterface[];
}

export interface PaymentCreateInterface {
  paymentRequest: number;
  methodId: string;
  customer: CustomerCreateInterface;
}

export class Payment extends BaseEntity {
  id: number;
  status: string;
  amount: number;
  fees: number;
  transactionId: string;
  payUrl: string;
  financialTransactionId: string;
  createdAt: string;
  updatedAt: string;
  customer: Customer;
  paymentMethod: PaymentMethod;
  paymentRequest: any;
  notifications: Notification[];

  public get formattedAmount(): string {
    return utils().formatAmount("FCFA", this.amount);
  }

  fromJson(json: PaymentInterface): Payment {
    if (json?.id) {
      this.id = json.id;
      this.status = json.status;
      this.payUrl = json.payUrl;
      this.amount = json.amount;
      this.fees = json.fees;
      this.paymentRequest = json.paymentRequest;
      this.transactionId = json.transactionId;
      this.financialTransactionId = json.financialTransactionId;
      this.createdAt = json.createdAt;
      this.updatedAt = json.updatedAt;
      this.customer = new Customer().fromJson(json.customer);
      this.paymentMethod = new PaymentMethod().fromJson(json.paymentMethod);
      this.notifications = json.notifications?.map((p) =>
        new Notification().fromJson(p)
      );
    }

    return this;
  }

  toView() {
    return [
      {
        title: "Informations générales",
        rows: [
          { label: "Payment ID", value: this.id },
          {
            label: "financial Transaction Id",
            value: this.financialTransactionId,
          },
          {
            label: "Montant",
            value: utils().formatAmount("FCFA", this.amount),
          },
          {
            label: "Frais",
            value: utils().formatAmount("FCFA", this.fees),
          },
          {
            label: "Statut",
            value: this.status,
          },
          {
            label: "Opérateur",
            value: this.paymentMethod?.libelle,
          },
          {
            label: "Date de l'opération",
            value: utils().formatDate("DD/MM/YYYY [à] H:mm:ss", this.createdAt),
          },
          {
            label: "Dernière mise à jour",
            value: this.updatedAt
              ? utils().formatDate("DD/MM/YYYY [à] H:mm:ss", this.updatedAt)
              : "Jamais modifié",
          },
        ],
      },
      {
        title: "Client",
        rows: [
          { label: "Nom", value: this.customer.firstName },
          { label: "Prénom", value: this.customer.lastName },
          { label: "Téléphone", value: this.customer.phone },
          { label: "Ville", value: this.customer.city },
        ],
      },
    ];
  }

  async removeElement() {
    const res = await this.remove(`/payments/${this.id}`);
    if (res?.status === "ok") {
      return true;
    }
    return false;
  }
}

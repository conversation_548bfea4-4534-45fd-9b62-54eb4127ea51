import { BaseEntity } from "./BaseEntity";

export interface CustomerInterface {
  id: number;
  firstName: string;
  lastName: string;
  phone: string;
  city: string;
  createdAt: string;
  updatedAt: string;
  mtnPayPhone: string;
}

export interface CustomerCreateInterface {
  firstName: string;
  lastName: string;
  phone: string;
  city: string;
  mtnPayPhone: string;
}

export class Customer extends BaseEntity {
  id: number;
  firstName: string;
  lastName: string;
  phone: string;
  city: string;
  createdAt: string;
  updatedAt: string;
  mtnPayPhone: string;

  fromJson(json: CustomerInterface): Customer {
    if (json?.id) {
      this.id = json.id;
      this.firstName = json.firstName;
      this.lastName = json.lastName;
      this.phone = json.phone;
      this.city = json.city;
      this.mtnPayPhone = json.mtnPayPhone;
      this.createdAt = json.createdAt;
      this.updatedAt = json.updatedAt;
    }

    return this;
  }

  toJson(): CustomerCreateInterface {
    return {
      firstName: this.firstName,
      lastName: this.lastName,
      phone: this.phone,
      city: this.city,
      mtnPayPhone: this.mtnPayPhone,
    };
  }
}

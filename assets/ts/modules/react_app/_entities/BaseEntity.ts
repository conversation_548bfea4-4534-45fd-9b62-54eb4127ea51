import { cloneDeep } from "lodash";
import { restClient } from "../_helpers";

class BaseEntity {
  [key: string]: any;

  get(req: any): Promise<any> {
    return restClient.get(req);
  }

  post(req: any, data?: any): Promise<any> {
    return restClient.post(req, data);
  }

  put(req: any, data?: any): Promise<any> {
    return restClient.put(req, data);
  }

  patch(req: any, data?: any): Promise<any> {
    return restClient.patch(req, data);
  }

  remove(req: any): Promise<any> {
    return restClient.remove(req);
  }

  clone() {
    return cloneDeep(this);
  }
}

export { BaseEntity };

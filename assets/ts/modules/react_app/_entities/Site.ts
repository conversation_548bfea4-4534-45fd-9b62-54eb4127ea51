import { utils } from "../_helpers";
import { BaseEntity } from "./BaseEntity";

export interface SiteInterface {
  id: number;
  name: string;
  status: string;
  description: string;
  merchandKey: string;
  website: string;
  active: boolean;
  solde: any;
  contactEmail: string;
  createdAt: string;
  updatedAt: string;
}

export class Site extends BaseEntity {
  id: number;
  name: string;
  status: string;
  merchandKey: string;
  description: string;
  website: string;
  active: boolean;
  solde: any;
  contactEmail: string;
  createdAt: string;
  updatedAt: string;

  fromJson(json: SiteInterface): Site {
    if (json?.id) {
      this.id = json.id;
      this.name = json.name;
      this.status = json.status;
      this.active = json.active;
      this.solde = json.solde;
      this.contactEmail = json.contactEmail;
      this.merchandKey = json.merchandKey;
      this.description = json.description;
      this.website = json.website;
      this.createdAt = json.createdAt;
      this.updatedAt = json.updatedAt;
    }

    return this;
  }

  public async switchStatus() {
    const path = this.active
      ? `/sites/${this.id}/deactivate`
      : `/sites/${this.id}/activate`;
    const res = await this.patch(path, {});
    if (res?.data?.id) {
      return this;
    }
    return false;
  }

  toView() {
    return [
      {
        title: "Informations générales",
        rows: [
          { label: "Site ID", value: this.id },
          {
            label: "Nom",
            value: this.name,
          },
          /*{
            label: "Statut",
            value: this.status,
          },*/
          {
            label: "Merchand Key",
            value: this.merchandKey,
          },
          {
            label: "Site web",
            value: `<a href="${this.website}" target="_blank">Ouvrir</a>`,
          },
          {
            label: "Email de contact",
            value: `<a href="mailto:${this.contactEmail}" target="_blank">${this.contactEmail}</a>`,
          },
          {
            label: "Solde",
            value: utils().formatAmount("FCFA", this.solde?.value),
          },
          {
            label: "Date de création",
            value: utils().formatDate("DD/MM/YYYY [à] H:mm:ss", this.createdAt),
          },
          {
            label: "Dernière mise à jour",
            value: this.updatedAt
              ? utils().formatDate("DD/MM/YYYY [à] H:mm:ss", this.updatedAt)
              : "Jamais modifié",
          },
        ],
      },
      {
        title: "Description",
        rows: [{ label: false, value: this.description }],
      },
    ];
  }

  public async updateSite() {
    const res = await this.patch(`/sites/${this.id}`, this.toJson("update"));
    if (res?.data?.id) {
      return this;
    }
    throw new Error("Erreur lors de la mise à jour du profil");
  }

  toJson(context: string = "create") {
    if (context === "update") {
      return {
        name: this.name,
        website: this.website,
        description: this.description,
        contactEmail: this.contactEmail,
      };
    }
    return {};
  }
}

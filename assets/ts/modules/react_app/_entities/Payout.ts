import { utils } from "../_helpers";
import { BaseEntity } from "./BaseEntity";
import { NotificationInterface, Notification } from "./Notification";
import { PaymentMethod, PaymentMethodInterface } from "./PaymentMethod";
import { Site } from "./Site";

export interface PayoutInterface {
  id: number;
  status: string;
  amount: number;
  fees: number;
  payUrl: string;
  tel: string;
  transactionId: string;
  financialTransactionId: string;
  createdAt: string;
  updatedAt: string;
  site: any;
  paymentMethod: PaymentMethodInterface;
  notifications: NotificationInterface[];
}

export class Payout extends BaseEntity {
  id: number;
  status: string;
  amount: number;
  fees: number;
  transactionId: string;
  payUrl: string;
  tel: string;
  financialTransactionId: string;
  createdAt: string;
  updatedAt: string;
  siteName: string;
  site: Site;
  paymentMethod: PaymentMethod;
  notifications: Notification[];

  public get formattedAmount(): string {
    return utils().formatAmount("FCFA", this.amount);
  }

  fromJson(json: PayoutInterface): Payout {
    if (json?.id) {
      this.id = json.id;
      this.status = json.status;
      this.payUrl = json.payUrl;
      this.amount = json.amount;
      this.fees = json.fees;
      this.tel = json.tel;
      this.transactionId = json.transactionId;
      this.financialTransactionId = json.financialTransactionId;
      this.createdAt = json.createdAt;
      this.updatedAt = json.updatedAt;
      this.paymentMethod = new PaymentMethod().fromJson(json.paymentMethod);
      if (json?.site) {
        this.siteName = json.site.name;
        this.site = new Site().fromJson(json.site);
      }
      this.notifications = json.notifications?.map((p) =>
        new Notification().fromJson(p)
      );
    }

    return this;
  }

  toView() {
    return [
      {
        title: "Informations générales",
        rows: [
          { label: "Payment ID", value: this.id },
          {
            label: "financial Transaction Id",
            value: this.financialTransactionId
              ? "JPM " + this.financialTransactionId
              : "-",
          },
          {
            label: "Montant",
            value: utils().formatAmount("FCFA", this.amount),
          },
          {
            label: "Frais",
            value: utils().formatAmount("FCFA", this.fees),
          },
          {
            label: "Statut",
            value: this.status,
          },
          {
            label: "Téléphone de paiement",
            value: this.tel,
          },
          {
            label: "Opérateur",
            value: this.paymentMethod?.libelle,
          },
          {
            label: "Date de l'opération",
            value: utils().formatDate("DD/MM/YYYY [à] H:mm:ss", this.createdAt),
          },
          {
            label: "Dernière mise à jour",
            value: this.updatedAt
              ? utils().formatDate("DD/MM/YYYY [à] H:mm:ss", this.updatedAt)
              : "Jamais modifié",
          },
        ],
      },
      {
        title: "Site web (Client professionnel)",
        rows: [
          { label: "Nom", value: this.site.name },
          {
            label: "Lien",
            value: `<a href="${this.site.website}" target="_blank">Ouvrir</a>`,
          },
          {
            label: "Email de contact",
            value: `<a href="mailto:${this.site.contactEmail}" target="_blank">${this.site.contactEmail}</a>`,
          },
          { label: "Description", value: this.site.description },
        ],
      },
    ];
  }

  async processPayout(payload: any) {
    return this.post(`/payouts`, payload).then((res) => {
      if (res?.data?.id) {
        return this.fromJson(res.data);
      }
      throw new Error("Erreur lors du payout");
    });
  }
}

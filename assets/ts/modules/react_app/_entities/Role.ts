import { BaseEntity } from "./BaseEntity";

export class RoleInterface {
  id: number;
  libelle: string;
  description: string;
  active: boolean;
  createdAt: string;
  updatedAt: string;
}
export class Role extends BaseEntity {
  id: number;
  libelle: string;
  description: string;
  active: boolean;
  createdAt: string;
  updatedAt: string;

  fromJson(json: RoleInterface): Role {
    if (json?.id) {
      this.id = json.id;
      this.libelle = json.libelle;
      this.description = json.description;
      this.active = json.active;
      this.createdAt = json.createdAt;
      this.updatedAt = json.updatedAt;
    }
    return this;
  }
}

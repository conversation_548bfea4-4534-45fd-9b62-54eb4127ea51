import moment from "moment";
moment.locale("fr");

import { restClient, utils } from "../_helpers";
import { BaseModel } from "../_models";

export const dashboardPrefix = "/dashboard";

export const dashboardSidebarItems = [
  {
    label: "Opérations courantes",
    children: [
      {
        path: dashboardPrefix,
        icon: "fa fa-tv",
        label: "Dashboard",
      },
      {
        path: `${dashboardPrefix}/paiements`,
        icon: "fas fa-money",
        label: "Mes paiements",
      },
      {
        path: `${dashboardPrefix}/requetes`,
        icon: "fa fa-align-center",
        label: "Mes requêtes",
      },
      {
        path: `${dashboardPrefix}/payouts`,
        icon: "fab fa-btc",
        label: "Mes payouts",
      },
      {
        path: `${dashboardPrefix}/ecritures`,
        icon: "fa fa-edit",
        label: "Opérations",
      },
    ],
  },
  {
    label: "Configurations",
    children: [
      {
        path: `${dashboardPrefix}/site`,
        icon: "fa fa-briefcase",
        label: "Mon site",
      },
      {
        path: `${dashboardPrefix}/profil`,
        icon: "fa fa-user",
        label: "Mon profil",
      },
      {
        path: `${dashboardPrefix}/profil/password/edit`,
        icon: "fa fa-lock",
        label: "Sécurité",
      },
    ],
  },
];

export const dashboardKpiItems = [
  {
    path: `${dashboardPrefix}/paiements`,
    title: "Total paiements",
    icon: "fas fa-money",
    key: "totalPaiementsAmount",
    dataCallBack: async () => {
      return new Promise(async (resole) => {
        const res = await restClient.get(
          `/payments/kpis/totalPaiementsAmount/?site=${
            BaseModel.getInstance().user.siteId
          }`
        );
        if (parseInt(res.data) >= 0) {
          resole({ data: utils().formatAmount("FCFA", res.data) });
        }
        resole({});
      });
    },
  },
  {
    path: `${dashboardPrefix}/paiements`,
    title: "Paiements traités",
    icon: "fa fa-random",
    key: "totalPaiements",
    dataCallBack: async () => {
      return new Promise(async (resole) => {
        const res = await restClient.get(
          `/payments/kpis/totalPaiements/?site=${
            BaseModel.getInstance().user.siteId
          }`
        );
        if (parseInt(res.data) >= 0) {
          resole({ data: utils().formatInt(res.data) });
        }
        resole({});
      });
    },
  },
  {
    path: `${dashboardPrefix}/paiements`,
    title: "Paiements réussies",
    icon: "fa fa-check",
    key: "totalPaiementsSuccess",
    dataCallBack: async () => {
      return new Promise(async (resole) => {
        const res = await restClient.get(
          `/payments/kpis/totalPaiementsSuccess/?site=${
            BaseModel.getInstance().user.siteId
          }`
        );
        if (parseInt(res.data) >= 0) {
          resole({ data: utils().formatInt(res.data) });
        }
        resole({});
      });
    },
  },
  {
    path: `${dashboardPrefix}/paiements`,
    title: "Paiements échoués",
    icon: "fa fa-times",
    key: "totalPaiementsFailed",
    dataCallBack: async () => {
      return new Promise(async (resole) => {
        const res = await restClient.get(
          `/payments/kpis/totalPaiementsFailed/?site=${
            BaseModel.getInstance().user.siteId
          }`
        );
        if (parseInt(res.data) >= 0) {
          resole({ data: utils().formatInt(res.data) });
        }
        resole({});
      });
    },
  },
];

export const dashboardWidgets = [
  {
    id: "chart-paiement-count-per-operator",
    title: "Évolution des paiements suivant les opérateurs",
    cls: "col-12 col-xl-6 mb-4",
    type: "chart",
    settings: {
      type: "line",
      cls2: "chart-container",
      canvasAttributes: {
        id: "chart-paiement-count-per-operator",
        width: 2,
        height: 1,
      },
      options: {
        scales: {
          yAxes: [
            {
              ticks: {
                fontColor: "rgba(0,0,0,.6)",
                fontStyle: "bold",
                beginAtZero: true,
                maxTicksLimit: 8,
                padding: 10,
              },
            },
          ],
        },
        responsive: true,
        legend: {
          position: "bottom",
          display: false,
        },
      },
    },
    dataCallBack: async () => {
      return new Promise(async (resole) => {
        const res = await restClient.get(
          `/payments/kpis/paiementsEvolution/?site=${
            BaseModel.getInstance().user.siteId
          }`
        );
        if (res.data?.length) {
          let data = {
            labels: res?.data.map((r: any) =>
              moment(r.date).format("MMM YYYY")
            ),
            datasets: [
              {
                label: "MTN",
                data: res?.data.map((r: any) => r.mtn || 0),
                backgroundColor: "transparent",
                borderColor: "#5b6582",
                borderWidth: 2,
              },
              {
                label: "Orange",
                data: res?.data.map((r: any) => r.orange || 0),
                backgroundColor: "transparent",
                borderColor: "#36a2eb",
                borderWidth: 2,
              },
            ],
          };
          resole({ data });
        }
        resole(false);
      });
    },
  },
  {
    id: "chart-paiement-per-operator",
    title: "Volumes des transactions réalisées par opérateur",
    cls: "col-12 col-xl-6 mb-4",
    type: "chart",
    settings: {
      type: "bar",
      cls2: "chart-container",
      canvasAttributes: {
        id: "chart-paiement-per-operator",
        width: 2,
        height: 1,
      },
      options: {
        barValueSpacing: 1,
        scales: {
          yAxes: [
            {
              ticks: {
                fontColor: "rgba(0,0,0,.6)",
                fontStyle: "bold",
                beginAtZero: true,
                maxTicksLimit: 8,
                padding: 10,
              },
            },
          ],
          xAxes: [
            {
              barPercentage: 0.4,
            },
          ],
        },
        responsive: true,
        legend: {
          position: "bottom",
          display: false,
        },
      },
    },
    dataCallBack: async () => {
      return new Promise(async (resole) => {
        const res = await restClient.get(
          `/payments/kpis/paiementsEvolutionAmount/?site=${
            BaseModel.getInstance().user.siteId
          }`
        );
        if (res.data?.length) {
          let data = {
            labels: res?.data.map((r: any) =>
              moment(r.date).format("MMM YYYY")
            ),
            datasets: [
              {
                label: "MTN",
                data: res?.data.map((r: any) => r.mtn || 0),
                backgroundColor: "#5b6582",
                borderColor: "#5b6582",
                borderWidth: 2,
              },
              {
                label: "Orange",
                data: res?.data.map((r: any) => r.orange || 0),
                backgroundColor: "#36a2eb",
                borderColor: "#36a2eb",
                borderWidth: 2,
              },
            ],
          };
          resole({ data });
        }
        resole(false);
      });
    },
  },
  {
    id: "chart-paiement-repartition",
    title: "Repartition des paiements",
    cls: "col-12 col-xl-4 mb-4 align-items-stretch",
    type: "chart",
    hasFooter: true,
    settings: {
      type: "doughnut",
      cls2:
        "chart-container d-flex h-100 align-items-center justify-content-center",
      canvasAttributes: {
        id: "chart-paiement-repartition",
        style: { height: "100%" },
      },
      options: {
        legend: {
          position: "bottom",
          display: false,
        },
        cutoutPercentage: 80,
      },
    },
    dataCallBack: async () => {
      return new Promise(async (resole) => {
        const res = await restClient.get(
          `/payments/kpis/paiementsEvolutionCount/?site=${
            BaseModel.getInstance().user.siteId
          }`
        );
        if (
          typeof res?.data?.mtn !== "undefined" &&
          typeof res?.data?.orange !== "undefined"
        ) {
          let data = {
            datasets: [
              {
                data: [parseInt(res?.data?.mtn), parseInt(res?.data?.orange)],
                backgroundColor: ["#98a4c7", "#36a2eb"],
              },
            ],
            labels: ["MTN", "Orange"],
          };
          resole({ data });
        }
        resole(false);
      });
    },
  },
  {
    id: "chart-paiements-recent",
    title: "Paiements récents",
    cls: "col-12 col-xl-8 mb-4 align-items-stretch",
    type: "table",
    settings: {},
    dataCallBack: async () => {
      return new Promise(async (resole) => {
        const res = await restClient.get(
          `/payments/kpis/paiementsRecents/?site=${
            BaseModel.getInstance().user.siteId
          }`
        );
        if (Array.isArray(res?.data)) {
          resole(res);
        }
        resole(false);
      });
    },
  },
  {
    id: "chart-payouts-recent",
    title: "Payouts récents",
    cls: "col-12 col-xl-6 mb-4",
    type: "table2",
    settings: {},
    dataCallBack: async () => {
      return new Promise(async (resole) => {
        const res = await restClient.get(
          `/payments/kpis/payoutsRecents/?site=${
            BaseModel.getInstance().user.siteId
          }`
        );
        if (Array.isArray(res?.data)) {
          resole(res);
        }
        resole(false);
      });
    },
  },
  {
    id: "chart-requests-recent",
    title: "Requêtes récentes",
    cls: "col-12 col-xl-6 mb-4",
    type: "table3",
    settings: {},
    dataCallBack: async () => {
      return new Promise(async (resole) => {
        const res = await restClient.get(
          `/payments/kpis/paiementRequestsRecents/?site=${
            BaseModel.getInstance().user.siteId
          }`
        );
        if (Array.isArray(res?.data)) {
          resole(res);
        }
        resole(false);
      });
    },
  },
];

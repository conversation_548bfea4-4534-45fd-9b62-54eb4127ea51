import { cloneDeep } from "lodash";
import { AdminState, AlertInterface, ModelOptions } from "../_interfaces";

export class AdminModel {
  private static _instance: AdminModel | undefined;

  pathname: string;
  freeze: boolean;
  step: number;
  alerts: AlertInterface[];

  component: any;

  constructor(options?: ModelOptions) {
    if (AdminModel._instance) {
      throw new Error(
        "Error : Instanciation failed : Use AdminModel.getInstance() instead of new."
      );
    }

    this.alerts = [];
    this.initOptions(options);
    AdminModel._instance = this;
  }

  public static getInstance(options?: ModelOptions): AdminModel {
    if (!AdminModel._instance) {
      return new AdminModel(options);
    }
    return AdminModel._instance;
  }

  public reset(): AdminModel {
    AdminModel._instance = undefined;
    return AdminModel.getInstance();
  }

  clone() {
    return cloneDeep(this);
  }

  initOptions(options?: ModelOptions) {
    if (options) {
      const { freeze, component, step } = options;
      this.freeze = freeze ?? false;
      this.step = step ?? 1;
      this.component = component ?? null;
    }
  }

  rebuildView(newState?: AdminState) {
    if (this.component) {
      this.component.refresh(newState);
    }
  }
}

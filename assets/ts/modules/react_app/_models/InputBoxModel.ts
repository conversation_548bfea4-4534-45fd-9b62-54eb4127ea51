import { InputBoxProps } from "../_interfaces";

export class InputBoxModel implements InputBoxProps {
  icon?: string;
  initialValue?: any;
  onChange?: (value: any) => void;
  inputProps: React.HTMLAttributes<HTMLInputElement> &
    React.InputHTMLAttributes<HTMLInputElement>;

  constructor(json: InputBoxProps) {
    this.icon = json.icon;
    this.initialValue = json.initialValue;
    this.onChange = json.onChange;
    this.inputProps = json.inputProps;
  }
}

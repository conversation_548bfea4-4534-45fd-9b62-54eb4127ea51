import { InputGroupProps, PayMethodItemProps } from "../_interfaces";
import { InputBoxModel } from "./InputBoxModel";
import { InputGroupModel } from "./InputGroupModel";

export class PayMethodItemModel implements PayMethodItemProps {
  methodId: string;
  label: string;
  checked?: boolean;
  detailedContent: string;
  inputProps: React.HTMLAttributes<HTMLInputElement> &
    React.InputHTMLAttributes<HTMLInputElement>;
  labelProps: React.LabelHTMLAttributes<HTMLLabelElement>;
  onClick: () => void;
  paymentMethodInputs?: InputGroupProps[];

  constructor(json: PayMethodItemProps) {
    this.detailedContent = json.detailedContent;
    this.checked = json.checked;
    this.label = json.label;
    this.inputProps = json.inputProps;
    this.labelProps = json.labelProps;
    this.methodId = json.methodId;
    this.onClick = json.onClick;
    this.paymentMethodInputs = json.paymentMethodInputs;
  }

  /**
   * getPayMethodItemModels
   */
  public static getPayMethodItemModels(obj: any): PayMethodItemModel[] {
    return [
      new PayMethodItemModel({
        methodId: "mtn",
        label: "MTN Mobile",
        detailedContent: "mtn",
        onClick: () => obj.changeSelectedPayMethodItemId("mtn"),
        checked: true,
        inputProps: {
          id: "bc1",
        },
        labelProps: {
          htmlFor: "bc1",
          style: { pointerEvents: obj.freeze ? "none" : "auto" },
        },
        paymentMethodInputs: [
          new InputGroupModel({
            inputs: [
              new InputBoxModel({
                icon: "fa fa-phone",
                initialValue: obj.customer.mtnPayPhone,
                onChange: (value) => (obj.customer.mtnPayPhone = value),
                inputProps: {
                  placeholder: "Numéro MoMo (ex.237XXXXXXXXX )",
                  required: true,
                  disabled: obj.freeze,
                  type: "tel",
                  name: "mtnPayPhone",
                  value: obj.customer.mtnPayPhone,
                },
              }),
            ],
          }),
        ],
      }),
      new PayMethodItemModel({
        methodId: "orange",
        label: "Orange Money",
        detailedContent: "orange",
        onClick: () => obj.changeSelectedPayMethodItemId("orange"),
        inputProps: {
          id: "bc2",
        },
        labelProps: {
          htmlFor: "bc2",
          style: { pointerEvents: obj.freeze ? "none" : "auto" },
        },
      }),
    ];
  }

  toJson(): PayMethodItemProps {
    const json = {
      methodId: this.methodId,
      label: this.label,
      detailedContent: this.detailedContent,
      inputProps: this.inputProps,
      labelProps: this.labelProps,
      paymentMethodInputs: this.paymentMethodInputs,
      onClick: () => this.onClick(),
    };
    if (this.checked) {
      Object.assign(json, { checked: true });
    }
    return json;
  }
}

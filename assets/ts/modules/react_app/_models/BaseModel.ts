import { cloneDeep } from "lodash";
import { Account } from "../_entities";
import { restClient } from "../_helpers";

class BaseModel {
  private static _instance: BaseModel | undefined;

  user: Account;
  fullLoading = false;

  constructor() {
    if (BaseModel._instance) {
      throw new Error(
        "Error : Instanciation failed : Use BaseModel.getInstance() instead of new."
      );
    }
    this.user = new Account();
    BaseModel._instance = this;
  }

  public static getInstance(): BaseModel {
    if (!BaseModel._instance) {
      return new BaseModel();
    }
    return BaseModel._instance;
  }

  public static reset(): BaseModel {
    BaseModel._instance = undefined;
    return BaseModel.getInstance();
  }

  public get path(): string {
    return window.location.pathname;
  }

  public get userPath(): string {
    return this.user.userPath;
  }

  public get isLogin(): boolean {
    if (sessionStorage.getItem("jwttoken")) {
      return true;
    }
    return false;
  }

  async initUser(user?: Account) {
    if (!user) {
      this.user = await this.user.loadFromSession();
    } else {
      this.user = user;
    }
    this.fullLoading = false;
    return true;
  }

  async logout() {
    let res = await restClient.get("/auth/logout");
    this.fullLoading = false;
    return res.success === true;
  }

  public get hasAccess(): boolean {
    if (this.path.indexOf(this.userPath) !== -1) {
      return true;
    }
    return false;
  }

  clone() {
    return cloneDeep(this);
  }
}

export { BaseModel };

import { InputGroupModel } from "./InputGroupModel";
import { InputBoxModel } from "./InputBoxModel";

export class PayerInfosModel {
  /**
   * getPayerInfosFields
   */
  public static getPayerInfosFields(obj: any): InputGroupModel[] {
    return [
      new InputGroupModel({
        inputs: [
          new InputBoxModel({
            icon: "fa fa-user",
            initialValue: obj.customer.firstName,
            onChange: (value) => (obj.customer.firstName = value),
            inputProps: {
              placeholder: "Noms",
              required: true,
              disabled: obj.freeze,
              type: "text",
              name: "firstName",
              value: obj.customer.firstName,
            },
          }),
          new InputBoxModel({
            icon: "fa fa-user",
            onChange: (value) => (obj.customer.lastName = value),
            inputProps: {
              placeholder: "Prénoms",
              required: true,
              disabled: obj.freeze,
              type: "text",
              name: "lastName",
              value: obj.customer.lastName,
            },
          }),
        ],
      }),
      new InputGroupModel({
        inputs: [
          new InputBoxModel({
            icon: "fa fa-phone",
            onChange: (value) => (obj.customer.phone = value),
            inputProps: {
              placeholder: "Téléphone",
              required: true,
              type: "tel",
              disabled: obj.freeze,
              name: "phone",
              value: obj.customer.phone,
            },
          }),
        ],
      }),
      new InputGroupModel({
        inputs: [
          new InputBoxModel({
            icon: "fa fa-institution",
            onChange: (value) => (obj.customer.city = value),
            inputProps: {
              placeholder: "Ville",
              required: true,
              type: "city",
              name: "city",
              disabled: obj.freeze,
              value: obj.customer.city,
            },
          }),
        ],
      }),
    ];
  }
}

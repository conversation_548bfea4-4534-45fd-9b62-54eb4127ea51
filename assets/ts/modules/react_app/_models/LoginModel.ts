import { cloneDeep } from "lodash";
import { Account } from "../_entities";
import { utils } from "../_helpers";
import { AlertInterface, LoginState, ModelOptions } from "../_interfaces";
import { InputGroupModel } from "./InputGroupModel";
import { LoginFieldsModel } from "./LoginFieldsModel";

export class LoginModel {
  private static _instance: LoginModel | undefined;

  username: string;
  password: string;

  freeze: boolean;
  step: number;
  alerts: AlertInterface[];

  component: any;

  constructor(options?: ModelOptions) {
    if (LoginModel._instance) {
      throw new Error(
        "Error : Instanciation failed : Use LoginModel.getInstance() instead of new."
      );
    }

    this.alerts = [];
    this.initOptions(options);
    LoginModel._instance = this;
  }

  public static getInstance(options?: ModelOptions): LoginModel {
    if (!LoginModel._instance) {
      return new LoginModel(options);
    }
    return LoginModel._instance;
  }

  public get loginFields(): InputGroupModel[] {
    return LoginFieldsModel.getLoginFields(this);
  }

  public get loginError(): any {
    if (this.alerts.length) {
      const i = this.alerts.findIndex(
        (a) => a.id === "process_login_pequest_error"
      );
      if (i > -1) {
        const err = cloneDeep(this.alerts[i].message);
        this.alerts.splice(i, 1);
        return err;
      }
    }
    return null;
  }

  public reset(): LoginModel {
    LoginModel._instance = undefined;
    return LoginModel.getInstance();
  }

  clone() {
    return cloneDeep(this);
  }

  initOptions(options?: ModelOptions) {
    if (options) {
      const { freeze, component, step } = options;
      this.freeze = freeze ?? false;
      this.step = step ?? 1;
      this.component = component ?? null;
    }
  }

  rebuildView(newState?: LoginState) {
    if (this.component) {
      this.component.refresh(newState);
    }
  }

  async submitLogin() {
    this.freeze = true;
    this.alerts = [];
    this.rebuildView();
    const res = await new Account().processLogin(this.username, this.password);
    if (res?.token) {
      sessionStorage.setItem("jwttoken", res.token);
      this.step = 2;
      setTimeout(() => {
        utils().goTo("/admin");
      }, 2000);
    } else {
      const message =
        res?.status === 401
          ? res?.message === "inactive"
            ? "Votre compte utilisateur est inactif"
            : "Nom d'utilisateur ou mot de passe incorrect !"
          : "Une erreur s'est produite lors du paiement. Merci de reéssayer plus tard.";
      this.alerts.push({
        id: "process_login_pequest_error",
        type: "error",
        message,
      });
    }
    this.freeze = false;
    this.rebuildView();
  }
}

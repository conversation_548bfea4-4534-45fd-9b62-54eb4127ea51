import { cloneDeep } from "lodash";
import { Customer, PaymentCreateInterface, PaymentRequest } from "../_entities";
import {
  AlertInterface,
  ModelOptions,
  PaymentState,
  PayMethodItemProps,
} from "../_interfaces";
import { PayerInfosModel } from "./PayerInfosModel";
import { InputGroupModel } from "./InputGroupModel";
import { PayMethodItemModel } from "./PayMethodItemModel";

export class PaymentModel {
  private static _instance: PaymentModel | undefined;

  step: number;
  token: string;
  freeze: boolean;
  customer: Customer;
  paymentRequest: PaymentRequest;
  selectedPayMethodItemId: string;
  alerts: AlertInterface[];

  component: any;

  constructor(options?: ModelOptions) {
    if (PaymentModel._instance) {
      throw new Error(
        "Error : Instanciation failed : Use PaymentModel.getInstance() instead of new."
      );
    }

    this.alerts = [];
    this.customer = new Customer();
    this.selectedPayMethodItemId = "mtn";
    this.initOptions(options);
    PaymentModel._instance = this;
  }

  public static getInstance(options?: ModelOptions): PaymentModel {
    if (!PaymentModel._instance) {
      return new PaymentModel(options);
    }
    return PaymentModel._instance;
  }

  public reset(): PaymentModel {
    PaymentModel._instance = undefined;
    return PaymentModel.getInstance();
  }

  clone() {
    return cloneDeep(this);
  }

  public get payMethodItems(): PayMethodItemProps[] {
    return this.payMethodItemModels.map((p) => p.toJson());
  }

  public get selectedPayMethodItem(): PayMethodItemProps | undefined {
    const item = this.payMethodItemModels.find(
      (p) => p.methodId === this.selectedPayMethodItemId
    );
    if (!item) {
      return undefined;
    }

    return item.toJson();
  }

  public get fatalError(): any {
    if (this.alerts.length) {
      const i = this.alerts.findIndex(
        (a) => a.id === "init_payment_pequest_error"
      );
      if (i > -1) {
        return this.alerts[i].message;
      }
    }
    return null;
  }

  public get payError(): any {
    if (this.alerts.length) {
      const i = this.alerts.findIndex(
        (a) => a.id === "process_payment_pequest_error"
      );
      if (i > -1) {
        return this.alerts[i].message;
      }
    }
    return null;
  }

  public get payerInfosFields(): InputGroupModel[] {
    return PayerInfosModel.getPayerInfosFields(this);
  }

  public get payMethodItemModels(): PayMethodItemModel[] {
    return PayMethodItemModel.getPayMethodItemModels(this);
  }

  public get statusCallout(): string {
    if (!this.isPaymentEnded) {
      if (this.paymentRequest.paymentMethod === "mtn") {
        return "Merci de valider votre paiement en composant le *126#.";
      } else if (this.paymentRequest.paymentMethod === "orange") {
        return "Merci de valider votre paiement depuis la fenêtre Orange Money ouverte. Si aucune fenêtre ne s'est ouverte, cliquez sur le bouton ci-dessus pour l'ouvrir";
      }
    }
    return "";
  }

  public get initialTimer(): any {
    if (this.isPaymentEnded) {
      return undefined;
    }
    return 10000;
  }

  public get isPaymentEnded(): boolean {
    const { paymentRequest } = this;
    if (
      paymentRequest.status !== "INITIATED" &&
      paymentRequest.status !== "PENDING"
    ) {
      return true;
    }
    return false;
  }

  public get statusLabel(): string {
    if (this.isPaymentEnded) {
      const { paymentRequest } = this;
      if (
        paymentRequest.status === "SUCCESSFUL" ||
        paymentRequest.status === "SUCCESS"
      ) {
        return "Paiement réussi.";
      } else {
        return "Erreur lors du paiement. Veuillez recommencer la réservation via l'application ou réessayer avec un autre moyen de paiement.";
      }
    }
    return "Paiement initié.";
  }

  public get statusSubLabel(): string {
    if (this.isPaymentEnded) {
      const { paymentRequest } = this;
      if (
        paymentRequest.status === "SUCCESSFUL" ||
        paymentRequest.status === "SUCCESS"
      ) {
        return "Nous vous remercions pour votre paiement. Vous recevrez un sms de confirmation de paiement.";
      } else {
        return "Merci de reprendre votre réservation ou contacter le support client en soumettant une requête si vous éprouvez des difficultés à payer.";
      }
    }
    console.log("this.paymentRequest == ", this.paymentRequest);
    return "En attente de validation ...";
  }

  public get isPaymentSuccess(): boolean {
    if (this.isPaymentEnded) {
      const { paymentRequest } = this;
      if (
        paymentRequest.status === "SUCCESSFUL" ||
        paymentRequest.status === "SUCCESS"
      ) {
        return true;
      }
    }
    return false;
  }

  public get statusLabelClass(): string {
    if (this.isPaymentEnded) {
      if (this.isPaymentSuccess) {
        return "text-success";
      }
      return "text-danger";
    }
    return "";
  }

  public get statusSubLabelClass(): string {
    if (this.isPaymentEnded) {
      if (this.isPaymentSuccess) {
        return "text-info font-weight-bold";
      }
      return "text-danger";
    }
    return "text-danger";
  }

  initOptions(options?: ModelOptions) {
    if (options) {
      const { freeze, token, step, component } = options;
      this.token = token ?? "";
      this.freeze = freeze ?? false;
      this.step = step ?? 0;
      this.component = component ?? null;
    }
  }

  async initPaymentRequest() {
    try {
      this.paymentRequest = await new PaymentRequest().loadFromToken(
        this.token
      );
      this.step = this.paymentRequest?.status !== "INITIATED" ? 2 : 1;
    } catch (error) {
      console.error(error);
      this.alerts.push({
        id: "init_payment_pequest_error",
        type: "error",
        message: "Impossible d'initialiser la requete de paiement. Veuillez supprimer cette réservation de votre liste d'activités réservées et recommencer, ou réessayer avec un autre mode de paiement.",
      });
    }
  }

  rebuildView(newState?: PaymentState) {
    if (this.component) {
      this.component.refresh(newState);
    }
  }

  changeSelectedPayMethodItemId(methodId: string) {
    let i = this.payMethodItemModels.findIndex(
      (p) => p.methodId === this.selectedPayMethodItemId
    );
    if (i > -1) {
      this.payMethodItemModels[i].checked = false;
    }
    this.selectedPayMethodItemId = methodId;
    i = this.payMethodItemModels.findIndex((p) => p.methodId === methodId);
    if (i > -1) {
      this.payMethodItemModels[i].checked = true;
    }
    this.rebuildView();
  }

  async submitPayment() {
    this.freeze = true;
    this.rebuildView();
    const payload: PaymentCreateInterface = {
      paymentRequest: this.paymentRequest.id,
      methodId: this.selectedPayMethodItemId,
      customer: this.customer.toJson(),
    };
    try {
      console.log("payload ==> ", this.customer.toJson());
      this.paymentRequest = await this.paymentRequest.processPayment(payload);
      this.step = 2;
    } catch (error) {
      console.error(error);
      this.alerts.push({
        id: "process_payment_pequest_error",
        type: "error",
        message:
          "Votre transaction a expiré ou n'est plus valide. Veuillez fermer cette page puis supprimer la réservation et réserver de nouveau.",
      });
    }
    this.freeze = false;
    this.rebuildView();
    if (this.selectedPayMethodItemId === "orange" && this.step === 2) {
      setTimeout(() => {
        this.launchOrangeMoneyPopup();
      }, 500);
    }
  }

  launchOrangeMoneyPopup() {
    const w = window.screen.width;
    const h = window.screen.height;
    let ww = 0,
      hh = 0;
    let w1 = w - 500;
    let h1 = h - 600;
    if (w1 > 0) {
      ww = parseInt((w1 / 2).toString());
    }
    if (h1 > 0) {
      hh = parseInt((h1 / 2).toString());
    }
    const params = `scrollbars=no,resizable=no,status=no,location=no,toolbar=no,menubar=no,width=500,height=600,left=${ww},top=${hh}`;
    const popupWindow = window.open(
      this.paymentRequest.orangePayUrl,
      "sportaabe",
      params
    );
    this.rebuildView({ popupWindow, payModel: this });
  }

  async checkPaymentStatus() {
    try {
      console.log("==> checking payment status at ", new Date());
      await this.paymentRequest.checkPaymentStatus();
      if (this.isPaymentEnded) {
        this.rebuildView();
      }
    } catch (error) {
      console.error(error);
    }
  }
}

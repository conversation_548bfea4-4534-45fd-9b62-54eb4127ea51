import { InputGroupModel } from "./InputGroupModel";
import { InputBoxModel } from "./InputBoxModel";

export class LoginFieldsModel {
  /**
   * getPayerInfosFields
   */
  public static getLoginFields(obj: any): InputGroupModel[] {
    return [
      new InputGroupModel({
        inputs: [
          new InputBoxModel({
            icon: "fa fa-user",
            onChange: (value) => (obj.username = value),
            inputProps: {
              placeholder: "Nom d'utilisateur",
              required: true,
              disabled: obj.freeze,
              type: "text",
              name: "username",
              value: obj.username,
            },
          }),
        ],
      }),
      new InputGroupModel({
        inputs: [
          new InputBoxModel({
            icon: "fa fa-lock",
            onChange: (value) => (obj.password = value),
            inputProps: {
              placeholder: "Mot de passe",
              required: true,
              type: "password",
              disabled: obj.freeze,
              name: "password",
              value: obj.password,
            },
          }),
        ],
      }),
    ];
  }
}

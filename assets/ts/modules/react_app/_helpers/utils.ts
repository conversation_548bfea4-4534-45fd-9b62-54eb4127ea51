import mitt, { Emitter } from "mitt";
import ReactDOMServer from "react-dom/server";
import moment from "moment";
import { BaseModel } from "../_models";
import { restClient } from "./restClient";
import { appConstants } from "../_constants";
moment.locale("fr");

class Utils {
  private static _instance: Utils | undefined;
  public emitter: Emitter = mitt();

  constructor() {
    if (Utils._instance) {
      throw new Error(
        "Error : Instanciation failed : Use Utils.getInstance() instead of new."
      );
    }
    Utils._instance = this;
  }

  public static getInstance(): Utils {
    if (!Utils._instance) {
      return new Utils();
    }
    return Utils._instance;
  }

  public static reset() {
    Utils._instance = undefined;
  }

  getNumberInputValue(e: React.ChangeEvent<HTMLInputElement>): number {
    let mt = parseFloat(e.target.value);
    if (mt >= 1) {
      e.target.value = e.target.value.replace(/^0+/, "");
      mt = parseFloat(e.target.value);
    }
    if (!(typeof mt === "number" && mt > 0)) {
      mt = 0;
    }
    return mt;
  }

  changePageTitle(newTitle: string) {
    let title = document.getElementsByTagName("title")[0];
    title.innerHTML = `${newTitle} | sportaabe `;
  }

  ajustBody(reset: boolean = true) {
    let body = document.getElementsByTagName("body")[0];
    if (reset) {
      body.classList.remove("modal-open");
      body.style.paddingRight = "0px";
    } else {
      body.classList.add("modal-open");
      body.style.paddingRight = "15px";
    }
  }

  isActiveSidebarItem(item: any) {
    const { pathname } = window.location;
    if (item.path === pathname) {
      return true;
    }
    if (item?.subItems?.length) {
      const { subItems } = item;
      for (let index = 0; index < subItems.length; index++) {
        const element = subItems[index];
        if (element.path === pathname) {
          return true;
        }
      }
    }
    if (
      Array.isArray(item?.relatedPaths) &&
      item?.relatedPaths?.indexOf(pathname) !== -1
    ) {
      return true;
    }
    return false;
  }

  render(component: any) {
    return ReactDOMServer.renderToString(component);
  }

  formatDate(format: string, dt: string) {
    return moment(dt).format(format);
  }

  formatDouble(val: any) {
    let v = val || 0;
    try {
      v = parseFloat(val.toString());
    } catch (error) {
      v = 0;
    }

    if (isNaN(v)) {
      v = 0;
    }
    return `${v
      .toFixed(2)
      .toString()
      .replace(/\B(?=(\d{3})+(?!\d))/g, " ")}`;
  }

  formatInt(val: any) {
    let v = val || 0;
    try {
      v = parseInt(val.toString());
    } catch (error) {
      v = 0;
    }

    if (isNaN(v)) {
      v = 0;
    }
    return `${v.toString().replace(/\B(?=(\d{3})+(?!\d))/g, " ")}`;
  }

  formatAmount(curr: string, val: any) {
    return `${this.formatDouble(val)} ${curr}`;
  }

  goTo(url: string) {
    window.location.assign(url);
  }

  getUserPath(user: any) {
    let path = "/";
    if (!user) {
      return path;
    }
    const profil = user?.profil || user?.user?.profil || "User";
    const accountTypeId = user?.accountType?.accountTypeId || 0;
    if (profil === "Admin") {
      path = "/admin";
    } else if (accountTypeId < 4) {
      path = "/dashboard";
    } else if (accountTypeId === 4) {
      path = "/entreprise";
    } else if (accountTypeId === 5) {
      path = "/marchand";
    } else if (accountTypeId === 6) {
      path = "/supermarchand";
    } else if (accountTypeId === 7) {
      path = "/admin/financier";
    } else if (accountTypeId === 8) {
      path = "/admin/eRecette";
    }
    return path;
  }

  checkAccess(user: any, path: string) {
    if (user) {
      if (path.indexOf(this.getUserPath(user)) !== -1) {
        return true;
      }
    }
    return false;
  }

  rand(arg: string | number | any[]) {
    if (Array.isArray(arg)) {
      return arg[this.rand(arg.length)];
    } else if (typeof arg === "number") {
      return Math.floor(Math.random() * arg);
    } else {
      return 4;
    }
  }

  getUserAccountId() {
    return BaseModel.getInstance().user?.accountId || 0;
  }

  async resolveKpis(kpis: any[], props: any) {
    const data: any[] = [];
    kpis.forEach((kpi) => {
      if (Array.isArray(kpi.deps)) {
        kpi.deps.forEach((dep: any) => {
          const i = data.findIndex((d) => d.key === dep.key);
          if (i < 0) {
            data.push(dep);
          }
        });
      }
    });
    const res = await Promise.all(
      data.map(async (dep: any) => {
        const path =
          typeof dep.path === "function" ? dep.path(props) : dep.path;
        const result = await restClient.get(path);
        return { dep, result };
      })
    );
    kpis.forEach((kpi) => {
      const deps: any = { data: [] };
      if (Array.isArray(kpi.deps)) {
        kpi.deps.forEach((dep: any) => {
          const i = res.findIndex((d) => d.dep.key === dep.key);
          if (i > -1) {
            deps.data.push(res[i]);
          }
        });
        const value = kpi.resolve(deps);
        this.emitter.emit(kpi.key, { value, error: false });
      }
    });
  }

  showLoader() {
    this.emitter.emit(appConstants.keys.eventKeys.FULL_LOADING_ACTION, true);
  }
  hideLoader() {
    this.emitter.emit(appConstants.keys.eventKeys.FULL_LOADING_ACTION, false);
  }

  getUrlParam(key: string): any {
    return this.getUrlParams().get(key) || "";
  }

  getUrlParams(): URLSearchParams {
    const parts = window.location.href.split("?");
    const search = parts[1] || "";
    const query = new URLSearchParams(search);
    return query;
  }

  isProfil() {
    return window.location.pathname.indexOf("/profile") !== -1;
  }
}

export { Utils };

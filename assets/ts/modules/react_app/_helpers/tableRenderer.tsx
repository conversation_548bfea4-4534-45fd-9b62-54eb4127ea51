import React from "react";
import { utils } from ".";
import { Field } from "../_entities";
import { RenderA<PERSON>, RenderOuiNon, RenderStatus } from "../_components";
import { appConstants } from "../_constants";

const defaultString = "<b>-</b>";

const {
  RENDER_ID,
  RENDER_STATUS,
  RENDER_DATETIME,
  RENDER_DATE,
  RENDER_AMOUNT,
  RENDER_LONG_TEXT,
  RENDER_ACTIONS,
  RENDER_OUI_NON,
  RENDER_DOUBLE,
  RENDER_AMOUNT_ECRITURE,
} = appConstants.keys.renderKeys;

class TableRenderer {
  private static _instance: TableRenderer | undefined;

  constructor() {
    if (TableRenderer._instance) {
      throw new Error(
        "Error : Instanciation failed : Use TableRenderer.getInstance() instead of new."
      );
    }
    TableRenderer._instance = this;
  }

  public static getInstance(): TableRenderer {
    if (!TableRenderer._instance) {
      return new TableRenderer();
    }
    return TableRenderer._instance;
  }

  public static reset() {
    TableRenderer._instance = undefined;
  }

  renderId() {
    return (data: any, type: any, row: any, meta: any) => {
      if (type === "display") {
        return (
          '<span data-id="' + data + '">' + (parseInt(meta.row) + 1) + "</span>"
        );
      } else {
        return data;
      }
    };
  }

  renderText() {
    return (data: any, type: any, row: any, meta: any) => {
      if (type === "display") {
        if (data) {
          return data;
        } else {
          return defaultString;
        }
      }
      return data;
    };
  }

  renderStatus() {
    return (data: any, type: any, row: any, meta: any) => {
      if (type === "display") {
        return utils().render(<RenderStatus data={data} />);
      } else {
        return data;
      }
    };
  }

  renderOuiNon() {
    return (data: any, type: any, row: any, meta: any) => {
      if (type === "display") {
        return utils().render(<RenderOuiNon data={data} />);
      } else {
        return data;
      }
    };
  }

  renderDateTime() {
    return (data: any, type: any, row: any, meta: any) => {
      if (type === "display") {
        if (data) {
          return utils().formatDate("DD/MM/YYYY [à] H:mm:ss", data);
        } else {
          return defaultString;
        }
      } else {
        return data;
      }
    };
  }

  renderDate() {
    return (data: any, type: any, row: any, meta: any) => {
      if (type === "display") {
        if (data) {
          return utils().formatDate("DD/MM/YYYY", data);
        } else {
          return defaultString;
        }
      } else {
        return data;
      }
    };
  }

  renderLongText(field?: Field) {
    return (data: any, type: any, row: any, meta: any) => {
      if (type === "display") {
        const cuttoff = field?.customData?.renderOptions?.cuttoff || 30;
        return data
          ? `<span title="${data}">${data.substring(0, cuttoff)} ... </span>`
          : defaultString;
      } else {
        return data;
      }
    };
  }

  renderAmount() {
    return (data: any, type: any, row: any, meta: any) => {
      return utils().formatAmount("FCFA", data);
    };
  }

  renderAmountEcriture() {
    return (data: any, type: any, row: any, meta: any) => {
      const mt = utils().formatAmount("FCFA", data);
      if (type === "display") {
        if (row.type === "debit") {
          return `<span class="text-danger">-${mt}</span>`;
        } else if (row.type === "credit") {
          return `<span class="text-success">+${mt}</span>`;
        }
      } else {
        return mt;
      }
    };
  }

  renderActions(field?: Field) {
    return (data: any, type: any, row: any, meta: any) => {
      if (type === "display") {
        const actions = field?.customData?.actions || [];
        return utils().render(
          <RenderActions actions={actions} id={data} data={row} field={field} />
        );
      } else {
        return data;
      }
    };
  }

  renderDouble() {
    return (data: any, type: any, row: any, meta: any) => {
      if (type === "display") {
        return utils().formatDouble(data);
      } else {
        return data;
      }
    };
  }

  public getRenderer(renderKey?: string, field?: Field) {
    switch (renderKey) {
      case RENDER_ID:
        return this.renderId();
      case RENDER_STATUS:
        return this.renderStatus();
      case RENDER_DATETIME:
        return this.renderDateTime();
      case RENDER_DATE:
        return this.renderDate();
      case RENDER_AMOUNT:
        return this.renderAmount();
      case RENDER_AMOUNT_ECRITURE:
        return this.renderAmountEcriture();
      case RENDER_LONG_TEXT:
        return this.renderLongText(field);
      case RENDER_ACTIONS:
        return this.renderActions(field);
      case RENDER_OUI_NON:
        return this.renderOuiNon();
      case RENDER_DOUBLE:
        return this.renderDouble();
      default:
        return this.renderText();
    }
  }
}

export { TableRenderer };

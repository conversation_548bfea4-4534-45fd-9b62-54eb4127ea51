import React, { Component, Suspense, lazy } from "react";
import { Router, Route, Switch, Redirect } from "react-router-dom";
import { Loader } from "../_components";
import { history } from "../_helpers";

import { PropsFromRedux, connector } from "../_reducers";

const Payment = lazy(() => import("../pages/Payment"));

interface AppState {
  loading: boolean;
}

class App extends Component<PropsFromRedux, AppState> {
  constructor(props: PropsFromRedux) {
    super(props);
    this.state = {
      loading: false,
    };
  }

  render() {
    return (
      <>
        <Loader />
        {/* <header>
          <a id="logo" href="https://github.com/nelmio/NelmioApiDocBundle">
            <img
              src="/build/images/header-logosize-3.png"
              alt="sportaabe "
            />
          </a>
        </header> */}
        <Router history={history}>
          <Suspense fallback={<Loader />}>
            <Switch>
              <Route
                path="/public/payments/:token"
                component={Payment as any}
              />
              <Route
                exact
                path="/notfound"
                component={() => <h1>Not found !</h1>}
              />
              <Redirect from="*" to="/notfound" />
            </Switch>
          </Suspense>
        </Router>
      </>
    );
  }
}

const connected = connector<any>(App);
export { connected as App };

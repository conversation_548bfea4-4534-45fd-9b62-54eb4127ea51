FROM php:8.2.20-apache

ADD --chmod=0755 https://github.com/mlocati/docker-php-extension-installer/releases/latest/download/install-php-extensions /usr/local/bin/

RUN install-php-extensions pdo_mysql intl

RUN curl -sSk https://getcomposer.org/installer | php -- --disable-tls && \
    mv composer.phar /usr/local/bin/composer

# Install Symfony CLI
RUN curl -sS https://get.symfony.com/cli/installer | bash && \
    mv /root/.symfony*/bin/symfony /usr/local/bin/symfony

RUN apt update && apt install -yqq nodejs npm zip

COPY . /var/www/
COPY ./docker/apache.conf /etc/apache2/sites-available/000-default.conf

RUN cd /var/www/ 

CMD bash -c "composer install --ignore-platform-reqs && npm install --force && npm run build" 

WORKDIR /var/www/

ENTRYPOINT [ "bash","./docker/docker.sh" ]

EXPOSE 80
# Tournament Management System

This document describes the tournament management system that has been implemented, replacing the Match entity with Encounter entity.

## Overview

The system allows:
- **<PERSON><PERSON>** to create tournaments and add companies
- **Admins** to create encounters between teams of the same category
- **Company owners** to manage players for their teams
- **Admins** to add goals, yellow cards, and red cards to encounters with player tracking
- **All users** to view tournament statistics

## Key Entities

### 1. Tournament
- **Location**: `src/Entity/Tournament/Tournament.php`
- **Table**: `tournaments`
- **Features**:
  - Name, description, category, start/end dates
  - Location, rules, max teams
  - Status (DRAFT, ACTIVE, ONGOING, COMPLETED)
  - Many-to-many relationship with companies
  - One-to-many relationship with encounters

### 2. Encounter (replaces Match)
- **Location**: `src/Entity/Tournament/Encounter.php`
- **Table**: `encounters`
- **Features**:
  - Home team vs Away team
  - Scores, encounter date, location
  - Status (SCHEDULED, ONGOING, COMPLETED, CANCELLED)
  - Category validation (teams must be same category)
  - Belongs to tournament (optional)
  - One-to-many relationship with encounter events

### 3. EncounterEvent
- **Location**: `src/Entity/Tournament/EncounterEvent.php`
- **Table**: `encounter_events`
- **Features**:
  - Types: GOAL, YELLOW_CARD, RED_CARD, ASSIST
  - Player who performed the action
  - Minute of the event
  - Optional assist player for goals
  - Automatically updates player statistics

### 4. Team
- **Location**: `src/Entity/Team/Team.php`
- **Table**: `teams`
- **Features**:
  - Name, category
  - Belongs to a company
  - One-to-many relationship with players

## Services

### 1. TournamentService
- **Interface**: `src/Service/Tournament/Interface/ITournamentService.php`
- **Implementation**: `src/Service/Tournament/Implementation/TournamentService.php`
- **Features**:
  - CRUD operations for tournaments (admin only)
  - Add/remove companies to tournaments
  - Tournament statistics and standings

### 2. EncounterService
- **Interface**: `src/Service/Tournament/Interface/IEncounterService.php`
- **Implementation**: `src/Service/Tournament/Implementation/EncounterService.php`
- **Features**:
  - CRUD operations for encounters (admin only)
  - Add events (goals, cards) to encounters
  - Automatic score calculation from goal events
  - Automatic player statistics updates

### 3. TeamService
- **Interface**: `src/Service/Team/Interface/ITeamService.php`
- **Implementation**: `src/Service/Team/Implementation/TeamService.php`
- **Features**:
  - CRUD operations for teams
  - Company owners can manage their own teams
  - Team statistics and player listings

## Controllers

### 1. TournamentController
- **Location**: `src/Controller/Api/Tournament/TournamentController.php`
- **Routes**: `/api/v2/tournaments/*`
- **Endpoints**:
  - `POST /create` - Create tournament (admin)
  - `GET /list` - List tournaments
  - `GET /show/{id}` - Get tournament details
  - `PUT /update/{id}` - Update tournament (admin)
  - `DELETE /delete/{id}` - Delete tournament (admin)
  - `POST /add-company` - Add company to tournament (admin)
  - `POST /remove-company` - Remove company from tournament (admin)
  - `GET /statistics/{id}` - Get tournament statistics
  - `GET /standings/{id}` - Get tournament standings

### 2. EncounterController
- **Location**: `src/Controller/Api/Tournament/EncounterController.php`
- **Routes**: `/api/v2/encounters/*`
- **Endpoints**:
  - `POST /create` - Create encounter (admin)
  - `GET /list` - List encounters
  - `GET /show/{id}` - Get encounter details
  - `PUT /update/{id}` - Update encounter (admin)
  - `DELETE /delete/{id}` - Delete encounter (admin)
  - `POST /add-event` - Add event to encounter (admin)
  - `GET /upcoming` - Get upcoming encounters
  - `GET /completed` - Get completed encounters
  - `GET /team/{teamId}` - Get encounters by team
  - `GET /tournament/{tournamentId}` - Get encounters by tournament

### 3. TeamController
- **Location**: `src/Controller/Api/Team/TeamController.php`
- **Routes**: `/api/v2/teams/*`
- **Endpoints**:
  - `POST /create` - Create team
  - `GET /list` - List teams
  - `GET /show/{id}` - Get team details
  - `PUT /update/{id}` - Update team
  - `DELETE /delete/{id}` - Delete team
  - `GET /company/{companyId}` - Get teams by company
  - `GET /category/{category}` - Get teams by category
  - `GET /statistics/{id}` - Get team statistics
  - `GET /players/{id}` - Get team players

## Repositories

All entities have corresponding repositories with custom query methods:
- `TournamentRepository` - Tournament-specific queries
- `EncounterRepository` - Encounter-specific queries
- `EncounterEventRepository` - Event-specific queries and statistics
- `TeamRepository` - Team-specific queries

## Permissions

### Admin Users
- Create, update, delete tournaments
- Add/remove companies to tournaments
- Create, update, delete encounters
- Add events (goals, cards) to encounters
- Create teams for any company

### Company Owners
- Create, update, delete teams for their company
- Create, update, delete players for their teams
- View their company's tournaments and encounters

### All Users
- View tournaments, encounters, teams
- View statistics and standings

## Database Changes

1. **New Tables**:
   - `tournaments`
   - `encounters`
   - `encounter_events`
   - `tournament_companies` (junction table)
   - `teams`

2. **Updated Tables**:
   - `player_statistics`: Changed `match_*` fields to `encounter_*`
   - `companies`: Fixed relationship with teams

3. **Removed**:
   - `matches` table (replaced by encounters)
   - Match-related entities and services

## Usage Examples

### Create a Tournament (Admin)
```json
POST /api/v2/tournaments/create
{
    "name": "Summer Championship 2024",
    "description": "Annual summer tournament",
    "category": "Senior",
    "startDate": "2024-06-01",
    "endDate": "2024-06-30",
    "location": "Main Stadium",
    "maxTeams": 16,
    "status": "DRAFT"
}
```

### Create an Encounter (Admin)
```json
POST /api/v2/encounters/create
{
    "homeTeamId": "team-uuid-1",
    "awayTeamId": "team-uuid-2",
    "encounterDate": "2024-06-15 15:00:00",
    "location": "Stadium A",
    "tournamentId": "tournament-uuid",
    "status": "SCHEDULED"
}
```

### Add a Goal Event (Admin)
```json
POST /api/v2/encounters/add-event
{
    "encounterId": "encounter-uuid",
    "playerId": "player-uuid",
    "type": "GOAL",
    "minute": 45,
    "description": "Header from corner kick",
    "assistPlayerId": "assist-player-uuid"
}
```

This system provides a comprehensive tournament management solution with proper permissions, statistics tracking, and a clean API interface.

<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250530085014 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create tournament system tables: tournaments, encounters, encounter_events, and update player statistics';
    }

    public function up(Schema $schema): void
    {
        // Create tournaments table
        $this->addSql('CREATE TABLE tournaments (
            id INT AUTO_INCREMENT NOT NULL,
            uuid VARCHAR(36) NOT NULL,
            name VARCHAR(255) NOT NULL,
            description LONGTEXT DEFAULT NULL,
            start_date DATE NOT NULL,
            end_date DATE NOT NULL,
            category VARCHAR(255) NOT NULL,
            location VARCHAR(255) DEFAULT NULL,
            rules LONGTEXT DEFAULT NULL,
            max_teams INT DEFAULT NULL,
            status VARCHAR(50) NOT NULL DEFAULT "DRAFT",
            active TINYINT(1) NOT NULL DEFAULT 1,
            created_at DATETIME NOT NULL,
            updated_at DATETIME NOT NULL,
            UNIQUE INDEX UNIQ_E4BCFAC3D17F50A6 (uuid),
            PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');

        // Create encounters table
        $this->addSql('CREATE TABLE encounters (
            id INT AUTO_INCREMENT NOT NULL,
            uuid VARCHAR(36) NOT NULL,
            home_team_id INT NOT NULL,
            away_team_id INT NOT NULL,
            tournament_id INT DEFAULT NULL,
            home_score INT NOT NULL DEFAULT 0,
            away_score INT NOT NULL DEFAULT 0,
            encounter_date DATETIME NOT NULL,
            category VARCHAR(255) NOT NULL,
            location VARCHAR(255) DEFAULT NULL,
            status VARCHAR(50) NOT NULL DEFAULT "SCHEDULED",
            notes LONGTEXT DEFAULT NULL,
            active TINYINT(1) NOT NULL DEFAULT 1,
            created_at DATETIME NOT NULL,
            updated_at DATETIME NOT NULL,
            UNIQUE INDEX UNIQ_E4BCFAC3D17F50A6 (uuid),
            INDEX IDX_ENCOUNTERS_HOME_TEAM (home_team_id),
            INDEX IDX_ENCOUNTERS_AWAY_TEAM (away_team_id),
            INDEX IDX_ENCOUNTERS_TOURNAMENT (tournament_id),
            PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');

        // Create encounter_events table
        $this->addSql('CREATE TABLE encounter_events (
            id INT AUTO_INCREMENT NOT NULL,
            uuid VARCHAR(36) NOT NULL,
            encounter_id INT NOT NULL,
            player_id INT NOT NULL,
            assist_player_id INT DEFAULT NULL,
            type VARCHAR(50) NOT NULL,
            minute INT DEFAULT NULL,
            description LONGTEXT DEFAULT NULL,
            created_at DATETIME NOT NULL,
            updated_at DATETIME NOT NULL,
            UNIQUE INDEX UNIQ_E4BCFAC3D17F50A6 (uuid),
            INDEX IDX_ENCOUNTER_EVENTS_ENCOUNTER (encounter_id),
            INDEX IDX_ENCOUNTER_EVENTS_PLAYER (player_id),
            INDEX IDX_ENCOUNTER_EVENTS_ASSIST_PLAYER (assist_player_id),
            PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');

        // Create tournament_companies junction table
        $this->addSql('CREATE TABLE tournament_companies (
            tournament_id INT NOT NULL,
            company_id INT NOT NULL,
            INDEX IDX_TOURNAMENT_COMPANIES_TOURNAMENT (tournament_id),
            INDEX IDX_TOURNAMENT_COMPANIES_COMPANY (company_id),
            PRIMARY KEY(tournament_id, company_id)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');

        // Update player_statistics table to use encounter instead of match
        $this->addSql('ALTER TABLE player_statistics
            CHANGE match_id encounter_id VARCHAR(255) NOT NULL,
            CHANGE match_name encounter_name VARCHAR(255) NOT NULL,
            CHANGE match_date encounter_date DATETIME NOT NULL');

        // Add foreign key constraints
        $this->addSql('ALTER TABLE encounters
            ADD CONSTRAINT FK_ENCOUNTERS_HOME_TEAM FOREIGN KEY (home_team_id) REFERENCES teams (id),
            ADD CONSTRAINT FK_ENCOUNTERS_AWAY_TEAM FOREIGN KEY (away_team_id) REFERENCES teams (id),
            ADD CONSTRAINT FK_ENCOUNTERS_TOURNAMENT FOREIGN KEY (tournament_id) REFERENCES tournaments (id)');

        $this->addSql('ALTER TABLE encounter_events
            ADD CONSTRAINT FK_ENCOUNTER_EVENTS_ENCOUNTER FOREIGN KEY (encounter_id) REFERENCES encounters (id),
            ADD CONSTRAINT FK_ENCOUNTER_EVENTS_PLAYER FOREIGN KEY (player_id) REFERENCES players (id),
            ADD CONSTRAINT FK_ENCOUNTER_EVENTS_ASSIST_PLAYER FOREIGN KEY (assist_player_id) REFERENCES players (id)');

        $this->addSql('ALTER TABLE tournament_companies
            ADD CONSTRAINT FK_TOURNAMENT_COMPANIES_TOURNAMENT FOREIGN KEY (tournament_id) REFERENCES tournaments (id) ON DELETE CASCADE,
            ADD CONSTRAINT FK_TOURNAMENT_COMPANIES_COMPANY FOREIGN KEY (company_id) REFERENCES companies (id) ON DELETE CASCADE');
    }

    public function down(Schema $schema): void
    {
        // Drop foreign key constraints first
        $this->addSql('ALTER TABLE encounters DROP FOREIGN KEY FK_ENCOUNTERS_HOME_TEAM');
        $this->addSql('ALTER TABLE encounters DROP FOREIGN KEY FK_ENCOUNTERS_AWAY_TEAM');
        $this->addSql('ALTER TABLE encounters DROP FOREIGN KEY FK_ENCOUNTERS_TOURNAMENT');
        $this->addSql('ALTER TABLE encounter_events DROP FOREIGN KEY FK_ENCOUNTER_EVENTS_ENCOUNTER');
        $this->addSql('ALTER TABLE encounter_events DROP FOREIGN KEY FK_ENCOUNTER_EVENTS_PLAYER');
        $this->addSql('ALTER TABLE encounter_events DROP FOREIGN KEY FK_ENCOUNTER_EVENTS_ASSIST_PLAYER');
        $this->addSql('ALTER TABLE tournament_companies DROP FOREIGN KEY FK_TOURNAMENT_COMPANIES_TOURNAMENT');
        $this->addSql('ALTER TABLE tournament_companies DROP FOREIGN KEY FK_TOURNAMENT_COMPANIES_COMPANY');

        // Drop tables
        $this->addSql('DROP TABLE tournament_companies');
        $this->addSql('DROP TABLE encounter_events');
        $this->addSql('DROP TABLE encounters');
        $this->addSql('DROP TABLE tournaments');

        // Revert player_statistics table changes
        $this->addSql('ALTER TABLE player_statistics
            CHANGE encounter_id match_id VARCHAR(255) NOT NULL,
            CHANGE encounter_name match_name VARCHAR(255) NOT NULL,
            CHANGE encounter_date match_date DATETIME NOT NULL');
    }
}

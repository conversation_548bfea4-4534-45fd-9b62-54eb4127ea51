<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250408154354 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE player_statistics (id INT AUTO_INCREMENT NOT NULL, player_id INT NOT NULL, match_id VARCHAR(255) NOT NULL, match_name VARCHAR(255) NOT NULL, match_date DATETIME NOT NULL, goals INT DEFAULT NULL, yellow_cards INT DEFAULT NULL, red_cards INT DEFAULT NULL, assists INT DEFAULT NULL, notes LONGTEXT DEFAULT NULL, uuid CHAR(36) NOT NULL COMMENT \'(DC2Type:guid)\', created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', updated_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', UNIQUE INDEX UNIQ_BD760F1FD17F50A6 (uuid), INDEX IDX_BD760F1F99E6F5DF (player_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE players (id INT AUTO_INCREMENT NOT NULL, team_id INT NOT NULL, license_id INT NOT NULL, first_name VARCHAR(255) DEFAULT NULL, last_name VARCHAR(255) DEFAULT NULL, category VARCHAR(255) DEFAULT NULL, birth_date DATE DEFAULT NULL, photo VARCHAR(255) DEFAULT NULL, idcard_recto VARCHAR(255) DEFAULT NULL, idcard_verso VARCHAR(255) DEFAULT NULL, position VARCHAR(255) NOT NULL, jersey_number INT DEFAULT NULL, total_goals INT DEFAULT NULL, total_yellow_cards INT DEFAULT NULL, total_red_cards INT DEFAULT NULL, total_assists INT DEFAULT NULL, total_matches INT DEFAULT NULL, status VARCHAR(255) NOT NULL, uuid CHAR(36) NOT NULL COMMENT \'(DC2Type:guid)\', created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', updated_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', active TINYINT(1) DEFAULT 0 NOT NULL, UNIQUE INDEX UNIQ_264E43A6D17F50A6 (uuid), INDEX IDX_264E43A6296CD8AE (team_id), UNIQUE INDEX UNIQ_264E43A6460F904B (license_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE player_statistics ADD CONSTRAINT FK_BD760F1F99E6F5DF FOREIGN KEY (player_id) REFERENCES players (id)');
        $this->addSql('ALTER TABLE players ADD CONSTRAINT FK_264E43A6296CD8AE FOREIGN KEY (team_id) REFERENCES companies (id)');
        $this->addSql('ALTER TABLE players ADD CONSTRAINT FK_264E43A6460F904B FOREIGN KEY (license_id) REFERENCES licenses (id)');
        $this->addSql('ALTER TABLE files CHANGE progiciel progiciel TINYINT(1) NOT NULL DEFAULT 0');
        $this->addSql('ALTER TABLE licenses ADD player_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE licenses ADD CONSTRAINT FK_7F320F3F99E6F5DF FOREIGN KEY (player_id) REFERENCES players (id)');
        $this->addSql('CREATE INDEX IDX_7F320F3F99E6F5DF ON licenses (player_id)');
        $this->addSql('ALTER TABLE permissions CHANGE progiciel progiciel TINYINT(1) NOT NULL DEFAULT 0');
        $this->addSql('ALTER TABLE roles CHANGE progiciel progiciel TINYINT(1) NOT NULL DEFAULT 0');
        $this->addSql('ALTER TABLE settings CHANGE progiciel progiciel TINYINT(1) NOT NULL DEFAULT 0');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE licenses DROP FOREIGN KEY FK_7F320F3F99E6F5DF');
        $this->addSql('ALTER TABLE player_statistics DROP FOREIGN KEY FK_BD760F1F99E6F5DF');
        $this->addSql('ALTER TABLE players DROP FOREIGN KEY FK_264E43A6296CD8AE');
        $this->addSql('ALTER TABLE players DROP FOREIGN KEY FK_264E43A6460F904B');
        $this->addSql('DROP TABLE player_statistics');
        $this->addSql('DROP TABLE players');
        $this->addSql('ALTER TABLE files CHANGE progiciel progiciel TINYINT(1) DEFAULT 0 NOT NULL');
        $this->addSql('DROP INDEX IDX_7F320F3F99E6F5DF ON licenses');
        $this->addSql('ALTER TABLE licenses DROP player_id');
        $this->addSql('ALTER TABLE permissions CHANGE progiciel progiciel TINYINT(1) DEFAULT 0 NOT NULL');
        $this->addSql('ALTER TABLE roles CHANGE progiciel progiciel TINYINT(1) DEFAULT 0 NOT NULL');
        $this->addSql('ALTER TABLE settings CHANGE progiciel progiciel TINYINT(1) DEFAULT 0 NOT NULL');
    }
}

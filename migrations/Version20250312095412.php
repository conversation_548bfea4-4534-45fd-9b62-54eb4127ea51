<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250312095412 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE files CHANGE progiciel progiciel TINYINT(1) NOT NULL DEFAULT 0');
        $this->addSql('ALTER TABLE licenses ADD photo VARCHAR(255) DEFAULT NULL, ADD birth_date DATE DEFAULT NULL, ADD category VARCHAR(5) DEFAULT NULL');
        $this->addSql('ALTER TABLE permissions CHANGE progiciel progiciel TINYINT(1) NOT NULL DEFAULT 0');
        $this->addSql('ALTER TABLE roles CHANGE progiciel progiciel TINYINT(1) NOT NULL DEFAULT 0');
        $this->addSql('ALTER TABLE settings CHANGE progiciel progiciel TINYINT(1) NOT NULL DEFAULT 0');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE files CHANGE progiciel progiciel TINYINT(1) DEFAULT 0 NOT NULL');
        $this->addSql('ALTER TABLE licenses DROP photo, DROP birth_date, DROP category');
        $this->addSql('ALTER TABLE permissions CHANGE progiciel progiciel TINYINT(1) DEFAULT 0 NOT NULL');
        $this->addSql('ALTER TABLE roles CHANGE progiciel progiciel TINYINT(1) DEFAULT 0 NOT NULL');
        $this->addSql('ALTER TABLE settings CHANGE progiciel progiciel TINYINT(1) DEFAULT 0 NOT NULL');
    }
}

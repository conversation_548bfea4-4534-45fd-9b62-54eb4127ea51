<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250530160953 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add team categories table and update teams to use category relationships';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE team_categories (id INT AUTO_INCREMENT NOT NULL, name VARCHAR(255) NOT NULL, code VARCHAR(100) NOT NULL, min_age INT DEFAULT NULL, max_age INT DEFAULT NULL, max_players_per_team INT DEFAULT NULL, min_players_per_team INT DEFAULT NULL, gender VARCHAR(255) DEFAULT NULL, rules LONGTEXT DEFAULT NULL, sort_order INT NOT NULL, uuid CHAR(36) NOT NULL COMMENT \'(DC2Type:guid)\', created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', updated_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', active TINYINT(1) DEFAULT 0 NOT NULL, description LONGTEXT DEFAULT NULL, UNIQUE INDEX UNIQ_42A34AD5E237E06 (name), UNIQUE INDEX UNIQ_42A34AD77153098 (code), UNIQUE INDEX UNIQ_42A34ADD17F50A6 (uuid), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');

        // Insert default team categories (U13 to U18)
        $this->addSql("INSERT INTO team_categories (name, code, description, min_age, max_age, min_players_per_team, max_players_per_team, gender, sort_order, uuid, created_at, active) VALUES
            ('U13', 'U13', 'Under 13 category for players 12 years and under', NULL, 12, 11, 25, 'MIXED', 10, UUID(), NOW(), 1),
            ('U14', 'U14', 'Under 14 category for players 13 years and under', NULL, 13, 11, 25, 'MIXED', 20, UUID(), NOW(), 1),
            ('U15', 'U15', 'Under 15 category for players 14 years and under', NULL, 14, 11, 25, 'MIXED', 30, UUID(), NOW(), 1),
            ('U16', 'U16', 'Under 16 category for players 15 years and under', NULL, 15, 11, 25, 'MIXED', 40, UUID(), NOW(), 1),
            ('U17', 'U17', 'Under 17 category for players 16 years and under', NULL, 16, 11, 25, 'MIXED', 50, UUID(), NOW(), 1),
            ('U18', 'U18', 'Under 18 category for players 17 years and under', NULL, 17, 11, 25, 'MIXED', 60, UUID(), NOW(), 1)
        ");

        // Create a default category for existing teams that have string categories
        $this->addSql("INSERT INTO team_categories (name, code, description, sort_order, uuid, created_at, active) VALUES
            ('General', 'GENERAL', 'General category for existing teams', 5, UUID(), NOW(), 1)
        ");

        $this->addSql('ALTER TABLE encounter_events CHANGE uuid uuid CHAR(36) NOT NULL COMMENT \'(DC2Type:guid)\', CHANGE created_at created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', CHANGE updated_at updated_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE encounter_events ADD CONSTRAINT FK_5761B042D6E2FADC FOREIGN KEY (encounter_id) REFERENCES encounters (id)');
        $this->addSql('ALTER TABLE encounter_events ADD CONSTRAINT FK_5761B04299E6F5DF FOREIGN KEY (player_id) REFERENCES players (id)');
        $this->addSql('ALTER TABLE encounter_events ADD CONSTRAINT FK_5761B0425904EF7F FOREIGN KEY (assist_player_id) REFERENCES players (id)');
        $this->addSql('ALTER TABLE encounter_events RENAME INDEX uniq_e4bcfac3d17f50a6 TO UNIQ_5761B042D17F50A6');
        $this->addSql('ALTER TABLE encounter_events RENAME INDEX idx_encounter_events_encounter TO IDX_5761B042D6E2FADC');
        $this->addSql('ALTER TABLE encounter_events RENAME INDEX idx_encounter_events_player TO IDX_5761B04299E6F5DF');
        $this->addSql('ALTER TABLE encounter_events RENAME INDEX idx_encounter_events_assist_player TO IDX_5761B0425904EF7F');
        $this->addSql('ALTER TABLE encounters CHANGE uuid uuid CHAR(36) NOT NULL COMMENT \'(DC2Type:guid)\', CHANGE home_score home_score INT NOT NULL, CHANGE away_score away_score INT NOT NULL, CHANGE status status VARCHAR(50) NOT NULL, CHANGE active active TINYINT(1) DEFAULT 0 NOT NULL, CHANGE created_at created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', CHANGE updated_at updated_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE encounters ADD CONSTRAINT FK_60D6368C9C4C13F6 FOREIGN KEY (home_team_id) REFERENCES teams (id)');
        $this->addSql('ALTER TABLE encounters ADD CONSTRAINT FK_60D6368C45185D02 FOREIGN KEY (away_team_id) REFERENCES teams (id)');
        $this->addSql('ALTER TABLE encounters ADD CONSTRAINT FK_60D6368C33D1A3E7 FOREIGN KEY (tournament_id) REFERENCES tournaments (id)');
        $this->addSql('ALTER TABLE encounters RENAME INDEX uniq_e4bcfac3d17f50a6 TO UNIQ_60D6368CD17F50A6');
        $this->addSql('ALTER TABLE encounters RENAME INDEX idx_encounters_home_team TO IDX_60D6368C9C4C13F6');
        $this->addSql('ALTER TABLE encounters RENAME INDEX idx_encounters_away_team TO IDX_60D6368C45185D02');
        $this->addSql('ALTER TABLE encounters RENAME INDEX idx_encounters_tournament TO IDX_60D6368C33D1A3E7');
        $this->addSql('ALTER TABLE files CHANGE progiciel progiciel TINYINT(1) NOT NULL DEFAULT 0');
        $this->addSql('ALTER TABLE permissions CHANGE progiciel progiciel TINYINT(1) NOT NULL DEFAULT 0');
        $this->addSql('ALTER TABLE players ADD CONSTRAINT FK_264E43A6296CD8AE FOREIGN KEY (team_id) REFERENCES teams (id)');
        $this->addSql('ALTER TABLE roles CHANGE progiciel progiciel TINYINT(1) NOT NULL DEFAULT 0');
        $this->addSql('ALTER TABLE settings CHANGE progiciel progiciel TINYINT(1) NOT NULL DEFAULT 0');
        $this->addSql('ALTER TABLE teams ADD category_id INT NOT NULL, DROP category');
        $this->addSql('ALTER TABLE teams ADD CONSTRAINT FK_96C2225812469DE2 FOREIGN KEY (category_id) REFERENCES team_categories (id)');
        $this->addSql('CREATE INDEX IDX_96C2225812469DE2 ON teams (category_id)');
        $this->addSql('ALTER TABLE tournaments CHANGE uuid uuid CHAR(36) NOT NULL COMMENT \'(DC2Type:guid)\', CHANGE status status VARCHAR(50) NOT NULL, CHANGE active active TINYINT(1) DEFAULT 0 NOT NULL, CHANGE created_at created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', CHANGE updated_at updated_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\'');
        $this->addSql('ALTER TABLE tournament_companies ADD CONSTRAINT FK_52C4022B33D1A3E7 FOREIGN KEY (tournament_id) REFERENCES tournaments (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE tournament_companies ADD CONSTRAINT FK_52C4022B979B1AD6 FOREIGN KEY (company_id) REFERENCES companies (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE tournament_companies RENAME INDEX idx_tournament_companies_tournament TO IDX_52C4022B33D1A3E7');
        $this->addSql('ALTER TABLE tournament_companies RENAME INDEX idx_tournament_companies_company TO IDX_52C4022B979B1AD6');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE teams DROP FOREIGN KEY FK_96C2225812469DE2');
        $this->addSql('DROP TABLE team_categories');
        $this->addSql('ALTER TABLE encounter_events DROP FOREIGN KEY FK_5761B042D6E2FADC');
        $this->addSql('ALTER TABLE encounter_events DROP FOREIGN KEY FK_5761B04299E6F5DF');
        $this->addSql('ALTER TABLE encounter_events DROP FOREIGN KEY FK_5761B0425904EF7F');
        $this->addSql('ALTER TABLE encounter_events CHANGE uuid uuid VARCHAR(36) NOT NULL, CHANGE created_at created_at DATETIME NOT NULL, CHANGE updated_at updated_at DATETIME NOT NULL');
        $this->addSql('ALTER TABLE encounter_events RENAME INDEX idx_5761b0425904ef7f TO IDX_ENCOUNTER_EVENTS_ASSIST_PLAYER');
        $this->addSql('ALTER TABLE encounter_events RENAME INDEX idx_5761b042d6e2fadc TO IDX_ENCOUNTER_EVENTS_ENCOUNTER');
        $this->addSql('ALTER TABLE encounter_events RENAME INDEX idx_5761b04299e6f5df TO IDX_ENCOUNTER_EVENTS_PLAYER');
        $this->addSql('ALTER TABLE encounter_events RENAME INDEX uniq_5761b042d17f50a6 TO UNIQ_E4BCFAC3D17F50A6');
        $this->addSql('ALTER TABLE encounters DROP FOREIGN KEY FK_60D6368C9C4C13F6');
        $this->addSql('ALTER TABLE encounters DROP FOREIGN KEY FK_60D6368C45185D02');
        $this->addSql('ALTER TABLE encounters DROP FOREIGN KEY FK_60D6368C33D1A3E7');
        $this->addSql('ALTER TABLE encounters CHANGE home_score home_score INT DEFAULT 0 NOT NULL, CHANGE away_score away_score INT DEFAULT 0 NOT NULL, CHANGE status status VARCHAR(50) DEFAULT \'SCHEDULED\' NOT NULL, CHANGE uuid uuid VARCHAR(36) NOT NULL, CHANGE created_at created_at DATETIME NOT NULL, CHANGE updated_at updated_at DATETIME NOT NULL, CHANGE active active TINYINT(1) DEFAULT 1 NOT NULL');
        $this->addSql('ALTER TABLE encounters RENAME INDEX idx_60d6368c45185d02 TO IDX_ENCOUNTERS_AWAY_TEAM');
        $this->addSql('ALTER TABLE encounters RENAME INDEX idx_60d6368c9c4c13f6 TO IDX_ENCOUNTERS_HOME_TEAM');
        $this->addSql('ALTER TABLE encounters RENAME INDEX idx_60d6368c33d1a3e7 TO IDX_ENCOUNTERS_TOURNAMENT');
        $this->addSql('ALTER TABLE encounters RENAME INDEX uniq_60d6368cd17f50a6 TO UNIQ_E4BCFAC3D17F50A6');
        $this->addSql('ALTER TABLE files CHANGE progiciel progiciel TINYINT(1) DEFAULT 0 NOT NULL');
        $this->addSql('ALTER TABLE permissions CHANGE progiciel progiciel TINYINT(1) DEFAULT 0 NOT NULL');
        $this->addSql('ALTER TABLE players DROP FOREIGN KEY FK_264E43A6296CD8AE');
        $this->addSql('ALTER TABLE roles CHANGE progiciel progiciel TINYINT(1) DEFAULT 0 NOT NULL');
        $this->addSql('ALTER TABLE settings CHANGE progiciel progiciel TINYINT(1) DEFAULT 0 NOT NULL');
        $this->addSql('DROP INDEX IDX_96C2225812469DE2 ON teams');
        $this->addSql('ALTER TABLE teams ADD category VARCHAR(255) NOT NULL, DROP category_id');
        $this->addSql('ALTER TABLE tournament_companies DROP FOREIGN KEY FK_52C4022B33D1A3E7');
        $this->addSql('ALTER TABLE tournament_companies DROP FOREIGN KEY FK_52C4022B979B1AD6');
        $this->addSql('ALTER TABLE tournament_companies RENAME INDEX idx_52c4022b979b1ad6 TO IDX_TOURNAMENT_COMPANIES_COMPANY');
        $this->addSql('ALTER TABLE tournament_companies RENAME INDEX idx_52c4022b33d1a3e7 TO IDX_TOURNAMENT_COMPANIES_TOURNAMENT');
        $this->addSql('ALTER TABLE tournaments CHANGE status status VARCHAR(50) DEFAULT \'DRAFT\' NOT NULL, CHANGE uuid uuid VARCHAR(36) NOT NULL, CHANGE created_at created_at DATETIME NOT NULL, CHANGE updated_at updated_at DATETIME NOT NULL, CHANGE active active TINYINT(1) DEFAULT 1 NOT NULL');
    }
}

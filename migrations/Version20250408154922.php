<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250408154922 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE files CHANGE progiciel progiciel TINYINT(1) NOT NULL DEFAULT 0');
        $this->addSql('ALTER TABLE permissions CHANGE progiciel progiciel TINYINT(1) NOT NULL DEFAULT 0');
        $this->addSql('ALTER TABLE players DROP FOREIGN KEY FK_264E43A6460F904B');
        $this->addSql('DROP INDEX UNIQ_264E43A6460F904B ON players');
        $this->addSql('ALTER TABLE players DROP license_id');
        $this->addSql('ALTER TABLE roles CHANGE progiciel progiciel TINYINT(1) NOT NULL DEFAULT 0');
        $this->addSql('ALTER TABLE settings CHANGE progiciel progiciel TINYINT(1) NOT NULL DEFAULT 0');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE files CHANGE progiciel progiciel TINYINT(1) DEFAULT 0 NOT NULL');
        $this->addSql('ALTER TABLE permissions CHANGE progiciel progiciel TINYINT(1) DEFAULT 0 NOT NULL');
        $this->addSql('ALTER TABLE players ADD license_id INT NOT NULL');
        $this->addSql('ALTER TABLE players ADD CONSTRAINT FK_264E43A6460F904B FOREIGN KEY (license_id) REFERENCES licenses (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_264E43A6460F904B ON players (license_id)');
        $this->addSql('ALTER TABLE roles CHANGE progiciel progiciel TINYINT(1) DEFAULT 0 NOT NULL');
        $this->addSql('ALTER TABLE settings CHANGE progiciel progiciel TINYINT(1) DEFAULT 0 NOT NULL');
    }
}

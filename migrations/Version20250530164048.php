<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250530164048 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Update tournaments to use TeamCategory relationship instead of string category';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE files CHANGE progiciel progiciel TINYINT(1) NOT NULL DEFAULT 0');
        $this->addSql('ALTER TABLE permissions CHANGE progiciel progiciel TINYINT(1) NOT NULL DEFAULT 0');
        $this->addSql('ALTER TABLE players ADD CONSTRAINT FK_264E43A6296CD8AE FOREIGN KEY (team_id) REFERENCES teams (id)');
        $this->addSql('ALTER TABLE roles CHANGE progiciel progiciel TINYINT(1) NOT NULL DEFAULT 0');
        $this->addSql('ALTER TABLE settings CHANGE progiciel progiciel TINYINT(1) NOT NULL DEFAULT 0');
        // First add category_id as nullable and drop the old category column
        $this->addSql('ALTER TABLE tournaments ADD category_id INT DEFAULT NULL, DROP category, CHANGE uuid uuid CHAR(36) NOT NULL COMMENT \'(DC2Type:guid)\', CHANGE status status VARCHAR(50) NOT NULL, CHANGE active active TINYINT(1) DEFAULT 0 NOT NULL, CHANGE created_at created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', CHANGE updated_at updated_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\'');

        // Set default category (U13) for existing tournaments
        $this->addSql("UPDATE tournaments SET category_id = (SELECT id FROM team_categories WHERE code = 'U13') WHERE category_id IS NULL");

        // Now make category_id NOT NULL
        $this->addSql('ALTER TABLE tournaments MODIFY category_id INT NOT NULL');
        $this->addSql('ALTER TABLE tournaments ADD CONSTRAINT FK_E4BCFAC312469DE2 FOREIGN KEY (category_id) REFERENCES team_categories (id)');
        $this->addSql('CREATE INDEX IDX_E4BCFAC312469DE2 ON tournaments (category_id)');
        $this->addSql('ALTER TABLE tournament_companies ADD CONSTRAINT FK_52C4022B33D1A3E7 FOREIGN KEY (tournament_id) REFERENCES tournaments (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE tournament_companies ADD CONSTRAINT FK_52C4022B979B1AD6 FOREIGN KEY (company_id) REFERENCES companies (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE tournament_companies RENAME INDEX idx_tournament_companies_tournament TO IDX_52C4022B33D1A3E7');
        $this->addSql('ALTER TABLE tournament_companies RENAME INDEX idx_tournament_companies_company TO IDX_52C4022B979B1AD6');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE files CHANGE progiciel progiciel TINYINT(1) DEFAULT 0 NOT NULL');
        $this->addSql('ALTER TABLE permissions CHANGE progiciel progiciel TINYINT(1) DEFAULT 0 NOT NULL');
        $this->addSql('ALTER TABLE players DROP FOREIGN KEY FK_264E43A6296CD8AE');
        $this->addSql('ALTER TABLE roles CHANGE progiciel progiciel TINYINT(1) DEFAULT 0 NOT NULL');
        $this->addSql('ALTER TABLE settings CHANGE progiciel progiciel TINYINT(1) DEFAULT 0 NOT NULL');
        $this->addSql('ALTER TABLE tournament_companies DROP FOREIGN KEY FK_52C4022B33D1A3E7');
        $this->addSql('ALTER TABLE tournament_companies DROP FOREIGN KEY FK_52C4022B979B1AD6');
        $this->addSql('ALTER TABLE tournament_companies RENAME INDEX idx_52c4022b979b1ad6 TO IDX_TOURNAMENT_COMPANIES_COMPANY');
        $this->addSql('ALTER TABLE tournament_companies RENAME INDEX idx_52c4022b33d1a3e7 TO IDX_TOURNAMENT_COMPANIES_TOURNAMENT');
        $this->addSql('ALTER TABLE tournaments DROP FOREIGN KEY FK_E4BCFAC312469DE2');
        $this->addSql('DROP INDEX IDX_E4BCFAC312469DE2 ON tournaments');
        $this->addSql('ALTER TABLE tournaments ADD category VARCHAR(255) NOT NULL, DROP category_id, CHANGE status status VARCHAR(50) DEFAULT \'DRAFT\' NOT NULL, CHANGE uuid uuid VARCHAR(36) NOT NULL, CHANGE created_at created_at DATETIME NOT NULL, CHANGE updated_at updated_at DATETIME NOT NULL, CHANGE active active TINYINT(1) DEFAULT 1 NOT NULL');
    }
}

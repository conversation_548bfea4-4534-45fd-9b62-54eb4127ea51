{"doctrine/annotations": {"version": "2.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.10", "ref": "64d8583af5ea57b7afa4aba4b159907f3a148b05"}}, "doctrine/doctrine-bundle": {"version": "2.10", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.10", "ref": "f0d8c9a4da17815830aac0d63e153a940ae176bb"}, "files": ["config/packages/doctrine.yaml", "src/Entity/.gitignore", "src/Repository/.gitignore"]}, "doctrine/doctrine-fixtures-bundle": {"version": "3.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.0", "ref": "1f5514cfa15b947298df4d771e694e578d4c204d"}, "files": ["src/DataFixtures/AppFixtures.php"]}, "doctrine/doctrine-migrations-bundle": {"version": "3.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.1", "ref": "1d01ec03c6ecbd67c3375c5478c9a423ae5d6a33"}, "files": ["config/packages/doctrine_migrations.yaml", "migrations/.gitignore"]}, "friendsofsymfony/rest-bundle": {"version": "3.5", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "3.0", "ref": "3762cc4e4f2d6faabeca5a151b41c8c791bd96e5"}, "files": ["config/packages/fos_rest.yaml"]}, "jms/serializer-bundle": {"version": "5.3", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "4.0", "ref": "cc04e10cf7171525b50c18b36004edf64cb478be"}, "files": ["config/packages/jms_serializer.yaml"]}, "kreait/firebase-bundle": {"version": "5.0.0"}, "lexik/jwt-authentication-bundle": {"version": "2.19", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.5", "ref": "e9481b233a11ef7e15fe055a2b21fd3ac1aa2bb7"}, "files": ["config/packages/lexik_jwt_authentication.yaml"]}, "mailgun/mailgun-php": {"version": "3.6", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "2.3", "ref": "9aea325919644b58a53edf71d1212005e7081cad"}, "files": ["config/packages/mailgun.yaml"]}, "nelmio/api-doc-bundle": {"version": "4.12", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "3.0", "ref": "c8e0c38e1a280ab9e37587a8fa32b251d5bc1c94"}, "files": ["config/packages/nelmio_api_doc.yaml", "config/routes/nelmio_api_doc.yaml"]}, "nelmio/cors-bundle": {"version": "2.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.5", "ref": "6bea22e6c564fba3a1391615cada1437d0bde39c"}, "files": ["config/packages/nelmio_cors.yaml"]}, "php-http/discovery": {"version": "1.19", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.18", "ref": "f45b5dd173a27873ab19f5e3180b2f661c21de02"}, "files": ["config/packages/http_discovery.yaml"]}, "phpunit/phpunit": {"version": "9.6", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "9.6", "ref": "7364a21d87e658eb363c5020c072ecfdc12e2326"}, "files": [".env.test", "phpunit.xml.dist", "tests/bootstrap.php"]}, "sentry/sentry-symfony": {"version": "5.0", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "5.0", "ref": "76afa07d23e76f678942f00af5a6a417ba0816d0"}, "files": ["config/packages/sentry.yaml"]}, "symfony/console": {"version": "6.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "da0c8be8157600ad34f10ff0c9cc91232522e047"}, "files": ["bin/console"]}, "symfony/flex": {"version": "2.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "146251ae39e06a95be0fe3d13c807bcf3938b172"}, "files": [".env"]}, "symfony/framework-bundle": {"version": "6.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.2", "ref": "af47254c5e4cd543e6af3e4508298ffebbdaddd3"}, "files": ["config/packages/cache.yaml", "config/packages/framework.yaml", "config/preload.php", "config/routes/framework.yaml", "config/services.yaml", "public/index.php", "src/Controller/.gitignore", "src/Kernel.php"]}, "symfony/maker-bundle": {"version": "1.49", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "fadbfe33303a76e25cb63401050439aa9b1a9c7f"}}, "symfony/monolog-bundle": {"version": "3.10", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.7", "ref": "aff23899c4440dd995907613c1dd709b6f59503f"}, "files": ["config/packages/monolog.yaml"]}, "symfony/phpunit-bridge": {"version": "6.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.3", "ref": "01dfaa98c58f7a7b5a9b30e6edb7074af7ed9819"}, "files": [".env.test", "bin/phpunit", "phpunit.xml.dist", "tests/bootstrap.php"]}, "symfony/routing": {"version": "6.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.2", "ref": "e0a11b4ccb8c9e70b574ff5ad3dfdcd41dec5aa6"}, "files": ["config/packages/routing.yaml", "config/routes.yaml"]}, "symfony/security-bundle": {"version": "6.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.0", "ref": "8a5b112826f7d3d5b07027f93786ae11a1c7de48"}, "files": ["config/packages/security.yaml"]}, "symfony/twig-bundle": {"version": "6.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.3", "ref": "b7772eb20e92f3fb4d4fe756e7505b4ba2ca1a2c"}, "files": ["config/packages/twig.yaml", "templates/base.html.twig"]}, "symfony/uid": {"version": "6.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.2", "ref": "d294ad4add3e15d7eb1bae0221588ca89b38e558"}, "files": ["config/packages/uid.yaml"]}, "symfony/validator": {"version": "6.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "c32cfd98f714894c4f128bb99aa2530c1227603c"}, "files": ["config/packages/validator.yaml"]}, "symfony/webpack-encore-bundle": {"version": "2.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.0", "ref": "082d754b3bd54b3fc669f278f1eea955cfd23cf5"}, "files": ["assets/app.js", "assets/styles/app.css", "config/packages/webpack_encore.yaml", "package.json", "webpack.config.js"]}}
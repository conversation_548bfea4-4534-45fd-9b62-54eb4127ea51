{"dependencies": {"@fortawesome/fontawesome-free": "^5.15.2", "axios": "^0.21.0", "bootstrap": "^4.6.0", "chart.js": "^2.9.4", "encore": "^0.0.30-beta", "hamburgers": "^1.1.3", "lodash": "^4.17.20", "mitt": "^2.1.0", "moment": "^2.29.1", "react": "^17.0.1", "react-dom": "^17.0.1", "react-redux": "^7.2.2", "react-router-dom": "^5.2.0", "react-select": "^3.1.0", "react-toastr": "^3.0.0", "react-useinterval": "^1.0.2", "redux": "^4.0.5", "redux-thunk": "^2.3.0", "sass": "^1.62.0", "sweetalert": "^2.1.2"}, "devDependencies": {"@babel/core": "^7.17.0", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/preset-env": "^7.16.0", "@babel/preset-react": "^7.12.10", "@symfony/stimulus-bridge": "^1.1.0", "@symfony/webpack-encore": "^4.0.0", "@testing-library/jest-dom": "^5.11.4", "@testing-library/react": "^11.1.0", "@testing-library/user-event": "^12.1.10", "@types/axios": "^0.14.0", "@types/bootstrap": "^5.0.0", "@types/chart.js": "^2.9.30", "@types/headroom.js": "^0.11.0", "@types/jest": "^26.0.15", "@types/jquery": "^3.5.25", "@types/lodash": "^4.14.163", "@types/node": "^12.0.0", "@types/react": "^16.9.53", "@types/react-dom": "^16.9.8", "@types/react-fade-in": "^0.1.0", "@types/react-google-recaptcha": "^2.1.0", "@types/react-redux": "^7.1.9", "@types/react-router-dom": "^5.1.6", "@types/redux": "^3.6.0", "@types/redux-logger": "^3.0.8", "@types/redux-thunk": "^2.1.0", "@types/sweetalert": "^2.0.4", "core-js": "^3.23.0", "file-loader": "^6.0.0", "redux-devtools-extension": "^2.13.8", "redux-logger": "^3.0.6", "regenerator-runtime": "^0.13.9", "sass-loader": "^13.3.2", "stimulus": "^2.0.0", "ts-loader": "^9.5.0", "typescript": "^4.1.3", "web-vitals": "^0.2.4", "webpack": "^5.74.0", "webpack-cli": "^4.10.0", "webpack-notifier": "^1.15.0", "workbox-background-sync": "^5.1.3", "workbox-broadcast-update": "^5.1.3", "workbox-cacheable-response": "^5.1.3", "workbox-core": "^5.1.3", "workbox-expiration": "^5.1.3", "workbox-google-analytics": "^5.1.3", "workbox-navigation-preload": "^5.1.3", "workbox-precaching": "^5.1.3", "workbox-range-requests": "^5.1.3", "workbox-routing": "^5.1.3", "workbox-strategies": "^5.1.3", "workbox-streams": "^5.1.3"}, "license": "UNLICENSED", "private": true, "scripts": {"dev-server": "encore dev-server", "dev": "encore dev", "watch": "encore dev --watch", "build": "encore production --progress && cp -r public/build/* public/dist/"}}
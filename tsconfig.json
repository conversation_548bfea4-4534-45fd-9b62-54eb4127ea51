{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": false, "strictPropertyInitialization": false, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": false, "noEmit": false, "noImplicitAny": false, "jsx": "react-jsx"}, "include": ["assets/ts"]}
# Tournament Management System - Postman Testing Guide

This guide provides step-by-step instructions for testing the Tournament Management System using the provided Postman collection.

## Prerequisites

1. **Import the Collection**: Import `Tournament_Management_System.postman_collection.json` into Postman
2. **Set Environment Variables**: Configure the following variables in your Postman environment:
   - `base_url`: Your API base URL (e.g., `http://localhost:8000/api/v2`)
   - `auth_token`: Will be automatically set after login

## Testing Workflow

### 1. Authentication Setup

**Step 1.1: Login as Admin**
- Run: `Authentication > Login Admin`
- Update the request body with valid admin credentials
- The auth token will be automatically saved to collection variables

**Step 1.2: Login as Company Owner (Optional)**
- Run: `Authentication > Login Company Owner`
- Update the request body with valid company owner credentials

### 2. Tournament Management (Admin Only)

**Step 2.1: Create Tournament**
- Run: `Tournaments > Create Tournament (Admin)`
- Verify response contains tournament details
- Tournament ID will be automatically saved for subsequent requests

**Step 2.2: List Tournaments**
- Run: `Tournaments > Get All Tournaments`
- Verify the created tournament appears in the list
- Test filtering by status and category

**Step 2.3: Get Tournament Details**
- Run: `Tournaments > Get Tournament Details`
- Verify detailed tournament information is returned

**Step 2.4: Update Tournament**
- Run: `Tournaments > Update Tournament (Admin)`
- Verify tournament status changes to "ACTIVE"

### 3. Company and Team Management

**Step 3.1: Get Companies**
- Run: `Companies > Get All Companies`
- Company ID will be automatically saved for subsequent requests

**Step 3.2: Add Company to Tournament**
- Run: `Tournaments > Add Company to Tournament (Admin)`
- Verify company is successfully added to tournament

**Step 3.3: Create Team**
- Run: `Teams > Create Team`
- Team ID will be automatically saved for subsequent requests
- Verify team is created with correct company association

**Step 3.4: Get Team Details**
- Run: `Teams > Get Team Details`
- Verify team information and company relationship

### 4. Player Management

**Step 4.1: Create Player**
- Run: `Players > Create Player`
- Player ID will be automatically saved for subsequent requests
- Verify player is associated with the correct team

**Step 4.2: Get Player Details**
- Run: `Players > Get Player Details`
- Verify player information and team association

**Step 4.3: Update Player**
- Run: `Players > Update Player`
- Verify player information is updated correctly

### 5. Encounter Management (Admin Only)

**Step 5.1: Create Encounter**
- Run: `Encounters > Create Encounter (Admin)`
- Encounter ID will be automatically saved for subsequent requests
- Verify encounter is created with correct teams and tournament

**Step 5.2: Get Encounter Details**
- Run: `Encounters > Get Encounter Details`
- Verify encounter information and team details

**Step 5.3: Add Goal Event**
- Run: `Encounters > Add Goal Event (Admin)`
- Verify goal is recorded and player statistics are updated
- Check that encounter score is automatically updated

**Step 5.4: Add Card Events**
- Run: `Encounters > Add Yellow Card Event (Admin)`
- Run: `Encounters > Add Red Card Event (Admin)`
- Verify cards are recorded and player statistics are updated

**Step 5.5: Update Encounter**
- Run: `Encounters > Update Encounter (Admin)`
- Verify encounter details are updated

### 6. Statistics and Reports

**Step 6.1: Player Statistics**
- Run: `Players > Get Player Statistics`
- Verify statistics reflect the events added to encounters

**Step 6.2: Team Statistics**
- Run: `Teams > Get Team Statistics`
- Verify team statistics aggregate player data correctly

**Step 6.3: Tournament Statistics**
- Run: `Tournaments > Get Tournament Statistics`
- Verify tournament-level statistics

**Step 6.4: Top Scorers**
- Run: `Statistics & Reports > Get Top Scorers`
- Verify top scorers list is accurate

### 7. Additional Testing Scenarios

**Step 7.1: List Encounters by Tournament**
- Run: `Encounters > Get Encounters by Tournament`
- Verify only encounters from the specific tournament are returned

**Step 7.2: List Encounters by Team**
- Run: `Encounters > Get Encounters by Team`
- Verify only encounters involving the specific team are returned

**Step 7.3: Upcoming vs Completed Encounters**
- Run: `Encounters > Get Upcoming Encounters`
- Run: `Encounters > Get Completed Encounters`
- Verify encounters are correctly categorized by status

## Expected Test Results

### Successful Responses
- All requests should return HTTP 200 status
- Response format should follow the standard API structure:
```json
{
    "success": true,
    "message": "Success message",
    "data": { ... }
}
```

### Automatic Data Population
- Tournament ID, Team ID, Player ID, Encounter ID should be automatically captured
- Auth token should be automatically set after login
- Company ID should be captured from company list

### Data Integrity Checks
1. **Player Statistics**: After adding events, verify player totals are updated
2. **Encounter Scores**: After adding goals, verify encounter scores are calculated
3. **Team Statistics**: Verify team stats aggregate from all players
4. **Tournament Data**: Verify tournament contains correct companies and encounters

## Permission Testing

### Admin Permissions (should succeed)
- Create/update/delete tournaments
- Create/update/delete encounters
- Add events to encounters
- Create teams for any company

### Company Owner Permissions (should succeed)
- Create/update/delete teams for their company
- Create/update/delete players for their teams
- View tournaments and encounters

### Unauthorized Actions (should fail with 403)
- Non-admin trying to create tournaments
- Company owner trying to manage other companies' teams
- Non-admin trying to add events to encounters

## Troubleshooting

### Common Issues
1. **401 Unauthorized**: Check if auth token is properly set
2. **403 Forbidden**: Verify user has correct permissions for the action
3. **404 Not Found**: Ensure entity IDs are correctly captured from previous requests
4. **422 Validation Error**: Check request body format and required fields

### Debug Tips
1. Check Postman Console for variable values
2. Verify auth token is included in request headers
3. Ensure all required IDs are populated from previous requests
4. Check request body JSON format

## Test Data Cleanup

After testing, you may want to clean up test data:
1. Delete created encounters
2. Delete created players
3. Delete created teams
4. Delete created tournaments

Note: Use the delete endpoints with caution as they will permanently remove data.

## Advanced Testing

### Load Testing
- Use Postman's Collection Runner to execute multiple iterations
- Test with multiple tournaments, teams, and players
- Verify performance with large datasets

### Edge Cases
- Test with invalid UUIDs
- Test with non-existent entity references
- Test with malformed request bodies
- Test permission boundaries

This comprehensive testing approach ensures all aspects of the Tournament Management System are properly validated.

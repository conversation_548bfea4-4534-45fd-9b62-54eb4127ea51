<?php

namespace App\Service\Shared\Implementation;

use App\Constant\DefaultValues;
use App\Dto\GenericResponse;
use App\Dto\Shared\In\PatchOperationIn;
use App\Entity\Shared\Setting;
use App\Mapper\Shared\Interface\ISettingMapper;
use App\Repository\Shared\SettingRepository;
use App\Service\Traits\BaseServiceTrait;
use App\Service\Traits\LogServiceTrait;
use App\Service\Shared\Interface\ISettingService;
use Symfony\Component\HttpFoundation\Request;

class SettingService implements ISettingService
{
  use BaseServiceTrait, LogServiceTrait;

  private ISettingMapper $mapper;

  private SettingRepository $repository;

  public function __construct(ISettingMapper $settingMapper, SettingRepository $repository)
  {
    $this->mapper = $settingMapper;
    $this->repository = $repository;
  }

  public function findAll(Request $request): GenericResponse
  {
    $params = $this->mapper->fromFindAllRequest($request);
    $items = $this->repository->findAll();
    $outputs = $this->mapper->toSettingOutArray($params->getOutput(), ...$items);

    return $this->result($outputs);
  }

  public function findOne(string $uuid, Request $request): GenericResponse
  {
    $outputFormat = $request->query->get("output", DefaultValues::DEFAULT_FIND_ONE_OUTPUT_FORMAT);
    $setting = $this->repository->getByUuid($uuid);
    $output = $this->mapper->toSettingOut($outputFormat, $setting);

    return $this->result($output);
  }

  public function create(Request $request): GenericResponse
  {
    $setting = $this->mapper->fromSettingCreateRequest($request);
    $this->persist($setting);
    $outputFormat = $request->query->get("output", DefaultValues::DEFAULT_CREATE_OUTPUT_FORMAT);
    $output = $this->mapper->toSettingOut($outputFormat, $setting);

    return $this->result($output);
  }

  private function doUpdateInternal(Setting $setting, Request $request): GenericResponse
  {
    $this->persist($setting);
    $outputFormat = $request->query->get("output", DefaultValues::DEFAULT_REPLACE_OUTPUT_FORMAT);
    $output = $this->mapper->toSettingOut($outputFormat, $setting);

    return $this->result($output);
  }

  public function replace(string $uuid, Request $request): GenericResponse
  {
    $setting = $this->mapper->fromReplaceOrPatchUpdateRequest($uuid, $request);
    return $this->doUpdateInternal($setting, $request);
  }

  private function doUpdate(string $uuid, Request $request, PatchOperationIn $patchOperationIn): mixed
  {
    $setting = $this->mapper->fromReplaceOrPatchUpdateRequest($uuid, $patchOperationIn, false);
    return $this->doUpdateInternal($setting, $request);
  }

  public function update(string $uuid, Request $request): GenericResponse
  {
    $results = [];
    $operations = $this->mapper->fromUpdateRequest($uuid, $request);
    foreach ($operations as $operation) {
      $results[] = match ($operation->getType()) {
        "update" => $this->doUpdate($uuid, $request, $operation),
        default => null,
      };
    }
    return $this->result($results);
  }

  public function remove(string $uuid, Request $request): GenericResponse
  {
    $setting = $this->mapper->fromRemoveRequest($uuid, $request);
    return $this->result($this->delete($setting));
  }

  public function getByUuid(string $uuid, bool $throwIfNotFound = true): Setting|null
  {
    return $this->repository->getByUuid($uuid, $throwIfNotFound);
  }
}

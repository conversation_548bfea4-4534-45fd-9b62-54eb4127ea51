<?php

namespace App\Service\Shared\Implementation;

use App\Helper\CsvImporter;
use App\Service\Shared\Interface\ICsvService;

class CsvService implements ICsvService
{
  private string $csvDirectory;

  public function __construct(string $csvDirectory)
  {
    $this->csvDirectory = $csvDirectory;
  }

  public function csvFileToArray(string $csvFileName): array
  {
    $csvPath = $this->csvDirectory . $csvFileName;

    return (new CsvImporter($csvPath, TRUE))->get();
  }
}

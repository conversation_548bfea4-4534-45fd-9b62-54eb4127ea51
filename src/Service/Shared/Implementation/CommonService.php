<?php

namespace App\Service\Shared\Implementation;

use DateTime;
use Mailgun\Mailgun;
use Aws\Sns\SnsClient;
use DateTimeImmutable;
use App\Enum\LoginTypes;
use App\Entity\Security\User;
use App\Enum\UserStatusTypes;
use App\Entity\Chat\Discussion;
use App\Entity\Company\Company;
use App\Entity\Payment\Payment;
use App\Mapper\BaseMapperTrait;
use App\Entity\Activity\Activity;
use App\Entity\Security\UserMeta;
use App\Entity\Company\CompanyMeta;
use App\Entity\Company\Subscription;
use App\Form\Common\ProfileUpdateIn;
use App\Entity\Activity\ActivityMeta;
use App\Form\Common\ProfileUpdateType;
use Kreait\Firebase\Contract\Database;
use App\Service\Traits\LogServiceTrait;
use App\Entity\Company\SubscriptionPlan;
use App\Service\Traits\BaseServiceTrait;
use App\Constant\GenericResponseStatuses;
use App\Repository\Chat\RequestRepository;
use App\Entity\Chat\Request as ChatRequest;
use App\Repository\Security\UserRepository;
use Symfony\Bundle\SecurityBundle\Security;
use App\Repository\Shared\SettingRepository;
use App\Entity\Chat\Response as ChatResponse;
use App\Exception\RequestValidationException;
use App\Repository\Company\CompanyRepository;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Repository\Activity\ActivityRepository;
use Symfony\Component\HttpKernel\KernelInterface;
use App\Repository\Company\SubscriptionRepository;
use App\Service\Security\Implementation\UserService;
use App\Repository\Company\SubscriptionPlanRepository;
use App\Repository\Payment\LicenseRepository;
use App\Repository\Security\AuthentificationCodeRepository;

class CommonService
{
  use BaseServiceTrait, LogServiceTrait, BaseMapperTrait;

  private $appId = "0cb94376cd2260";
  private $appSecret = "2f5d91be5e1ea9e17cef70bf8499d9a1e2cb706511c261c10734f4ec9ceea27c";
  private $senderName = "sportaabe";

  public $mailGunKey = "**************************************************";
  public $mailGunDomain = "mg.afrik.sportaabe.com";

  public $mailGunSenderName = "sportaabe ";
  public $mailGunSenderMail = "<EMAIL>";

  private string $appEnv = "prod";

  private Security $security;

  private Database $firebaseRealTimeDb;

  private CompanyRepository $companyRepo;

  private ActivityRepository $activityRepo;

  private UserRepository $userRepo;
  private AuthentificationCodeRepository $otpRepository;

  private SettingRepository $settingRepo;

  private KernelInterface $appKernel;

  private SubscriptionPlanRepository $subscriptionPlanRepo;
  private SubscriptionRepository $subscriptionRepo;
  private RequestRepository $requestRepo;
  private LicenseRepository $licenseRepo;

  public function __construct(Security $security, UserRepository $userRepo, CompanyRepository $companyRepo, ActivityRepository $activityRepo, SettingRepository $settingRepo, Database $database, KernelInterface $appKernel, AuthentificationCodeRepository $otpRepository, SubscriptionPlanRepository $subscriptionPlanRepo, SubscriptionRepository $subscriptionRepo, RequestRepository $requestRepo, LicenseRepository $licenseRepo)
  {
    $this->security = $security;
    $this->userRepo = $userRepo;
    $this->companyRepo = $companyRepo;
    $this->activityRepo = $activityRepo;
    $this->firebaseRealTimeDb = $database;
    $this->settingRepo = $settingRepo;
    $this->appKernel = $appKernel;
    $this->otpRepository = $otpRepository;
    $this->subscriptionPlanRepo = $subscriptionPlanRepo;
    $this->subscriptionRepo = $subscriptionRepo;
    $this->requestRepo = $requestRepo;
    $this->licenseRepo = $licenseRepo;
  }

  public function sendMail(string $to, string $subject, string $code, string $template)
  {
    try {
      # Instantiate the client.
      $mgClient = Mailgun::create($this->mailGunKey);
      $domain = $this->mailGunDomain;
      $data = array(
        "code" => $code,
      );
      $params = array(
        'from' => $this->mailGunSenderName . ' <' . $this->mailGunSenderMail . '>',
        'to' => $to,
        'subject' => $subject,
        'template' => $template,
        'h:X-Mailgun-Variables' =>  \json_encode($data),
      );
      # Make the call to the client.
      $result = $mgClient->messages()->send($domain, $params);
      $this->logger->info('Mail sent! subject: ' . $subject . ' to: ' . $to);
    } catch (\Throwable $e) {
      $this->logger->error('Mail not sent! subject: ' . $subject . ' to: ' . $to . ' Error: ' . $e->getMessage());
      throw new RequestValidationException('Erreur lors de l\'envoi du mail: ' . $e->getMessage());
    }
  }

  public function sendSMS($phone, $msg)
  {
    $oMessage = \Camoo\Sms\Message::create($this->appId, $this->appSecret);
    $oMessage->from = $this->senderName;
    $oMessage->to = $phone;
    $oMessage->route = 'classic';
    $oMessage->message = $msg;
    try {
      $res = $oMessage->send();
      if (is_object($res)) {
        $res = $res->jsonSerialize();
      }
      $this->writeInLogFile("===== Response from SMS sending ==> " . \json_encode($res), 'INFO');
      if ($res && isset($res['_message']) && "succes" === $res['_message']) {
        $this->writeInLogFile("===== SMS sending was ==> SUCESSFUL", 'INFO');
        $this->logger->info('SMS sending was ==> SUCESSFUL');
        return true;
      }
      $this->writeInLogFile("===== SMS sending was ==> FAILURE", 'ERROR');
      return false;
    } catch (\Throwable $e) {
      $this->writeInLogFile('SMS sending Exception Message: ' . $e->getMessage(), 'ERROR');
      $this->writeInLogFile('SMS sending Exception string: ' . $e->__toString(), 'ERROR');
      return false;
    }
  }

  public function sendAwsMessage($phone, $sms): array
  {
    $sdk = new SnsClient([
      'region' => 'eu-west-1',
      'version' => 'latest',
      'credentials' => [
        'key' => '********************',
        'secret' => 'mwwYtBMHhrqu57JduP6jF0+SO3uMT/BvKQXHY0+D',
      ],
    ]);

    try {
      $result = $sdk->publish([
        'Message' => $sms,
        'PhoneNumber' => $phone,
        'MessageAttributes' => [
          'AWS.SNS.SMS.SenderID' => [
            'DataType' => 'String',
            'StringValue' => 'sportaabe',
          ],
        ]
      ]);

      // print_r($result);die;
      $this->logger->info('AWS SNS Api: ' . \json_encode($result));
      return ["ids" => [\json_encode($result)]];
    } catch (\Exception $e) {
      $this->logger->error('AWS SNS Api Error: ' . $e->getMessage());
      throw new \Exception('AWS SNS Api: ' . $e->getMessage());
    }
  }

  public function createDiscussion(Request $request, $reqActivity = null)
  {
    $body = $request->request->all();

    /**  @var \App\Entity\Security\User */
    $user = $this->security->getUser();

    $activity =  $reqActivity ?? new Discussion();
    $activity->setOwner($user);
    $activity->setCreator($user);
    $activity->setTitle($body["_instantInformation"]["_title"]);
    $activity->setShortDescription($body["_instantInformation"]["_shortDescription"]);
    $activity->setLongDescription($body["_instantInformation"]["_longDescription"]);
    $activity->setPlaceName($body["_address"]["_address"]);
    $activity->setStreetName($body["_address"]["_address"]);
    $activity->setCity($body["_address"]["_city"]);
    $activity->setZipCode(intval($body["_address"]["_zipCode"]));
    $activity->setLongitude(isset($body["_address"]["_coords"]["longitude"]) ? $body["_address"]["_coords"]["longitude"] : null);
    $activity->setLatitude(isset($body["_address"]["_coords"]["latitude"]) ? $body["_address"]["_coords"]["latitude"] : null);
    $activity->setCountry($body["_address"]["_country"]);
    $activity->setPrice(\floatval($body["_instantInformation"]["_price"]));
    $activity->setStatus('ACTIVE');
    $activity->setActive(true);

    $this->persist($activity);

    return $this->result($this->toActivityInfo($activity));
  }


  public function refreshFirebaseProfile(Request $request)
  {
    /**  @var \App\Entity\Security\User */
    $user = $this->security->getUser();

    try {
      $this->firebaseRealTimeDb->getReference('dev/profiles/' . $user->getUuid())
        ->set([
          'platform' =>  $request->request->get("platform"),
          'token' => $request->request->get("token"),
          'uuid' => $user->getUuid(),
          'is_admin' =>  $user->getMetaValueByCode('is_admin', '0'),
        ]);
    } catch (\Throwable $th) {
      $this->logger->error("Failed to refresh firebase profile for user " . $user->getUserIdentifier()." - " . $th->getMessage());
    }
    return $this->result(null);
  }

  
  


  public function cutActivityName(string $activityName)
  {
    $name = $activityName;
    if (strlen($activityName) > 13) {
      $name = substr($activityName, 0, 13 - strlen($activityName));
      $name .= "...";
    }

    return $name;
  }





  
  
  public function removePayment(mixed $id)
  {
    try {

      $sql = "DELETE FROM payments where order_id = :order_id";
      $params = array('order_id' => $id);

      $stmt = $this->em->getConnection()->prepare($sql);

      return $this->result($stmt->executeStatement($params));
    } catch (\Exception $e) {
      return $this->result(null);
    }
  }

  public function sendMail3($to, string $subject, $p, string $template = "pay-request", $revert = true)
  {
    return $this->sendMail2($subject, $p, $template, $revert, $to);
  }

  public function sendMail2(string $subject, $p, string $template = "pay-request", $revert = true, $to = null)
  {
    try {
           # Instantiate the client.
          $mgClient = Mailgun::create($this->mailGunKey);
          $domain = $this->mailGunDomain;

      if(is_array($p) && ($template === "pay-request" || $template === "pay-request-en")) {

          $dt = $p['activity_date_time'];
          $dt = date_create_from_format('d/m/Y, H:i', $dt);

          $activity = $this->activityRepo->getByUuid($p['id_activity']);


          $data = array(
            "link" => $p['pay_url'],
            "name" => $p['client_name'],
            "event_name" => $p['activity_name'],
            "amount" => $p['amount'] . ' FCFA',
            "name_org" => $p['company_name'],
            "date" => date('d/m/Y', !empty($dt) ? $dt->getTimestamp() : time()),
            "time" => date('H:i', !empty($dt) ? $dt->getTimestamp() : time()),
            "place_name" => $activity->getCountry() . ', ' . $activity->getCity() . ', ' . $activity->getStreetName(),
          );

          $params = array(
            'from' => $this->mailGunSenderName . ' <' . $this->mailGunSenderMail . '>',
            'to' => !empty($to) ? $to : $p['mail_address'],
            'subject' => $subject . " - ID #" . $p['id'],
            'template' => $template,
            'h:X-Mailgun-Variables' =>  \json_encode($data),
          );
        
      }elseif($template == "pay-subscription-request" || $template == "pay-subscription-request-en") {
          $payment_url = $p->getPayUrl();
          $name = $p->getClientName();
          $amount = $p->getAmount();
          $company = $p->getCompanyName();
          $subscription = $this->subscriptionRepo->findOneBy(['payment' => $p->getId()]);
          $plan = $subscription->getDescription();

          $data = array(
            "link" => $payment_url,
            "name" => $name,
            "amount" => $amount . ' FCFA',
            "company_name" => $company,
            "plan_name" => $plan,
          );

          $params = array(
            'from' => $this->mailGunSenderName . ' <' . $this->mailGunSenderMail . '>',
            'to' => !empty($to) ? $to : $p->getMailAddress(),
            'subject' => $subject . " - ID #" . $p->getId(),
            'template' => $template,
            'h:X-Mailgun-Variables' =>  \json_encode($data),
          );
      }elseif($template == "pay-insurance-request" || $template == "pay-insurance-request-en") {
        $payment_url = $p->getPayUrl();
        $name = $p->getClientName();
        $amount = $p->getAmount();
        $license = $this->licenseRepo->findOneBy(['payment' => $p->getId()]);
        $plan = $license->getLicensePlan()->getTitle();
        $license_name = $license->getLastName()." ".$license->getFirstName();

        $data = array(
          "link" => $payment_url,
          "name" => $name,
          "license_name" => $license_name,
          "amount" => $amount . ' FCFA',
          "plan_name" => $plan,
        );

        $params = array(
          'from' => $this->mailGunSenderName . ' <' . $this->mailGunSenderMail . '>',
          'to' => !empty($to) ? $to : $p->getMailAddress(),
          'subject' => $subject . " - ID #" . $p->getId(),
          'template' => $template,
          'h:X-Mailgun-Variables' =>  \json_encode($data),
        );
    }elseif($template == "subscription-remind" || $template == "subscription-remind-en") {

        $plan = $p->getDescription();
        $company = $p->getCompany()->getName();
        $startDate = $p->getStartDate()->format('d/m/Y H:i:s')." {$p->getStartDate()->getTimezone()->getName()}";
        $endDate = $p->getEndDate()->format('d/m/Y H:i:s')." {$p->getEndDate()->getTimezone()->getName()}";

        $data = array(
          "plan_name" => $plan,
          "company_name" => $company,
          "start_date" => $startDate,
          "end_date" => $endDate,
        );

        $params = array(
          'from' => $this->mailGunSenderName . ' <' . $this->mailGunSenderMail . '>',
          'to' => !empty($to) ? $to : $p->getPayment()->getMailAddress(),
          'subject' => $subject . " - ID #" . $p->getId(),
          'template' => $template,
          'h:X-Mailgun-Variables' =>  \json_encode($data),
        );
      }elseif($template == "insurance-remind" || $template == "insurance-remind-en") {

        $plan = $p->getLicensePlan()->getTitle();
        $name = strtoupper($p->getLastName()." ".$p->getFirstName());
        $startDate = $p->getCreatedAt()->format('d/m/Y H:i:s')." {$p->getCreatedAt()->getTimezone()->getName()}";
        $endDate = $p->getEndDate()->format('d/m/Y H:i:s')." {$p->getEndDate()->getTimezone()->getName()}";
        $client_name = $p->getPayment()->getClientName();


        $data = array(
          "plan_name" => $plan,
          "client_name" => $client_name,
          "license_name" => $name,
          "start_date" => $startDate,
          "end_date" => $endDate,
        );

        $params = array(
          'from' => $this->mailGunSenderName . ' <' . $this->mailGunSenderMail . '>',
          'to' => !empty($to) ? $to : $p->getPayment()->getMailAddress(),
          'subject' => $subject . " - ID #" . $p->getId(),
          'template' => $template,
          'h:X-Mailgun-Variables' =>  \json_encode($data),
        );
      }elseif($template == "subscription-expired" || $template == "subscription-expired-en") {

        $plan = $p->getDescription();
        $company = $p->getCompany()->getName();
        $startDate = $p->getStartDate()->format('d/m/Y H:i:s')." {$p->getStartDate()->getTimezone()->getName()}";
        $endDate = $p->getEndDate()->format('d/m/Y H:i:s')." {$p->getEndDate()->getTimezone()->getName()}";
        
        $data = array(
          "plan_name" => $plan,
          "company_name" => $company,
          "start_date" => $startDate,
          "end_date" => $endDate,
        );

        $params = array(
          'from' => $this->mailGunSenderName . ' <' . $this->mailGunSenderMail . '>',
          'to' => !empty($to) ? $to : $p->getPayment()->getMailAddress(),
          'subject' => $subject . " - ID #" . $p->getId(),
          'template' => $template,
          'h:X-Mailgun-Variables' =>  \json_encode($data),
        );
      }elseif($template == "insurance-expired" || $template == "insurance-expired-en") {

        $plan = $p->getLicensePlan()->getTitle();
        $name = strtoupper($p->getLastName()." ".$p->getFirstName());
        $startDate = $p->getCreatedAt()->format('d/m/Y H:i:s')." {$p->getCreatedAt()->getTimezone()->getName()}";
        $endDate = $p->getEndDate()->format('d/m/Y H:i:s')." {$p->getEndDate()->getTimezone()->getName()}";
        $client_name = $p->getPayment()->getClientName();
        
        $data = array(
          "plan_name" => $plan,
          "client_name" => $client_name,
          "license_name" => $name,
          "start_date" => $startDate,
          "end_date" => $endDate,
        );

        $params = array(
          'from' => $this->mailGunSenderName . ' <' . $this->mailGunSenderMail . '>',
          'to' => !empty($to) ? $to : $p->getPayment()->getMailAddress(),
          'subject' => $subject . " - ID #" . $p->getId(),
          'template' => $template,
          'h:X-Mailgun-Variables' =>  \json_encode($data),
        );
      }
      
      # Make the call to the client.
      $mgClient->messages()->send($domain, $params);
      $this->logger->info("Mail sent ! with subject" . $subject);

      $this->sendAdmin($params, $mgClient, $domain);
    } catch (\Throwable $th) {

      $this->logger->error("Error sending mail " . $th->getMessage());

      if ($revert && is_array($p)) {
        $this->removePayment($p['order_id']);
        throw $th;
      }
    }
  }

  public function sendMail4(string $subject, $p, $to, string $template = "pay-request-free", $revert = true)
  {
    try {
      $dt = $p['activity_date_time'];
      $dt = date_create_from_format('d/m/Y, H:i', $dt);

      $activity = $this->activityRepo->getByUuid($p['id_activity']);
      # Instantiate the client.
      $mgClient = Mailgun::create($this->mailGunKey);
      $domain = $this->mailGunDomain;

      $data = array(
        "name" => $p['client_name'],
        "event_name" => $p['activity_name'],
        "name_org" => $p['company_name'],
        "date" => date('d/m/Y', !empty($dt) ? $dt->getTimestamp() : time()),
        "time" => date('H:i', !empty($dt) ? $dt->getTimestamp() : time()),
        "place_name" => $activity->getCountry() . ', ' . $activity->getCity() . ', ' . $activity->getStreetName(),
      );

      $params = array(
        'from' => $this->mailGunSenderName . ' <' . $this->mailGunSenderMail . '>',
        'to' => $to,
        'subject' => $subject . " - ID #" . $p['id'],
        'template' => $template,
        'h:X-Mailgun-Variables' =>  \json_encode($data),
      );
      # Make the call to the client.
      $mgClient->messages()->send($domain, $params);

      $this->logger->info("Mail sent ! with subject" . $subject);

      $this->sendAdmin($params, $mgClient, $domain);
    } catch (\Throwable $th) {

      $this->logger->error("Error sending mail " . $th->getMessage());

      if ($revert) {
        $this->removePayment($p['order_id']);
        throw $th;
      }
    }
  }

  public function sendMail5(string $to, string $subject,string $sender,string $partner,string $activity, string $template)
  {
    try {
      # Instantiate the client.
      $mgClient = Mailgun::create($this->mailGunKey);
      $domain = $this->mailGunDomain;
      $data = array(
        "sender_name" => $sender,
        "partner_name" => $partner,
        "activity_name" => $activity
      );
      $params = array(
        'from' => $this->mailGunSenderName . ' <' . $this->mailGunSenderMail . '>',
        'to' => $to,
        'subject' => $subject,
        'template' => $template,
        'h:X-Mailgun-Variables' =>  \json_encode($data),
      );
      # Make the call to the client.
      $result = $mgClient->messages()->send($domain, $params);
      $this->logger->info("Mail sent ! with subject" . $subject);
    } catch (\Throwable $e) {
      $this->logger->error("Error sending mail " . $e->getMessage());
      throw new RequestValidationException('Erreur lors de l\'envoi du mail: ' . $e->getMessage());
    }
  }

  public function sendMail6(string $to = null, string $subject, $payment, string $template = "pay-success" )
  {
    try {
      $dt = $payment['activity_date_time'];
      $dt = date_create_from_format('d/m/Y, H:i', $dt);

      $activity = !empty($payment['id_activity']) ? $this->activityRepo->getByUuid($payment['id_activity']):null;
      # Instantiate the client.
      $mgClient = Mailgun::create($this->mailGunKey);
      $domain = $this->mailGunDomain;

      $data = array(

        "transaction_id" => $payment['financial_transaction_id'],
        "order_id" => $payment['order_id'],
        "name" => $payment['client_name'],
        "event_name" => $payment['activity_name'],
        "amount" => $payment['amount'] . ' FCFA',
        "name_org" => $payment['company_name'],
        "date" => date('d/m/Y', !empty($dt) ? $dt->getTimestamp() : time()),
        "time" => date('H:i', !empty($dt) ? $dt->getTimestamp() : time()),
        "place_name" => $activity?->getCountry() . ', ' . $activity?->getCity() . ', ' . $activity?->getStreetName(),
      );

      $params = array(
        'from' => $this->mailGunSenderName . ' <' . $this->mailGunSenderMail . '>',
        'to' => !empty($to) ? $to : $payment['mail_address'],
        'subject' => $subject . " - ID #" . $payment['id'],
        'template' => $template,
        'h:X-Mailgun-Variables' =>  \json_encode($data),
      );
      # Make the call to the client.
      $mgClient->messages()->send($domain, $params);

      $this->logger->info("Mail sent ! with subject" . $subject);

      $this->sendAdmin($params, $mgClient, $domain);
    } catch (\Throwable $th) {
      $this->logger->error("Error sending mail " . $th->getMessage());
        throw $th;
    }
  }

  public function sendMail7(string $to = null, string $subject, $payment, string $template = "pay-subscription-success" )
  {
    try {

        $plan = $this->subscriptionRepo->findOneBy(['payment' => $payment['id']]);
        if($plan == null){

          throw new \Exception("Plan not found", 404);
        }

      $start_date = $plan->getStartDate()->format('d/m/Y H:i:s')." {$plan->getStartDate()->getTimezone()->getName()}";
      $end_date = $plan->getEndDate()->format('d/m/Y H:i:s')." {$plan->getEndDate()->getTimezone()->getName()}";
      $plan_name = $plan->getDescription();

      
      # Instantiate the client.
      $mgClient = Mailgun::create($this->mailGunKey);
      $domain = $this->mailGunDomain;

      $data = array(
        "transaction_id" => $payment['financial_transaction_id'],
        "name" => $payment['client_name'],
        "plan_name" => $plan_name,
        "start_date" => $start_date,
        "end_date" => $end_date,
        "amount" => $payment['amount'] . ' FCFA',
        "company_name" => $payment['company_name'],
      );

      $params = array(
        'from' => $this->mailGunSenderName . ' <' . $this->mailGunSenderMail . '>',
        'to' => !empty($to) ? $to : $payment['mail_address'],
        'subject' => $subject . " - ID #" . $payment['id'],
        'template' => $template,
        'h:X-Mailgun-Variables' =>  \json_encode($data),
      );
      # Make the call to the client.
      $mgClient->messages()->send($domain, $params);

      $this->logger->info("Mail sent ! with data" . \json_encode($data));

      $this->sendAdmin($params, $mgClient, $domain);
    } catch (\Throwable $th) {
      $this->logger->error("Error sending mail " . $th->getMessage());
        throw $th;
    }
  }

  public function sendMail8(string $to = null, string $subject, $payment, string $template = "pay-insurance-success" )
  {
    try {
      # Instantiate the client.
      $mgClient = Mailgun::create($this->mailGunKey);
      $domain = $this->mailGunDomain;
      if($template == "pay-insurance-success" || $template == "pay-insurance-success-en"){
        
      
        $license = $this->licenseRepo->findOneBy(['payment' => $payment['id']]);
        if($license == null){

          throw new \Exception("license not found", 404);
        }
      $start_date = $license->getCreatedAt()->format('d/m/Y H:i:s')." {$license->getCreatedAt()->getTimezone()->getName()}";
      $end_date = $license->getEndDate()->format('d/m/Y H:i:s')." {$license->getEndDate()->getTimezone()->getName()}";
      $license_plan_name = $license->getLicensePlan()->getTitle();
      $license_name = strtoupper($license->getLastName()." ".$license->getFirstName());
      


      $data = array(
        "transaction_id" => $payment['financial_transaction_id'],
        "start_date" => $start_date,
        "name" => $payment['client_name'],
        "license_name" => $license_name,
        "plan_name" => $license_plan_name,
        "end_date" => $end_date,
        "amount" => $payment['amount'] . ' FCFA',
      );

      $params = array(
        'from' => $this->mailGunSenderName . ' <' . $this->mailGunSenderMail . '>',
        'to' => !empty($to) ? $to : $payment['mail_address'],
        'subject' => $subject . " - ID #" . $payment['id'],
        'template' => $template,
        'h:X-Mailgun-Variables' =>  \json_encode($data),
      );
    }elseif($template == "pay-insurance-success-free" || $template == "pay-insurance-success-free-en"){
       
      
      $license = $this->licenseRepo->findOneBy(['payment' => $payment->getId()]);
      if($license == null){

        throw new \Exception("license not found", 404);
      }
    $start_date = $license->getCreatedAt()->format('d/m/Y H:i:s')." {$license->getCreatedAt()->getTimezone()->getName()}";
    $end_date = $license->getEndDate()->format('d/m/Y H:i:s')." {$license->getEndDate()->getTimezone()->getName()}";
    $license_plan_name = $license->getLicensePlan()->getTitle();
    $license_name = strtoupper($license->getLastName()." ".$license->getFirstName());

    


    $data = array(
      
      "name" => $payment->getClientName(),
      "plan_name" => $license_plan_name,
      "license_name" => $license_name,
      "start_date" => $start_date,
      "end_date" => $end_date,
      "amount" => $license->getLicensePlan()->getPrice() . ' FCFA',
    );

    $params = array(
      'from' => $this->mailGunSenderName . ' <' . $this->mailGunSenderMail . '>',
      'to' => !empty($to) ? $to : $payment->getMailAddress(),
      'subject' => $subject . " - ID #" . $payment->getId(),
      'template' => $template,
      'h:X-Mailgun-Variables' =>  \json_encode($data),
    );
    }
      # Make the call to the client.
      $mgClient->messages()->send($domain, $params);

      $this->logger->info("Mail sent ! with data" . \json_encode($data));

      $this->sendAdmin($params, $mgClient, $domain);
    } catch (\Throwable $th) {
      $this->logger->error("Error sending mail " . $th->getMessage());
        throw $th;
    }
  }

  public function sendAdmin($params, $client, $domain)
  {
    try {
      //$params['to'] = "<EMAIL>";
      $params['to'] = "<EMAIL>";
      $client->messages()->send($domain, $params, $domain);
    } catch (\Throwable $th) {
    }
  }

  public function sendAdmin2($admins, $user, $subject, $template)
  {

    try {

      # Instantiate the client.
      $mgClient = Mailgun::create($this->mailGunKey);

      $data = array(
        "uuid" => $user->getUuid(),
        "id" => $user->getId(),
        "first_name" => $user->getFirstname()?$user->getFirstName():"_",
        "last_name" => $user->getLastName()?$user->getLastName():"_",
        "phone_number" => $user->getPhoneNumber()?$user->getPhoneNumber():"_",
        "email" => $user->getEmail()?$user->getEmail():"_",
        
      );

      $params = array(
        'from' => "sportaabe " . ' <' . "<EMAIL>" . '>',
        'to' => $admins,
        'subject' => $subject,
        'template' => $template,
        'h:X-Mailgun-Variables' =>  \json_encode($data),
      );
      # Make the call to the client.
      $result = $mgClient->messages()->send($this->mailGunDomain , $params);
      $this->logger->info("Email sent successfully with subject : " . $subject);
      
    } catch (\Throwable $th) {
      $this->logger->error("Email with subject : " . $subject. " not sent successfully");
      throw $th;
    }
  }
  /**
   * Returns a UUID4 string.
   *
   * @return string uuid4
   */
  public function uuid()
  {
    return sprintf(
      '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
      mt_rand(0, 0xffff),
      mt_rand(0, 0xffff),
      mt_rand(0, 0xffff),
      mt_rand(0, 0x0fff) | 0x4000,
      mt_rand(0, 0x3fff) | 0x8000,
      mt_rand(0, 0xffff),
      mt_rand(0, 0xffff),
      mt_rand(0, 0xffff)
    );
  }
  
}
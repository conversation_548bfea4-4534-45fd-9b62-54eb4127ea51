<?php

namespace App\Service\Player\Implementation;

use App\Entity\Player\Player;
use App\Entity\Player\PlayerStatistic;
use App\Mapper\BaseMapperTrait;
use App\Repository\Company\CompanyRepository;
use App\Repository\Payment\LicenseRepository;
use App\Repository\Player\PlayerRepository;
use App\Repository\Player\PlayerStatisticRepository;
use App\Service\Player\Interface\IPlayerService;
use App\Service\Traits\BaseServiceTrait;
use App\Service\Traits\LogServiceTrait;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class PlayerService implements IPlayerService
{
    use BaseServiceTrait, LogServiceTrait, BaseMapperTrait;

    private PlayerRepository $playerRepository;
    private PlayerStatisticRepository $statisticRepository;
    private LicenseRepository $licenseRepository;
    private CompanyRepository $companyRepository;
    private Security $security;

    public function __construct(
        PlayerRepository $playerRepository,
        PlayerStatisticRepository $statisticRepository,
        LicenseRepository $licenseRepository,
        CompanyRepository $companyRepository,
        Security $security
    ) {
        $this->playerRepository = $playerRepository;
        $this->statisticRepository = $statisticRepository;
        $this->licenseRepository = $licenseRepository;
        $this->companyRepository = $companyRepository;
        $this->security = $security;
    }

    /**
     * Create a new player
     */
    public function createPlayer(Request $request)
    {
        try {
            $user = $this->security->getUser();

            // Check if user is admin or company owner
            $isAdmin = $user->getMetaValueByCode('is_admin', false) || $user->getMetaValueByCode('is_super_admin', false);
            $userCompany = $user->getCompany();

            if (!$isAdmin && !$userCompany) {
                return $this->result(["message" => "Access denied. Admin privileges or company ownership required."], 403, Response::HTTP_FORBIDDEN);
            }

            // Check for duplicate full name
            $existingPlayer = $this->playerRepository->findByFullName(
                $request->get('firstName'),
                $request->get('lastName')
            );

            if ($existingPlayer) {
                return $this->result(["message" => "A player with this name already exists"], 400, Response::HTTP_BAD_REQUEST);
            }

            // Find team (company) - for admin, allow any team; for company owner, only their teams
            $teamId = $request->get('teamId');
            $team = null;

            if ($isAdmin) {
                $team = $this->companyRepository->findOneBy(['id' => $teamId]);
            } else {
                // Company owner can only create players for their own teams
                $teams = $userCompany->getTeams();
                foreach ($teams as $companyTeam) {
                    if ($companyTeam->getId() == $teamId) {
                        $team = $companyTeam;
                        break;
                    }
                }
            }

            if (!$team) {
                return $this->result(["message" => "Team not found or access denied"], 404, Response::HTTP_NOT_FOUND);
            }

            // Create player with basic information
            $player = new Player();
            $player->setTeam($team);
            $player->setPosition($request->get('position'));
            $player->setJerseyNumber($request->get('jerseyNumber'));
            $player->setFirstName($request->get('firstName'));
            $player->setLastName($request->get('lastName'));
            $player->setCategory($request->get('category'));

            if ($request->get('birthDate')) {
                $player->setBirthDate(new \DateTime($request->get('birthDate')));
            }

            $player->setActive(true);
            $this->persist($player);

            $this->logger->info('Player created: {id}', [
                'id' => $player->getId(),
            ]);

            $data = $this->toPlayerInfo($player);

            return $this->result($data);

        } catch (\Exception $e) {
            $this->logger->error('Error creating player: {message}', [
                'message' => $e->getMessage(),
            ]);
            return $this->result(["message" => "Error creating player: " . $e->getMessage()], 500, Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Update player information
     */
    public function updatePlayer(Request $request, string $id)
    {
        try {
            $user = $this->security->getUser();

            // Check if user is admin or company owner
            $isAdmin = $user->getMetaValueByCode('is_admin', false) || $user->getMetaValueByCode('is_super_admin', false);
            $userCompany = $user->getCompany();

            if (!$isAdmin && !$userCompany) {
                return $this->result(["message" => "Access denied. Admin privileges or company ownership required."], 403, Response::HTTP_FORBIDDEN);
            }

            // Find player
            $player = $this->playerRepository->findOneBy(['id' => $id]);
            if (!$player) {
                return $this->result(["message" => "Player not found"], 404, Response::HTTP_NOT_FOUND);
            }

            // Check if company owner can access this player
            if (!$isAdmin && $player->getTeam()->getCompany() !== $userCompany) {
                return $this->result(["message" => "Access denied. You can only update players from your own company."], 403, Response::HTTP_FORBIDDEN);
            }

            // Check for duplicate full name if name is being updated
            if ($request->get('firstName') || $request->get('lastName')) {
                $firstName = $request->get('firstName') ?? $player->getFirstName();
                $lastName = $request->get('lastName') ?? $player->getLastName();

                $existingPlayer = $this->playerRepository->findByFullName(
                    $firstName,
                    $lastName,
                    $player->getId()
                );

                if ($existingPlayer) {
                    return $this->result(["message" => "A player with this name already exists"], 400, Response::HTTP_BAD_REQUEST);
                }

                if ($request->get('firstName')) {
                    $player->setFirstName($firstName);
                }
                if ($request->get('lastName')) {
                    $player->setLastName($lastName);
                }
            }

            // Update player data
            if ($request->get('position')) {
                $player->setPosition($request->get('position'));
            }

            if ($request->get('jerseyNumber')) {
                $player->setJerseyNumber($request->get('jerseyNumber'));
            }

            if ($request->get('teamId')) {
                $team = $this->companyRepository->findOneBy(['id' => $request->get('teamId')]);
                if (!$team) {
                    return $this->result(["message" => "Team not found"], 404, Response::HTTP_NOT_FOUND);
                }
                $player->setTeam($team);
            }

            if ($request->get('active')) {
                $player->setActive($request->get('active'));
            }

            $this->persist($player);

            $this->logger->info('Player updated: {id}', [
                'id' => $player->getId(),
            ]);

            return $this->result($this->toPlayerInfo($player));
        } catch (\Exception $e) {
            $this->logger->error('Error updating player: {message}', [
                'message' => $e->getMessage(),
            ]);
            return $this->result(["message" => "Error updating player: " . $e->getMessage()], 500, Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get player by ID
     */
    public function getPlayer(string $id)
    {
        try {
            $player = $this->playerRepository->findOneBy(['id' => $id]);
            if (!$player) {
                return $this->result(["message" => "Player not found"], 404, Response::HTTP_NOT_FOUND);
            }

            return $this->result($this->toPlayerInfo($player));
        } catch (\Exception $e) {
            return $this->result(["message" => "Error retrieving player: " . $e->getMessage()], 500, Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get all players
     */
    public function getAllPlayers(Request $request)
    {
        try {
            $criteria = [];

            // Filter by team if provided
            if ($request->query->has('teamId')) {
                $team = $this->companyRepository->findOneBy(['id' => $request->query->get('teamId')]);
                if ($team) {
                    $criteria['team'] = $team;
                }
            }

            // Filter by active status
            if ($request->query->has('active')) {
                $criteria['active'] = $request->query->get('active') === 'true';
            }

            $players = $this->playerRepository->findBy($criteria);

            $result = [];
            foreach ($players as $player) {
                $result[] = $this->toPlayerInfo($player);
            }

            return $this->result($result);
        } catch (\Exception $e) {
            return $this->result(["message" => "Error retrieving players: " . $e->getMessage()], 500, Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Delete player
     */
    public function deletePlayer(string $id)
    {
        try {
            $user = $this->security->getUser();

            // Check if user is admin or company owner
            $isAdmin = $user->getMetaValueByCode('is_admin', false) || $user->getMetaValueByCode('is_super_admin', false);
            $userCompany = $user->getCompany();

            if (!$isAdmin && !$userCompany) {
                return $this->result(["message" => "Access denied. Admin privileges or company ownership required."], 403, Response::HTTP_FORBIDDEN);
            }

            $player = $this->playerRepository->findOneBy(['id' => $id]);
            if (!$player) {
                return $this->result(["message" => "Player not found"], 404, Response::HTTP_NOT_FOUND);
            }

            // Check if company owner can access this player
            if (!$isAdmin && $player->getTeam()->getCompany() !== $userCompany) {
                return $this->result(["message" => "Access denied. You can only delete players from your own company."], 403, Response::HTTP_FORBIDDEN);
            }

            // Check for active licenses
            $activeLicenses = $player->getLicenses()->filter(function($license) {
                return $license->isActive();
            });

            if (!$activeLicenses->isEmpty()) {
                return $this->result(["message" => "Cannot delete player with active licenses"], 400, Response::HTTP_BAD_REQUEST);
            }

            // Remove all licenses and their related payments
            foreach ($player->getLicenses() as $license) {
                $payment = $license->getPayment();
                if ($payment) {
                    $this->delete($payment);
                }
                $this->delete($license);
            }

            // Delete the player
            $this->delete($player);

            $this->logger->info('Player deleted: {id}', [
                'id' => $player->getId(),
            ]);

            return $this->result(["message" => "Player deleted successfully"]);
        } catch (\Exception $e) {
            $this->logger->error('Error deleting player: {message}', [
                'message' => $e->getMessage(),
            ]);
            return $this->result(["message" => "Error deleting player: " . $e->getMessage()], 500, Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Record player statistics for a match
     */
    public function recordStatistics(Request $request)
    {
        try {
            $user = $this->security->getUser();

            // Check if user is admin
            if (!$user->getMetaValueByCode('is_admin', false)) {
                return $this->result(["message" => "Only administrators can record statistics"], 403, Response::HTTP_FORBIDDEN);
            }

            // Find player
            $player = $this->playerRepository->findOneBy(['id' => $request->get('playerId')]);
            if (!$player) {
                return $this->result(["message" => "Player not found"], 404, Response::HTTP_NOT_FOUND);
            }

            // Create or update statistics
            $encounterId = $request->get('encounterId');
            $statistic = $this->statisticRepository->findOneBy([
                'player' => $player,
                'encounterId' => $encounterId
            ]);

            if (!$statistic) {
                $statistic = new PlayerStatistic();
                $statistic->setPlayer($player);
                $statistic->setEncounterId($encounterId);
                $statistic->setEncounterName($request->get('encounterName'));
                $statistic->setEncounterDate(new \DateTime($request->get('encounterDate')));
            }

            // Set statistics
            $goals = (int)$request->get('goals', 0);
            $yellowCards = (int)$request->get('yellowCards', 0);
            $redCards = (int)$request->get('redCards', 0);
            $assists = (int)$request->get('assists', 0);

            $statistic->setGoals($goals);
            $statistic->setYellowCards($yellowCards);
            $statistic->setRedCards($redCards);
            $statistic->setAssists($assists);
            $statistic->setNotes($request->get('notes'));

            $this->persist($statistic);

            // Update player totals
            $this->updatePlayerTotals($player);

            $this->logger->info('Player statistics recorded: {playerId} for encounter {encounterId}', [
                'playerId' => $player->getId(),
                'encounterId' => $encounterId,
            ]);

            return $this->result($this->toStatisticInfo($statistic));
        } catch (\Exception $e) {
            $this->logger->error('Error recording statistics: {message}', [
                'message' => $e->getMessage(),
            ]);
            return $this->result(["message" => "Error recording statistics: " . $e->getMessage()], 500, Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get player statistics
     */
    public function getPlayerStatistics(string $playerId)
    {
        try {
            $player = $this->playerRepository->findOneBy(['id' => $playerId]);
            if (!$player) {
                return $this->result(["message" => "Player not found"], 404, Response::HTTP_NOT_FOUND);
            }

            $statistics = $this->statisticRepository->findBy(['player' => $player], ['encounterDate' => 'DESC']);

            $result = [];
            foreach ($statistics as $statistic) {
                $result[] = $this->toStatisticInfo($statistic);
            }

            return $this->result($result);
        } catch (\Exception $e) {
            return $this->result(["message" => "Error retrieving statistics: " . $e->getMessage()], 500, Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get top scorers
     */
    public function getTopScorers(int $limit = 10)
    {
        try {
            $players = $this->playerRepository->findTopScorers($limit);

            $result = [];
            foreach ($players as $player) {
                $result[] = $this->toPlayerInfo($player);
            }

            return $this->result($result);
        } catch (\Exception $e) {
            return $this->result(["message" => "Error retrieving top scorers: " . $e->getMessage()], 500, Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get team statistics
     */
    public function getTeamStatistics(string $teamId)
    {
        try {
            $team = $this->companyRepository->findOneBy(['id' => $teamId]);
            if (!$team) {
                return $this->result(["message" => "Team not found"], 404, Response::HTTP_NOT_FOUND);
            }

            $players = $this->playerRepository->findByTeam($team);

            $totalGoals = 0;
            $totalYellowCards = 0;
            $totalRedCards = 0;
            $totalAssists = 0;
            $playerStats = [];

            foreach ($players as $player) {
                $totalGoals += $player->getTotalGoals() ?? 0;
                $totalYellowCards += $player->getTotalYellowCards() ?? 0;
                $totalRedCards += $player->getTotalRedCards() ?? 0;
                $totalAssists += $player->getTotalAssists() ?? 0;

                $playerStats[] = $this->toPlayerInfo($player);
            }

            $result = [
                'teamId' => $team->getId(),
                'teamName' => $team->getName(),
                'totalPlayers' => count($players),
                'totalGoals' => $totalGoals,
                'totalYellowCards' => $totalYellowCards,
                'totalRedCards' => $totalRedCards,
                'totalAssists' => $totalAssists,
                'players' => $playerStats
            ];

            return $this->result($result);
        } catch (\Exception $e) {
            return $this->result(["message" => "Error retrieving team statistics: " . $e->getMessage()], 500, Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Update player totals based on all statistics
     */
    private function updatePlayerTotals(Player $player)
    {
        $statistics = $this->statisticRepository->findBy(['player' => $player]);

        $totalGoals = 0;
        $totalYellowCards = 0;
        $totalRedCards = 0;
        $totalAssists = 0;

        foreach ($statistics as $statistic) {
            $totalGoals += $statistic->getGoals() ?? 0;
            $totalYellowCards += $statistic->getYellowCards() ?? 0;
            $totalRedCards += $statistic->getRedCards() ?? 0;
            $totalAssists += $statistic->getAssists() ?? 0;
        }

        $player->setTotalGoals($totalGoals);
        $player->setTotalYellowCards($totalYellowCards);
        $player->setTotalRedCards($totalRedCards);
        $player->setTotalAssists($totalAssists);
        $player->setTotalMatches(count($statistics)); // This represents total encounters played

        $this->persist($player);
    }

    /**
     * Convert PlayerStatistic to array
     */
    private function toStatisticInfo(PlayerStatistic $statistic): array
    {
        return [
            'id' => $statistic->getId(),
            'uuid' => $statistic->getUuid(),
            'player' => [
                'id' => $statistic->getPlayer()->getId(),
                'name' => $statistic->getPlayer()->getFirstName() . ' ' . $statistic->getPlayer()->getLastName()
            ],
            'encounterId' => $statistic->getEncounterId(),
            'encounterName' => $statistic->getEncounterName(),
            'encounterDate' => $statistic->getEncounterDate() ? $statistic->getEncounterDate()->format('d-m-Y') : null,
            'goals' => $statistic->getGoals(),
            'yellowCards' => $statistic->getYellowCards(),
            'redCards' => $statistic->getRedCards(),
            'assists' => $statistic->getAssists(),
            'notes' => $statistic->getNotes(),
            'createdAt' => $statistic->getCreatedAt() ? $statistic->getCreatedAt()->format('d-m-Y H:i:s') : null,
            'updatedAt' => $statistic->getUpdatedAt() ? $statistic->getUpdatedAt()->format('d-m-Y H:i:s') : null
        ];
    }
}
<?php

namespace App\Service\Player\Interface;

use Symfony\Component\HttpFoundation\Request;

interface IPlayerService
{
    /**
     * Create a new player
     */
    public function createPlayer(Request $request);

    /**
     * Update player information
     */
    public function updatePlayer(Request $request, string $id);

    /**
     * Get player by ID
     */
    public function getPlayer(string $id);

    /**
     * Get all players
     */
    public function getAllPlayers(Request $request);

    /**
     * Record player statistics for a match
     */
    public function recordStatistics(Request $request);

    /**
     * Get player statistics
     */
    public function getPlayerStatistics(string $playerId);

    /**
     * Get top scorers
     */
    public function getTopScorers(int $limit = 10);

    /**
     * Get team statistics
     */
    public function getTeamStatistics(string $teamId);
}
<?php

namespace App\Service\Activity\Implementation;

use App\Dto\GenericResponse;
use App\Constant\DefaultValues;
use App\Entity\Activity\Activity;
use App\Entity\Activity\ActivityMeta;
use App\Constant\DataFixtureFileNames;
use App\Repository\Activity\ActivityRepository;
use App\Service\Traits\LogServiceTrait;
use Doctrine\Persistence\ObjectManager;
use App\Service\Traits\BaseServiceTrait;
use App\Entity\Activity\ActivityCategory;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\HttpFoundation\Request;
use App\Service\Traits\DataFixturesServiceTrait;
use App\Service\Activity\Interface\IActivityService;
use App\Repository\Activity\ActivityCategoryRepository;
use App\Mapper\Activity\Interface\IActivityCategoryMapper;
use App\Mapper\BaseMapperTrait;
use App\Repository\Company\CompanyRepository;
use App\Service\Activity\Interface\IActivityCategoryService;
use App\Service\Shared\Implementation\CommonService;

class ActivityService implements IActivityService
{
  use BaseServiceTrait, BaseMapperTrait, LogServiceTrait, DataFixturesServiceTrait;

  private IActivityCategoryMapper $mapper;
  private Security $security;
  private CompanyRepository $companyRepo;

  private ActivityCategoryRepository $repository;
  private CommonService $commonService;
  private ActivityRepository $activityRepo;

  public function __construct(IActivityCategoryMapper $mapper, ActivityCategoryRepository $repository, Security $security, CompanyRepository $companyRepo, CommonService $commonService, ActivityRepository $activityRepo)
  {
    $this->mapper = $mapper;
    $this->repository = $repository;
    $this->security = $security;
    $this->companyRepo = $companyRepo;
    $this->commonService = $commonService;
    $this->activityRepo = $activityRepo;
  }

  public function getByUuid(string $uuid, bool $throwIfNotFound = true): ActivityCategory|null
  {
    return $this->repository->getByUuid($uuid, $throwIfNotFound);
  }

  public function loadFixtures(ObjectManager $manager): void
  {
    $data = $this->csvFileToArray(DataFixtureFileNames::ACTIVITY_CATEGORY);
    foreach ($data as $value) {
      $existingCategory = $this->getByUuid($value["UUID"], false);
      $activityCategory = empty($existingCategory) ? $this->mapper->fromArray($value) : $this->mapper->fromArrayUpdate($value, $existingCategory);
      $manager->persist($activityCategory);
    }
    $manager->flush();
  }

  public function findAll(Request $request): GenericResponse
  {
    $params = $this->mapper->fromFindAllRequest($request);
    $data = $this->repository->getItems($params);

    $outputs = $this->mapper->toOutArray($params->getOutput(), ...$data['items']);
    return $this->result([
      'items' => $outputs,
      'total' => $data['total'],
      'totalFiltered' => count($outputs)
    ]);
  }

  public function findOne(string $uuid, Request $request): GenericResponse
  {
    $outputFormat = $request->query->get("output", DefaultValues::DEFAULT_FIND_ONE_OUTPUT_FORMAT);
    $setting = $this->repository->getByUuid($uuid);
    $output = $this->mapper->toOut($outputFormat, $setting);

    return $this->result($output);
  }

  public function createActivity(Request $request, $reqActivity = null)
  {
    
    $body = $request->request->all();

    /**  @var \App\Entity\Security\User */
    $user = $this->security->getUser();

    if(!$user->getCompany() || !$user->getCompany()?->isActive()){
      return $this->result(["message" => "You need to own an active company to create an activity"], 400);
    }

    $activity =  $reqActivity ?? new Activity();
    $activity->setOwner($user);
    $activity->setCreator($user);
    $activity->setTitle($body["_instantInformation"]["_title"]);
    $activity->setShortDescription($body["_instantInformation"]["_shortDescription"]);
    $activity->setLongDescription($body["_instantInformation"]["_longDescription"]);
    $activity->setPlaceName($body["_address"]["_address"]);
    $activity->setStreetName($body["_address"]["_address"]);
    $activity->setCity($body["_address"]["_city"]);
    $activity->setZipCode(intval($body["_address"]["_zipCode"]));
    $activity->setLongitude(isset($body["_address"]["_coords"]["longitude"]) ? $body["_address"]["_coords"]["longitude"] : null);
    $activity->setLatitude(isset($body["_address"]["_coords"]["latitude"]) ? $body["_address"]["_coords"]["latitude"] : null);
    $activity->setCountry($body["_address"]["_country"]);
    $activity->setPrice(\floatval($body["_instantInformation"]["_price"]));
    $activity->setStatus('ACTIVE');
    $activity->setActive(true);
    
    $metas = ["_picture", "_category", "_pro_only"];
    foreach ($metas as $key) {
      if (!empty($body[$key])) {
        $activity->addMeta(
          (new ActivityMeta())
            ->setCode($key)
            ->setValue($body[$key])
        );
      } else if (!empty($body["_instantInformation"][$key])) {
        $activity->addMeta(
          (new ActivityMeta())
            ->setCode($key)
            ->setValue($body["_instantInformation"][$key])
        );
      }
    }

    try {
      $times = "";
      foreach ($body["_instantActivationTimes"] as $value) {
        $times .= '|' . $value['_activationTimeType'] . ',' . $value['_startDateTime'] . ',' . $value['_endDateTime'] . ',' . $value['_isEnabled'] . ',' . $value['_activeDayType'];
      }
      $activity->addMeta(
        (new ActivityMeta())
          ->setCode('_instantActivationTimes')
          ->setValue($times)
      );
    } catch (\Throwable $th) {
    }

    try {
      $times = "";
      foreach ($body["_instantActivationTimes"] as $value) {
        $times .= '|' . $value['_activationTimeType'] . ',' . $value['_startDateTime'] . ',' . $value['_endDateTime'] . ',' . $value['_isEnabled'] . ',' . $value['_activeDayType'];
      }
      $activity->addMeta(
        (new ActivityMeta())
          ->setCode('_instantActivationTimes')
          ->setValue($times)
      );
    } catch (\Throwable $th) {
    }

    try {
      $partners = "";
      foreach ($body["_instantPartner"] as $value) {
        $partners .= '|' . $value['_id'] . ',' . $value['_name'] . ',' . $value['_image'];
      }
      $activity->addMeta(
        (new ActivityMeta())
          ->setCode('_instantPartner')
          ->setValue($partners)
      );
    } catch (\Throwable $th) {
    }

    $this->persist($activity);

    $this->logger->info('Activity created : {activityname}', [
      'activityname' => $activity->getTitle(),
    ]);
    
    if(!empty($body["_instantPartner"]))
    {
        //send mail to all added partners 
        foreach ($body["_instantPartner"] as $value) {
          
          $sender = $user->getCompany()->getName();
          $to= $this->companyRepo->getByUuid($value['_id'])?->getMetaValueByCode("_email");
          $partner = $value['_name'];
          $activity_name= $activity->getTitle();
          $template = $user->getMetaValueByCode("language","fr") === "fr"?"add-partner":"add-partner-en";
            
          $this->commonService->sendMail5($to,"[sportaabe]- Partenariat",$sender,$partner,$activity_name,$template);
        }
    }
    
    

    return $this->result($this->toActivityInfo($activity));
  }

  public function editActivity(string $uuid, Request $request)
  {
    return $this->createActivity($request, $this->activityRepo->getByUuid($uuid));
  }

  public function editActivityAdmin(Request $request)
  {
    $body = $request->request->all();

    $activity= $this->activityRepo->getByUuid($body["uuid"]);
    $activity->setTitle($body["title"]);
    $activity->setShortDescription($body["shortDescription"]);
    $activity->setLongDescription($body["longDescription"]);
    $activity->setPlaceName($body["address"]);
    $activity->setStreetName($body["address"]);
    $activity->setCity($body["city"]);
    $activity->setActive($body["active"]);
    $activity->setZipCode(intval($body["zipCode"]));
    $activity->setLongitude($body["longitude"]);
    $activity->setLatitude($body["latitude"]);
    $activity->setCountry($body["country"]);
    $activity->setPrice(\floatval($body["price"]));

    $metas = ["_picture", "_category"];
    foreach ($metas as $key) {
      
        $activity->addMeta(
          (new ActivityMeta())
            ->setCode($key)
            ->setValue($body[$key])
        );
       
    }

    try {
      $times = "";
      foreach ($body["_instantActivationTimes"] as $value) {
        $times .= '|' . $value['_activationTimeType'] . ',' . $value['_startDateTime'] . ',' . $value['_endDateTime'] . ',' . $value['_isEnabled'] . ',' . $value['_activeDayType'];
      }
      $activity->addMeta(
        (new ActivityMeta())
          ->setCode('_instantActivationTimes')
          ->setValue($times)
      );
    } catch (\Throwable $th) {
    }

    try {
      $partners = "";
      foreach ($body["_instantPartner"] as $value) {
        $partners .= '|' . $value['_id'] . ',' . $value['_name'] . ',' . $value['_image'];
      }
      $activity->addMeta(
        (new ActivityMeta())
          ->setCode('_instantPartner')
          ->setValue($partners)
      );
    } catch (\Throwable $th) {
    }

    $this->persist($activity);

    $this->logger->info('Activity created by admin : {activityname}', [
      'activityname' => $activity->getTitle(),
    ]);

    return $this->result($this->toActivityAdminInfo($activity));
  }

  public function businessActivities(Request $request)
  {

    /**  @var \App\Entity\Security\User */
    $user = $this->security->getUser();

    $loadTimes = intval($request->query->get("loadTimes", "0"));
    $cities = $request->query->get("cities", "[]");
    $categories = $request->query->get("categoryIds", "[]");
    $companies = $request->query->get("companyIds", "[]");
    $days = $request->query->get("days", "[]");
    $price = $request->query->get("price", "all");
    $businessId = $request->query->get("businessId", "");

    $data = [];
    $activities = [];

    if (!empty($user->getCompany()) || !empty($businessId)) {

      if (!empty($businessId)) {
        $company = $this->companyRepo->getByUuid($businessId, false);
        if (!empty($company)) {
          $users = $company->getUsers()->toArray();
          foreach ($users as $value) {
            $activities = \array_merge($this->activityRepo->findBy(['owner' => $value]), $activities);
          }
        }
      } else {
        $activities = $this->activityRepo->findBy(['owner' => $user]);

        //lists activities the current user has been added as partner on 
        $partnersActivities = $this->activityRepo->findAll();
        $partners= [];
        foreach ($partnersActivities as $partnersActivity){ 
          if( !empty($partnersActivity->getActivitiesPartnersArray()) )
          { 
            $partners = $partnersActivity->getActivitiesPartnersArray();
            foreach ( $partners as $partner)
            {
              if($user->getCompany()->getUuid() == $partner['id'])
              {
                array_push($activities,$partnersActivity);
              }         
            }   
          }   
          
        }
          
      }

      usort($activities, function ($a, $b) {
        return $b->getId() - $a->getId();
      });


      foreach ($activities as $activity) {
        if (!empty($cities)) {
          $cities = is_string($cities) ?  \json_decode($cities) : $cities;
          if (is_array($cities) && count($cities) > 0) {
            if (in_array('En ligne', $cities) || in_array('Online', $cities)) {
              if (filter_var($activity->getStreetName(), FILTER_VALIDATE_URL) === FALSE) continue;
            } else {
              $city = trim(ucfirst(strtolower($activity->getCity())));
              if (!in_array($city, $cities)) continue;
            }
          }
        }

        if (!empty($categories)) {
          $categories = is_string($categories) ?  \json_decode($categories) : $categories;
          if (is_array($categories) && count($categories) > 0) {
            $category = $activity->getMetaValueByCode('_category');
            if (!in_array($category, $categories)) continue;
          }
        }

        if (!empty($companies)) {
          $companies = is_string($companies) ?  \json_decode($companies) : $companies;
          if (is_array($companies) && count($companies) > 0) {
            
            $activityCompanies = $activity->getInstantPartnersArray();
            $arr = array_filter($activityCompanies, function ($a) use ($companies) {
              return in_array($a["id"], $companies);
            });
            if (count($arr) === 0) continue;
          }
        }

        if (!empty($price)) {
          $activityPrice = \floatval($activity->getPrice());
          if ($price === "free" && $activityPrice > 0) continue;
          if ($price === "paid" && !($activityPrice > 0)) continue;
        }

        if (!empty($days)) {
          $days = is_string($days) ?  \json_decode($days) : $days;
        }

        $times = $activity->getInstantActivationTimesArray(true, $days);

        if (count($days) > 0 && count($times) < 1) continue;

        $data[] = [
          'id' => $activity->getUuid(),
          'companyId' => $activity->getOwner()->getCompany()?->getUuid(),
          'title' => $activity->getTitle(),
          'pictureUrl' => $activity->getMetaValueByCode('_picture'),
          'shortDescription' => $activity->getShortDescription(),
          'longDescription' => $activity->getLongDescription(),
          'categoryId' => $activity->getMetaValueByCode('_category'),
          'price' => \floatval($activity->getPrice()),
          'latitude' => \floatval($activity->getLatitude()),
          'longitude' => \floatval($activity->getLongitude()),
          'activationTimes' => $loadTimes  === 1 ? $times : [],
          'city' => $activity->getCity(),
          'country' => $activity->getCountry(),
          'active' => $activity->getStatus()
        ];
      }
    }

    $page = intval($request->query->get("page", "1"));
    $limit = intval($request->query->get("limit", "10"));

    if ($page < 1) $page = 1;
    if ($limit < 1) $limit = 10;

    $offset = ($page - 1) * $limit;

    return $this->result(array_slice($data, $offset, $limit));

    return $this->result($data);
  }

  public function businessActivity(string $uuid)
  {
    $activity = $this->activityRepo->getByUuid($uuid);
    
    $data = $this->toActivityInfo($activity);
    return $this->result($data);
  }

  public function deleteActivity(string $uuid)
  {

    $activity = $this->activityRepo->getByUuid($uuid);
    $data = [
      'id' => $activity->getUuid(),
      'title' => $activity->getTitle()
    ];
    $this->delete($activity);

    $this->logger->info("Activity deleted : " . $activity->getTitle());

    return $this->result($data);
  }

  public function getActivities(Request $request)
  {
    /**  @var \App\Entity\Security\User */
    $user = $this->security->getUser();

    $data = [];
    $onlyMyActivities = intval($request->query->get("onlyMyActivities", "0"));

    $activities = $onlyMyActivities === 1 ? $this->activityRepo->findBy(['owner' => $user]) : $this->activityRepo->findAll();
    $cities = $request->query->get("cities");
    $categories = $request->query->get("categoryIds");
    $companies = $request->query->get("companyIds");
    $days = $request->query->get("days");
    $price = $request->query->get("price");
    $lat = $request->query->get("lat");
    $lng = $request->query->get("lng");
    $date = $request->query->get("date");
    $name = $request->query->get("name");
    $distance = \floatval($request->query->get("distance"));

    foreach ($activities as $activity) {
      if (!empty($cities)) {
        $cities = is_string($cities) ?  \json_decode($cities) : $cities;
        if (is_array($cities) && count($cities) > 0) {
          if (in_array('En ligne', $cities) || in_array('Online', $cities)) {
            if (filter_var($activity->getStreetName(), FILTER_VALIDATE_URL) === FALSE) continue;
          } else {
            $city = trim(ucfirst(strtolower($activity->getCity())));
            if (!in_array($city, $cities)) continue;
          }
        }
      }

      if (!empty($categories)) {
        $categories = is_string($categories) ?  \json_decode($categories) : $categories;
        if (is_array($categories) && count($categories) > 0) {
          $category = $activity->getMetaValueByCode('_category');
          if (!in_array($category, $categories)) continue;
        }
      }

      if (!empty($companies)) {
        $companies = is_string($companies) ?  \json_decode($companies) : $companies;
        if (is_array($companies) && count($companies) > 0) {
          $company = $activity->getOwner()->getCompany()?->getUuid();
          if (!in_array($company, $companies)) continue;
        }
      }

      if (!empty($price)) {
        $activityPrice = \floatval($activity->getPrice());
        if ($price === "free" && $activityPrice > 0) continue;
        if ($price === "paid" && !($activityPrice > 0)) continue;
      }

      if (!empty($days)) {
        $days = is_string($days) ?  \json_decode($days) : $days;
      }

      if (!empty($name)) {
        if (strpos(strtolower($activity->getTitle()), strtolower($name)) === false) continue;
      }

      $times = $activity->getInstantActivationTimesArray(true, $days);

      if (!empty($lat) && !empty($lng)) {
        $times = array_filter($times, function ($time) use ($activity, $lat, $lng, $distance, $date) {
          $dt = !empty($date) ? date("Y-m-d", strtotime($date)) : time();
          return ($dt === date("Y-m-d", strtotime($time["startDateTime"])) ||
            $dt === date("Y-m-d", strtotime($time["endDateTime"]))) &&
            $activity->distanceInMetersFrom(\floatval($lat), \floatval($lng)) <= $distance;
        });
      }

      if (count($times) > 0) {
        $data[] = [
          'id' => $activity->getUuid(),
          'companyId' => $activity->getOwner()->getCompany()?->getUuid(),
          'title' => $activity->getTitle(),
          'pictureUrl' => $activity->getMetaValueByCode('_picture'),
          'shortDescription' => $activity->getShortDescription(),
          'categoryId' => $activity->getMetaValueByCode('_category'),
          'price' => \floatval($activity->getPrice()),
          'latitude' => \floatval($activity->getLatitude()),
          'longitude' => \floatval($activity->getLongitude()),
          'activationTimes' => $times,

        ];
      }
    }

    $items = [];

    foreach ($data as $value) {
      $items = array_merge($items, $value["activationTimes"]);
    }

    usort($items, function ($a, $b) {
      return (strtotime($a["startDateTime"]) - strtotime($b["startDateTime"]));
    });

    $page = intval($request->query->get("page", "1"));
    $limit = intval($request->query->get("limit", "10"));

    if ($page < 1) $page = 1;
    if ($limit < 1) $limit = 10;

    $offset = ($page - 1) * $limit;

    return $this->result(array_slice($items, $offset, $limit));
  }

  public function getActivityCities()
  {
    $data = [];

    $activities = $this->activityRepo->findAll();

    foreach ($activities as $activity) {

      if (!empty($activity->getCity())) {
        $city = trim(ucfirst(strtolower($activity->getCity())));
        if (!in_array($city, $data)) {
          $data[] = $city;
        }
      }
    }
    usort($data, 'strnatcasecmp');

    return $this->result($data);
  }

  public function getActivitiesAdmin()
  {
    $activities = $this->activityRepo->findAll();
    $output=[];
    
    foreach ($activities as $activity) {
      $output[]=$this->toActivityAdminInfo($activity);
    }
    return $this->result($output);


  }

  public function listPartners(Request $request)
  {

    /**  @var \App\Entity\Security\User */
    $user = $this->security->getUser();
    $showMine = intval($request->query->get("showMine", "0")) === 1 ? true : false;

    $list = [];
    $companies = $this->companyRepo->findAll();

    foreach ($companies as $value) {
      if (!empty($user->getCompany())) {
        $company = $user->getCompany();
        if ($company->getUuid() === $value->getUuid() && !$showMine) {
          continue;
        }
      }
      $pic  = $value->getMetaValueByCode('_profileImage');
      $list[] = [
        'id' => $value->getUuid(),
        'name' => $value->getName(),
        'picture' => !empty($pic) ? $pic : $value->getMetaValueByCode('_activity'),
      ];
    }
    return $this->result($list);
  }







}

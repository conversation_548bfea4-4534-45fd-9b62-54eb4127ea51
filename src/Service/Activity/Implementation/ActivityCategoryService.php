<?php

namespace App\Service\Activity\Implementation;

use App\Constant\DataFixtureFileNames;
use App\Constant\DefaultValues;
use App\Dto\GenericResponse;
use App\Entity\Activity\ActivityCategory;
use App\Mapper\Activity\Interface\IActivityCategoryMapper;
use App\Repository\Activity\ActivityCategoryRepository;
use App\Service\Traits\BaseServiceTrait;
use App\Service\Traits\LogServiceTrait;
use App\Service\Activity\Interface\IActivityCategoryService;
use App\Service\Traits\DataFixturesServiceTrait;
use Doctrine\Persistence\ObjectManager;
use Symfony\Component\HttpFoundation\Request;

class ActivityCategoryService implements IActivityCategoryService
{
  use BaseServiceTrait, LogServiceTrait, DataFixturesServiceTrait;

  private IActivityCategoryMapper $mapper;

  private ActivityCategoryRepository $repository;

  public function __construct(IActivityCategoryMapper $mapper, ActivityCategoryRepository $repository)
  {
    $this->mapper = $mapper;
    $this->repository = $repository;
  }

  public function getByUuid(string $uuid, bool $throwIfNotFound = true): ActivityCategory|null
  {
    return $this->repository->getByUuid($uuid, $throwIfNotFound);
  }

  public function loadFixtures(ObjectManager $manager): void
  {
    $data = $this->csvFileToArray(DataFixtureFileNames::ACTIVITY_CATEGORY);
    foreach ($data as $value) {
      $existingCategory = $this->getByUuid($value["UUID"], false);
      $activityCategory = empty($existingCategory) ? $this->mapper->fromArray($value) : $this->mapper->fromArrayUpdate($value, $existingCategory);
      $manager->persist($activityCategory);
    }
    $manager->flush();
  }

  public function findAll(Request $request): GenericResponse
  {
    $params = $this->mapper->fromFindAllRequest($request);
    $data = $this->repository->getItems($params);

    $outputs = $this->mapper->toOutArray($params->getOutput(), ...$data['items']);
    return $this->result([
      'items' => $outputs,
      'total' => $data['total'],
      'totalFiltered' => count($outputs)
    ]);
  }

  public function findOne(string $uuid, Request $request): GenericResponse
  {
    $outputFormat = $request->query->get("output", DefaultValues::DEFAULT_FIND_ONE_OUTPUT_FORMAT);
    $setting = $this->repository->getByUuid($uuid);
    $output = $this->mapper->toOut($outputFormat, $setting);

    return $this->result($output);
  }
}

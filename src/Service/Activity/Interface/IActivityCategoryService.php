<?php

namespace App\Service\Activity\Interface;

use App\Dto\GenericResponse;
use App\Entity\Activity\ActivityCategory;
use Doctrine\Persistence\ObjectManager;
use Symfony\Component\HttpFoundation\Request;

interface IActivityCategoryService
{

  /**
   * Does something interesting
   *
   * @param string  $uuid  Where something interesting takes place
   * @param bool  $throwIfNotFound  Where something interesting takes place
   * 
   * @throws Some_Exception_Class If something interesting cannot happen
   * <AUTHOR> <<EMAIL>>
   * @return ActivityCategory|null
   */
  public function getByUuid(string $uuid, bool $throwIfNotFound = true): ActivityCategory|null;

  /**
   * Does something interesting
   *
   * @param ObjectManager  $manager  Where something interesting takes place
   * 
   * <AUTHOR> <<EMAIL>>
   * @return void
   */
  public function loadFixtures(ObjectManager $manager): void;

  /**
   * Does something interesting
   *
   * @param Request $request  Where something interesting takes place
   * 
   * <AUTHOR> <<EMAIL>>
   * @return GenericResponse
   */
  public function findAll(Request $request): GenericResponse;

  /**
   * Does something interesting
   *
   * @param Request $request  Where something interesting takes place
   * 
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return GenericResponse
   */
  public function findOne(string $uuid, Request $request): GenericResponse;
}

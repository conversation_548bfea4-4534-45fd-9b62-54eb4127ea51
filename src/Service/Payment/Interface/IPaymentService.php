<?php

namespace App\Service\Payment\Interface;

use App\Dto\GenericResponse;
use Doctrine\Persistence\ObjectManager;
use Symfony\Component\HttpFoundation\Request;

interface IPaymentService
{

  /**
   * Does something interesting
   *
   * @param string  $uuid  Where something interesting takes place
   * @param bool  $throwIfNotFound  Where something interesting takes place
   * 
   * @throws Some_Exception_Class If something interesting cannot happen
   * <AUTHOR> <<EMAIL>>
   * @return ActivityCategory|null
   */
 // public function getByUuid(string $uuid, bool $throwIfNotFound = true): ActivityCategory|null;


}

<?php

namespace App\Service\Payment\Implementation;

use DateTime;
use DateTimeImmutable;
use App\Entity\Payment\Payment;
use App\Mapper\BaseMapperTrait;
use App\Entity\Payment\License;
use App\Entity\Payment\LicensePlan;
use App\Entity\Player\Player;
use App\Repository\Player\PlayerRepository;
use App\Service\Traits\LogServiceTrait;
use App\Service\Traits\BaseServiceTrait;
use Symfony\Bundle\SecurityBundle\Security;
use App\Repository\Shared\SettingRepository;
use App\Repository\Payment\PaymentRepository;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Repository\Payment\LicenseRepository;
use App\Repository\Payment\LicensePlanRepository;
use App\Service\Shared\Implementation\CommonService;

class LicensePaymentService{
    use BaseServiceTrait, LogServiceTrait, BaseMapperTrait;
    private LicenseRepository $licenseRepo;
    private SettingRepository $settingRepository;
    private CommonService $commonService;
    private Security $security;
    private LicensePlanRepository $licensePlanRepo;
    private PaymentRepository $paymentRepo;
    private PaymentService $paymentService;
    private PlayerRepository $playerRepository;
    
    public function __construct(
      Security $security, 
      LicenseRepository $licenseRepo, 
      SettingRepository $settingRepository, 
      CommonService $commonService, 
      LicensePlanRepository $licensePlanRepo,
      PaymentRepository $paymentRepo,
      PaymentService $paymentService,
      PlayerRepository $playerRepository
      ){
      $this->security = $security;
      $this->settingRepository = $settingRepository;
      $this->licenseRepo = $licenseRepo;
      $this->commonService = $commonService;
      $this->licensePlanRepo = $licensePlanRepo;
      $this->paymentRepo = $paymentRepo;
      $this->paymentService = $paymentService;
      $this->playerRepository = $playerRepository;
    }
    
    public function subscribeLicense(Request $request, $payUrl = null)
    {
      /**  @var \App\Entity\Security\User */
      $user = $this->security->getUser();

      $order_id = preg_replace('/-/', '', $this->commonService->uuid());//unique order_id
      $licensePlan = $this->licensePlanRepo->findOneBy(["title" => $request->get("license_plan"), "active" => true]);
      if (!$licensePlan) {
        $this->logger->warning('No {plan} active. No corresponding active License plan found !', [
          'license_plan' => $request->get("license_plan"),
        ]);
        return $this->result(["message" => "No corresponding active License plan found"], 404, Response::HTTP_NOT_FOUND);
      }

      // Check if player ID is provided
      $playerId = $request->get("playerId");
      if (!$playerId) {
        return $this->result(["message" => "Player ID is required"], 400, Response::HTTP_BAD_REQUEST);
      }

      // Find the player using the injected repository
      $player = $this->playerRepository->find($playerId);
      if (!$player) {
        return $this->result(["message" => "Player not found"], 404, Response::HTTP_NOT_FOUND);
      }

      // Check if player already has an active license of the same category
      $activeLicenses = $player->getLicenses()->filter(function($license) use ($licensePlan) {
          return $license->isActive() && $license->getLicensePlan()->getTitle() === $licensePlan->getTitle();
      });
      
      if (!$activeLicenses->isEmpty()) {
          return $this->result([
              "message" => "Player already has an active license for this category"
          ], 400, Response::HTTP_BAD_REQUEST);
      }

      $license_payment_price = ($request->request->get("spentFidelityAmount")) ? ($licensePlan->getPrice() - floatval($request->request->get("spentFidelityAmount"))) : $licensePlan->getPrice();
      $free = floatval($license_payment_price) <= 0;

      $payment = new Payment();
      $payment->setUser($user);
      $payment->setPayUrl($free ? null : $payUrl.$order_id);
      $payment->setCurrencyCode("XAF");
      $payment->setAmount($license_payment_price);
      $payment->setPhoneNumber($user->getPhoneNumber());
      $payment->setClientName($user->getDisplayName());
      $payment->setDescription($licensePlan->getTitle());
      $payment->setMailAddress($user->getEmail());
      $payment->setOrderID($order_id);
      $payment->setSpentFidelityAmount($request->request->get("spentFidelityAmount"));
      $payment->setStatus($free ? "FREE" :"INITIATED");
      $payment->setPaymentType("LICENSE");

      $this->persist($payment);
  
      $this->logger->info('license payment created : {order_id}', [
        'order_id' => $order_id,
      ]);
      if ($request->request->get("spentFidelityAmount")) {
       // $this->paymentService->updateFidelityBalance($user, $request->request->get("spentFidelityAmount"), "deduction");
       $this->logger->info("Temporarily, bonus points are not deducted for license payment");
      }
      
      $endDate = $this->calculateExpirationDate(new DateTimeImmutable());
  
      $license = new License();
      $license->setLicensePlan($licensePlan);
      $license->setEndDate($endDate);
      $license->setPayment($payment);
      $license->setStatus($free ? "ACTIVE" :"INITIATED");
      $license->setActive($free ? true : false); 
      $license->setUser($user);
      
      // Add the license to the player's collection
      $player->addLicense($license);
      
      $license->setChecked(false);
      $this->persist($license);
      $this->logger->info('License created for player: {playerId}', [
        'plan' => $licensePlan->getTitle(),
        'playerId' => $player->getId(),
      ]);
      
      if (!$free) {
       //notify user
             if ($user->getEmail()) {
          $template = $user->getMetaValueByCode("language","fr")=="fr" ? "pay-insurance-request":"pay-insurance-request-en";
  
          $this->commonService->sendMail2("[sportaabe ] Lien de paiement de licence", $payment, $template);
      } 
  
      if ($user->getPhoneNumber()) {
        $s = $user->getMetaValueByCode('language','fr') == "fr" ? "Hello, pour payer pour " . strtoupper($licensePlan->getTitle()) . ", utilisez ce lien: " . $payment->getPayUrl()." Merci d'utiliser sportaabe."
        :"Hello, to pay for " . strtoupper($licensePlan->getTitle()) . " , use this link: " . $payment->getPayUrl()." Thanks for using sportaabe";
        $this->commonService->sendAwsMessage($user->getPhoneNumber(), $s);
      }
    }elseif($free){
      if ($user->getEmail()) {
        $template = $user->getMetaValueByCode("language","fr")=="fr" ? "pay-insurance-success-free":"pay-insurance-success-free-en";
        $this->commonService->sendMail8($payment->getMailAddress(), "[sportaabe ] Paiement de licence réussi", $payment, $template);
    }

    if (!empty($payment->getPhoneNumber())) {
    
      $message = $user->getMetaValueByCode("language","fr")=="fr"? "Votre paiement pour ".strtoupper($licensePlan->getTitle())." d'une valeur de ".$licensePlan->getPrice()." XAF a réussi. Merci d'utiliser sportaabe":
      "Your sportaabe payment for ".strtoupper($licensePlan->getTitle())." for an amount of ".$licensePlan->getPrice()." XAF is successful. Thanks for using sportaabe";
      
      $this->commonService->sendAwsMessage($payment->getPhoneNumber(), $message);
    }
  }

  
      return $this->result($this->toLicenseInfo($license));
    }

    public function calculateExpirationDate(DateTimeImmutable $subscriptionDate): DateTimeImmutable
    {
        // Get subscription date components directly
        $year = (int) $subscriptionDate->format('Y');
        $month = (int) $subscriptionDate->format('n');
        
        // If month is after June (7-12), add a year
        if ($month > 6) {
            $year++;
        }
        
        // Create date directly for June 30th of target year
        return new DateTimeImmutable(sprintf('%d-06-30', $year));
    }

    public function updateLicenseAdmin(Request $request)
    {
        $license = $this->licenseRepo->findOneBy(["id" => $request->get("id")]);
        if (gettype($license) !== "object" || empty($license)) {
            $this->logger->warning('License not found : {id}', [
                'id' => $request->get("id"),
            ]);
            return $this->result(["message" => "License not found"], 404, Response::HTTP_NOT_FOUND);
        }
        $license->setActive($request->get("active"));
        $date = new DateTimeImmutable($request->get("endDate"));
        $license->setEndDate($date);
        $this->persist($license);
        $this->logger->info('License updated : {id}', [
            'id' => $request->get("id"),
        ]);
        return $this->result($this->toLicenseInfo($license));
    }

    public function getLicensesAdmin()
    {
        try {
            $licenses = $this->licenseRepo->findBy([], ['lastname' => 'ASC']);
            $grouped = [];
            
            foreach ($licenses as $license) {
                $company = $license->getUser()->getCompany();
                $companyName = $company ? $company->getName() : 'No Company';
                $category = $license->getCategory() ?? 'Uncategorized';
                
                if (!isset($grouped[$companyName])) {
                    $grouped[$companyName] = [];
                }
                if (!isset($grouped[$companyName][$category])) {
                    $grouped[$companyName][$category] = [];
                }
                
                $grouped[$companyName][$category][] = $this->toLicenseAdminInfo($license);
            }
            
            // Sort companies alphabetically
            ksort($grouped);
            
            // Sort categories within each company
            foreach ($grouped as &$companyData) {
                ksort($companyData);
            }
            
            return $this->result($grouped);
        } catch (\Exception $e) {
            return $this->result('', $e->getMessage());
        }
    }

    public function getLicensesPayments(Request $request)
    {
        try {
            $admin = $request->query->get("admin") == "false" ? false : true;

            /** @var \App\Entity\Security\User */
            $user = $this->security->getUser();

            $qb = $this->paymentRepo->createQueryBuilder('p');

            if ($admin) {
                $qb->orderBy('p.id', 'DESC');
            } else {
                $qb->andWhere('p.phoneNumber = :user1 OR p.mailAddress = :user2')
                  ->andWhere('p.paymentType = :paytype')
                  ->setParameter('user1', $user->getPhoneNumber())
                  ->setParameter('user2', $user->getEmail())
                  ->setParameter('paytype', 'LICENSE')
                  ->orderBy('p.id', 'DESC');
            }

            if ($request->query->get("last5") === "true") {
                $qb->setMaxResults(5);
            }

            $results = $qb->getQuery()->getResult();
            $payments = [];
            foreach($results as $result){
              $payment = $this->toLicensePaymentInfo($result,$this->licenseRepo);
              if($payment){
                $payments [] = $payment;
              }
            }

            return $this->result($payments);

        } catch (\Exception $e) {
            return $this->result([]);
        }
    }
    public function getLicenses()
    {
      try{
        $user = $this->security->getUser();
            $licenses = $this->licenseRepo->findBy(['user' => $user], ['lastname' => 'ASC']);
            $subs = [];
            foreach($licenses as $license){
              $subs[] = $this->toLicenseInfo($license);

            }
            return $this->result($subs);

        } catch (\Exception $e) {
            return $this->result([]);
        }
    }
    public function removeLicensePayment(mixed $id)
    {
      try {
        $license = $this->licenseRepo->findOneBy(['id' => $id, 'status' => ['INITIATED', 'EXPIRED'], 'active' => false]);
        if (!$license) {
          return $this->result(['message' => 'license not found or cannot be removed'], 404, Response::HTTP_NOT_FOUND);
        }
        $this->delete($license);
        if($license->getStatus() == 'INITIATED'){
          try{
            $this->delete($license->getPayment());
            $this->logger->info('License and linked payment removed : {id}', [
              'id' => $id,
            ]);

          }
          catch(\Exception $e){
            $this->logger->error($e->getMessage());
          }
            
        }else{
          $this->logger->info('License removed : {id}', [
            'id' => $id,
          ]);
          return $this->result(['message' => 'license removed'], 200, Response::HTTP_OK);
          
        }
      } catch (\Exception $e) {
        return $this->result(null, $e->getMessage());
      }
    }

    public function removeLicensePlan(mixed $id)
    {
      try {
        $licensePlan = $this->licensePlanRepo->findOneBy(['id' => $id, 'active' => false]);
        if (!$licensePlan) {
          return $this->result(['message' => 'license plan not found or cannot be removed'], 404, Response::HTTP_NOT_FOUND);
        }

        if($licensePlan->getLicenses()->count()==0)
        {
          $this->delete($licensePlan);
          $this->logger->info('License Plan removed : {id}', [
              'id' => $id,
            ]);
        }
        else{
          return $this->result(['message' => 'license plan cannot be removed because it has one or more licenses linked'], 404, Response::HTTP_NOT_FOUND);
        }
      
      } catch (\Exception $e) {
        return $this->result(null, $e->getMessage());
      }
    }

    public function getLicensePlans(Request $request)
    {
      $user = $this->security->getUser();
      $licensePlans = $request->query->get('admin') == "true"  && $user->getMetaValueByCode("is_admin") == true
      ? $this->licensePlanRepo->findBy(array(), array('price' => 'ASC')):
      $this->licensePlanRepo->findBy(array("active" => "1"), array('price' => 'ASC'));

      $plans = [];
      if ($request->query->get('admin') == "true")
      {
        foreach($licensePlans as $licensePlan){
          $plans [] = $this->toLicensePlanAdminInfo($licensePlan);
        }
      }
      else{
        foreach($licensePlans as $licensePlan){
          $plans [] = $this->toLicensePlanInfo($licensePlan);
        }
      }
      $plans = [];
    foreach($licensePlans as $licensePlan){
      $plans [] = ($request->query->get('admin') == "true") 
        ? $this->toLicensePlanAdminInfo($licensePlan) 
        : $this->toLicensePlanInfo($licensePlan);
    }
      return $this->result($plans);
    }


    public function getLicensePlan(mixed $id)
    {
      if(is_numeric($id)){
        $licensePlan = $this->licensePlanRepo->find($id);
      }else{
        $licensePlan = $this->licensePlanRepo->findOneBy(["title" => $id]);
      }
      if (!$licensePlan) {
        $this->logger->warning('License plan not found : {id}', [
          'id' => $id,
        ]);
        return $this->result(["message" => "License plan " . $id . " not found"], 404, Response::HTTP_NOT_FOUND);
      }
      return $this->result($this->toLicensePlanInfo($licensePlan));
    }

    public function updateLicensePlan(Request $request, mixed $id)
    {
      if(is_numeric($id)){
        $licensePlan = $this->licensePlanRepo->find($id);
      }else{
        $licensePlan = $this->licensePlanRepo->findOneBy(["title" => $id]);
      }

      if (gettype($licensePlan) !== "object" || empty($licensePlan)) {
        $this->logger->warning('License plan not found : {id}', [
          'id' => $id,
        ]);
        return $this->result(["message" =>  $id ." License plan not found"], 404, Response::HTTP_NOT_FOUND);
      }
      $user = $this->security->getUser();
      $isSuperAdmin = $user->getMetaValueByCode('is_super_admin',0);
      if($isSuperAdmin != 1){
        return $this->result(["message" => "Only super admins can update license plans"], 403, Response::HTTP_FORBIDDEN);
      }
      $licensePlan->setTitle($request->get("title"));
      $licensePlan->setDescription($request->get("description"));
      $licensePlan->setPrice($request->get("price"));
      $this->persist($licensePlan);
      $this->logger->info('License plan updated : {plan}', [
        'plan' => $licensePlan->getTitle(),
      ]);
      return $this->result($this->toLicensePlanInfo($licensePlan));
    }

    public function toggleLicensePlan(mixed $id)
    {
      if(is_numeric($id)){
        $licensePlan = $this->licensePlanRepo->find($id);
      }else{
        $licensePlan = $this->licensePlanRepo->findOneBy(["title" => $id]);
      }
      if (gettype($licensePlan) !== "object" || empty($licensePlan)) {
        $this->logger->warning('License plan not found : {id}', [
          'id' => $id,
        ]);
        return $this->result(["message" => "License plan not found"], 404, Response::HTTP_NOT_FOUND);
      }
      $user = $this->security->getUser();
      $isSuperAdmin = $user->getMetaValueByCode('is_super_admin',0);
      if($isSuperAdmin != 1){
        return $this->result(["message" => "Only super admins can enable or disable license plans"], 403, Response::HTTP_FORBIDDEN);
      }
      if($licensePlan->isActive()) {
        $licensePlan->setActive(false);
        $this->persist($licensePlan);
        $this->logger->info('License plan disabled : {plan}', [
          'title' => $licensePlan->getTitle(),
        ]);
      }else{
        $licensePlan->setActive(true);
        $this->persist($licensePlan);
        $this->logger->info('License plan enabled : {plan}', [
          'title' => $licensePlan->getTitle(),
        ]);
      }
      return $this->result($this->toLicensePlanInfo($licensePlan));
    }
    public function createLicensePlan(Request $request)
    {
      $user = $this->security->getUser();
      $isSuperAdmin = $this->security->getUser()->getMetaValueByCode('is_super_admin',0);
      if($isSuperAdmin != 1){
        return $this->result(["message" => "Only super admins can create license plans"], 403, Response::HTTP_FORBIDDEN);
      }
      $licensePlan = new LicensePlan();
      $licensePlan->setTitle($request->get("title"));
      $licensePlan->setDescription($request->get("description"));
      $licensePlan->setPrice($request->get("price"));
      $licensePlan->setActive(false);
      $this->persist($licensePlan);
      $this->logger->info('License plan created : {plan}', [
        'title' => $licensePlan->getTitle(),
      ]);
      return $this->result($this->toLicensePlanInfo($licensePlan));
    }
    
    public function checkCompanyTrials()
    {
      $companies = $this->companyRepo->findBy(["active" => true]);
      foreach ($companies as $company) {
        if (!empty($company->getMetaValueByCode('_trialEndDate'))) {
          $trialEndDate = $company->getMetaValueByCode('_trialEndDate');
          $trialEndDate = new DateTime($trialEndDate);
          $license = $this->licenseRepo->findOneBy(["active" => true, "company" => $company]);
  
          if ($trialEndDate < new DateTime() && is_null($license) ) {
            $this->logger->info('Company trial expired : {companyname}', [
              'companyname' => $company->getName(),
            ]);
            $company->setActive(false);
            $this->persist($company);
          }
        }
      }
      return $this->result([]);
      
    }
    public function checkLicenses(){
        $licenses = $this->licenseRepo->findBy(["active" => true]);

        if(empty($licenses)){
          return $this->result([]);
        }
        $notified = [];
        $expireds = [];
        $reminder_delay=$this->settingRepository->findOneBy(["code"=>"reminder_delay"])?->getValue();
        $now = new DateTime();
        foreach ($licenses as $license) {
          if($license->getEndDate() < $now ){
            $license->setStatus("EXPIRED");
            $license->setActive(false);
            $this->persist($license);
            $expireds[] = $license;
             
          }elseif(new \DateTime() >= $license->getEndDate()->modify('-'.($reminder_delay??'1').' days') && $license->getStatus() != "NOTIFIED"){
            $license->setStatus("NOTIFIED");
            $this->persist($license);
            
              $notified[] = $license;
            
          }

        }

    
         if( count($notified) > 0){
    
          foreach ($notified as $n) {
    
            if($n->getPayment()->getMailAddress()){
              $template = $n->getPayment()->getUser()->getMetaValueByCode("language","fr")=="fr"?"insurance-remind":"insurance-remind-en";
              $this->commonService->sendMail3($n->getPayment()->getMailAddress(), "[sportaabe ] Rappel d'expiration de licence", $n, $template, false);
            }
            if($n->getPayment()->getPhoneNumber()){
              $plan_name = strtoupper($n->getLicensePlan()->getTitle());
              $name = strtoupper($n->getLastname())." ".strtoupper($n->getFirstname());
              $end_date = $n->getEndDate()->format('d/m/Y H:i:s')." {$n->getEndDate()->getTimezone()->getName()}";
              $s = $n->getPayment()->getUser()->getMetaValueByCode("language","fr")=="fr"?"RAPPEL- LICENCE: Votre licence {$plan_name} pour {$name} arrivera à expiration ce {$end_date}. Pensez à vous réabonner pour en bénéficier de nouveau.":"REMINDER - LICENSE: Your license {$plan_name} for {$name} will expire on {$end_date}. Please do not forget to renew it to enjoy its benefits.";
              $this->commonService->sendAwsMessage($n->getPayment()->getPhoneNumber(), $s);
            }
        }
      }
        if( count($expireds) > 0){
    
          foreach ($expireds as $e) {
    
            if($e->getPayment()->getMailAddress()){
              $template = $e->getPayment()->getUser()->getMetaValueByCode("language","fr")=="fr"?"insurance-expired":"insurance-expired-en";
              $this->commonService->sendMail3($e->getPayment()->getMailAddress(), "[sportaabe ] Licence Expirée", $e, $template, false);
            }
            if($e->getPayment()->getPhoneNumber()){
              $plan_name = strtoupper($e->getLicensePlan()->getTitle());
              $name = strtoupper($e->getLastname())." ".strtoupper($e->getFirstname());
              $end_date = $e->getEndDate()->format('d/m/Y H:i:s')." {$e->getEndDate()->getTimezone()->getName()}";
              $s = $e->getPayment()->getUser()->getMetaValueByCode("language","fr")=="fr"?"LICENCE EXPIREE: Votre licence {$plan_name} pour {$name} a expiré ce {$end_date}. Veuillez re-souscrire pour en bénéficier de nouveau." : "LICENSE EXPIRED: Your license {$plan_name} for {$name} has expired on {$end_date}. Please renew to if you want to have access to its benefits.";
              $this->commonService->sendAwsMessage($e->getPayment()->getPhoneNumber(), $s);
            }
        }
      } 

    $notifs=[];
    $exps=[];

    foreach ($notified as $notif) {
      $notifs[] = $this->toLicenseInfo($notif);
    }
    foreach ($expireds as $exp) {
      $exps[] = $this->toLicenseInfo($exp);
    }
    
        return $this->result(["notified" => $notifs, "expireds" => $exps]);
    }

    public function validateLicense(mixed $id, bool $validate = true)
    {
        $license = $this->licenseRepo->findOneBy(['id' => $id]);
        if (!$license) {
            $this->logger->warning('License not found : {id}', [
                'id' => $id,
            ]);
            return $this->result(["message" => "License not found"], 404, Response::HTTP_NOT_FOUND);
        }

        $user = $this->security->getUser();
        if (!$user->getMetaValueByCode('is_admin', false)) {
            return $this->result(["message" => "Only administrators can validate or reject licenses"], 403, Response::HTTP_FORBIDDEN);
        }

        $license->setChecked($validate);
        $license->setStatus($validate ? "ACTIVE" : "REJECTED");
        $license->setActive($validate);
        $this->persist($license);
        if($validate){
          $player = $license->getPlayer();
          $player->setStatus('REGULAR');
          $this->persist($player);
        }

        $this->logger->info('License {action} : {id}', [
            'action' => $validate ? 'validated' : 'rejected',
            'id' => $id,
        ]);

        return $this->result($this->toLicenseInfo($license));
    }

}

<?php

namespace App\Service\Payment\Implementation;

use DateTime;
use Mailgun\Mailgun;
use Aws\Sns\SnsClient;
use DateTimeImmutable;
use App\Enum\LoginTypes;
use App\Dto\GenericResponse;
use App\Entity\Security\User;
use App\Enum\UserStatusTypes;
use App\Entity\Chat\Discussion;
use App\Entity\Company\Company;
use App\Entity\Payment\Payment;
use App\Mapper\BaseMapperTrait;
use App\Entity\Activity\Activity;
use App\Entity\Security\UserMeta;
use App\Entity\Company\CompanyMeta;
use App\Entity\Company\Subscription;
use App\Form\Common\ProfileUpdateIn;
use App\Entity\Activity\ActivityMeta;
use App\Form\Common\ProfileUpdateType;
use Kreait\Firebase\Contract\Database;
use App\Service\Traits\LogServiceTrait;
use App\Entity\Company\SubscriptionPlan;
use App\Service\Traits\BaseServiceTrait;
use App\Constant\GenericResponseStatuses;
use App\Repository\Chat\RequestRepository;
use App\Entity\Chat\Request as ChatRequest;
use App\Repository\Security\UserRepository;
use Symfony\Bundle\SecurityBundle\Security;
use App\Repository\Shared\SettingRepository;
use App\Entity\Chat\Response as ChatResponse;
use App\Exception\RequestValidationException;
use App\Repository\Company\CompanyRepository;
use App\Repository\Payment\PaymentRepository;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Repository\Activity\ActivityRepository;
use Symfony\Component\HttpKernel\KernelInterface;
use App\Repository\Company\SubscriptionRepository;
use App\Service\Security\Implementation\UserService;
use App\Service\Shared\Implementation\CommonService;
use App\Service\Payment\Implementation\OrangePayment;
use App\Repository\Company\SubscriptionPlanRepository;
use App\Repository\Security\AuthentificationCodeRepository;

class PaymentService
{
  use BaseServiceTrait, LogServiceTrait, BaseMapperTrait;

  private $appId = "0cb94376cd2260";
  private $appSecret = "2f5d91be5e1ea9e17cef70bf8499d9a1e2cb706511c261c10734f4ec9ceea27c";
  private $senderName = "sportaabe";

  private string $appEnv = "prod";

  //controller attributes
    /*public $subscriptionKey = "00e0e182886a4ac7bfa62a84015225e6";
  public $apiUser = "1adca557-34ec-44ea-9f64-8aacf99960c3";
  public $apiKey = "208b6dce6ef040729cf1c977b4796218";
  public $apiUrl = "https://sandbox.momodeveloper.mtn.com";
  public $targetEnvironment = "sandbox";
  public $currency = "EUR";*/

  public $subscriptionKey = "c4bd971632d24c009c1fc77e02ba983f";
  public $apiUser = "7e8571cf-2655-42b2-8e78-0b0e021d27ac";
  public $apiKey = "dc06eecfcd6247df86a413e80ea96b24";
  public $apiUrl = "https://proxy.momoapi.mtn.com";
  public $targetEnvironment = "mtncameroon";
  public $currency = "XAF";

  public $payerMessage = "Paiement sportaabe ";
  public $payeeNote = "Paiement sportaabe ";
  public $accessToken = "";

  public $mailGunSMTPHost = "smtp.mailgun.org";
  public $mailGunSMTPPort = 587;

  public $mailGunKey = "**************************************************";
  public $mailGunDomain = "mg.afrik.sportaabe.com";

  public $mailGunSenderName = "sportaabe ";
  public $mailGunSenderMail = "<EMAIL>";
  public $mailGunSenderPass = "**************************************************";

  /*public $currencyOrange = "OUV";
  public $paymentUrl = "https://api.orange.com/orange-money-webpay/dev/v1/webpayment";
  public $statusUrl = "https://api.orange.com/orange-money-webpay/dev/v1/transactionstatus";
  public $tokenUrl = "https://api.orange.com/oauth/v3/token";
  public $baseLink = "http://************";
  public $language = "fr";
  public $merchantName = "sportaabe SAS";
  public $merchantKey = "7630aa9c";
  public $authHeader = "Basic RjNVSDRqQzBxOTlCanV4MWY2Z0FPTXZPUEZsdE5NR1A6QWFPeU9lOHhMbjlIb04yRw==";
  public $productionMode = false;
  public $accessTokenOrange = "";*/

  public $currencyOrange = "XAF";
  public $paymentUrl = "https://api.orange.com/orange-money-webpay/cm/v1/webpayment";
  public $statusUrl = "https://api.orange.com/orange-money-webpay/cm/v1/transactionstatus";
  public $tokenUrl = "https://api.orange.com/oauth/v3/token";
  public $baseLink = "https://api.afrik.sportaabe.com";
  public $language = "fr";
  public $merchantName = "sportaabe";
  public $merchantKey = "5da13aba";
  public $authHeader = "Basic SGFsVmVLQTdWQTNvQW9qWktua1dRT0hRWjdCcmFvQWo6NG9xTWtCUGFEQkhmWUwwag==";
  public $productionMode = true;
  public $accessTokenMode = true;
  public $accessTokenOrange = "";

  private Security $security;


  private CompanyRepository $companyRepo;

  private ActivityRepository $activityRepo;

  private UserRepository $userRepo;
  private AuthentificationCodeRepository $otpRepository;

  private SettingRepository $settingRepo;

  private KernelInterface $appKernel;

  private SubscriptionPlanRepository $subscriptionPlanRepo;
  private SubscriptionRepository $subscriptionRepo;
  private RequestRepository $requestRepo;
  private CommonService $commonService;
  private PaymentService $service;
  private UserService $userService;
  private $gatewayService = 'collection';
  private PaymentRepository $paymentRepo;

  public function __construct(Security $security,PaymentRepository $paymentRepo, UserRepository $userRepo, CompanyRepository $companyRepo, ActivityRepository $activityRepo, SettingRepository $settingRepo, Database $database, KernelInterface $appKernel, AuthentificationCodeRepository $otpRepository, SubscriptionPlanRepository $subscriptionPlanRepo, SubscriptionRepository $subscriptionRepo, RequestRepository $requestRepo, CommonService $commonService, UserService $userService)
  {
    $this->security = $security;
    $this->paymentRepo = $paymentRepo;
    $this->userRepo = $userRepo;
    $this->companyRepo = $companyRepo;
    $this->activityRepo = $activityRepo;
    $this->settingRepo = $settingRepo;
    $this->appKernel = $appKernel;
    $this->otpRepository = $otpRepository;
    $this->subscriptionPlanRepo = $subscriptionPlanRepo;
    $this->subscriptionRepo = $subscriptionRepo;
    $this->requestRepo = $requestRepo;
    $this->commonService = $commonService;
    $this->userService = $userService;
  }
  
  public function updateSql(string $sql, array $data)
  {
    try {
      $stmt = $this->em->getConnection()->prepare($sql);
      $stmt->executeStatement($data);
      return true;
    } catch (\Exception $e) {
      dd($e);
      return false;
    }
  }

  
  public function createActivityPayment(Request $request, $payUrl)
  {
    /**  @var \App\Entity\Security\User */
    $user = $this->security->getUser();
    $now = new DateTime();

    //reservation delay setting
    $reservation_delay = $this->settingRepo->findOneBy(["code" => "reservation_delay"])->getValue();

    if (date_create_from_format('d/m/Y, H:i', $request->request->get("activity_date_time"))->modify('-' . ($reservation_delay ? $reservation_delay : '2') . ' hours') >= $now) {
  
      $order_id = preg_replace('/-/', '', $this->commonService->uuid());//unique order_id
      $exists = $this->getPayment1($request->request->get("activity_uuid"),$request->request->get("activity_date_time"), $user->getId())->getData();

      if (is_array($exists) && count($exists) > 0) {
        $this->writeInLogFile1("ORDER ID WAS ".json_encode($exists), 'DEBUG');
        return $this->result($exists[0]);
      }

      /**  @var \App\Entity\Security\User */
      $user = $this->security->getUser();


      $activity = $this->activityRepo->getByUuid($request->request->get("activity_uuid"));
      $activity_price = ($request->request->get("spentFidelityAmount")) ? ($activity->getPrice() - floatval($request->request->get("spentFidelityAmount"))) : $activity->getPrice();

      if ($request->request->get("spentFidelityAmount")) {
        $this->updateFidelityBalance($user, $request->request->get("spentFidelityAmount"), "deduction");
      }

      $params = [
        "pay_url" => $payUrl . $order_id,
        "currency_code" => "XAF",
        "id_activity" => $activity->getUuid(),
        "activity_name" => $activity->getTitle(),
        "description" => $activity->getShortDescription(),
        "amount" => $activity_price,
        "id_company" => $activity->getCreator()?->getCompany()?->getUuid(),
        "company_name" => $activity->getCreator()?->getCompany()?->getName(),
        "phone_number" => $user->getPhoneNumber(),
        "client_name" => $user->getDisplayName(),
        "mail_address" => $user->getEmail(),
        "order_id" => $order_id,
        "activity_date_time" => $request->request->get("activity_date_time"),
        "status" => "INITIATED",
        "spent_fidelity_amount" => $request->request->get("spentFidelityAmount"), 
        "user_id" => $user->getId(),
        "offer_to" => $request->request->get("offer_to_name").','.$request->request->get("offer_to_number").','.$request->request->get("offer_to_email"),
        "created_at" => $now->format("Y-m-d H:i:s"),
        "payment_type" => "ACTIVITY",
      ];


      $sql = "INSERT INTO payments 
          (pay_url, currency_code, id_activity,
            activity_name, description, amount, id_company, company_name, phone_number, client_name, mail_address, order_id, activity_date_time, status, spent_fidelity_amount, offer_to, user_id, created_at, payment_type) 
          VALUES (:pay_url, :currency_code, :id_activity, 
            :activity_name, :description, :amount, :id_company, :company_name, :phone_number, :client_name, :mail_address, 
            :order_id, :activity_date_time, :status, :spent_fidelity_amount, :offer_to, :user_id, :created_at, :payment_type)";

      $conn = $this->em->getConnection();
      $stmt = $conn->prepare($sql);
      $stmt->executeQuery($params);
      $idSaved = $conn->lastInsertId();
      $params["idSaved"] = $idSaved;

      $exists = $this->getPayment(intval($idSaved))->getData();
      $activityName = $this->commonService->cutActivityName($exists[0]['activity_name']);
      if (is_array($exists) && count($exists) > 0) {
        if ($user->getEmail()) {
          $template = $user->getMetaValueByCode("language","fr")=="fr" ? "pay-request":"pay-request-en";

          $this->commonService->sendMail2("[sportaabe ] Lien de paiement", $exists[0], $template);
        }
 
        if ("prod" == $this->appEnv && $user->getPhoneNumber()) {
          $s = $user->getMetaValueByCode('language','fr') == "fr" ? "Hello, pour payer pour " . $activityName . ", utilisez ce lien: " . $exists[0]['pay_url']." Merci d'utiliser sportaabe."
          :"Hello, to pay for: " . $activityName . ", use this link: " . $exists[0]['pay_url']." Thanks for using sportaabe";
          $this->commonService->sendAwsMessage($user->getPhoneNumber(), $s);
        }
        $this->logger->info("Payment created successfully with iD: " . $idSaved);
        return $this->result($exists[0]);
      }
      return $this->result(null);
    } else {
      return $this->result(
        $data = "It's too late to book this activity. Please try another one hours before it begins",
        $status = GenericResponseStatuses::FAILED,
        $code = Response::HTTP_CONFLICT,
        $messages = []
      );
    }
  }





  public function createFreeActivityPayment(Request $request)
  {
    
    /**  @var \App\Entity\Security\User */
    $user = $this->security->getUser();
    $now = new DateTime();

    //reservation delay setting
    $reservation_delay = $this->settingRepo->findOneBy(["code" => "reservation_delay"])->getValue();

    if (date_create_from_format('d/m/Y, H:i', $request->request->get("activity_date_time"))->modify('-' . ($reservation_delay ? $reservation_delay : '2') . ' hours') >= $now) {

      $order_id = preg_replace('/-/', '', $this->commonService->uuid());//unique order_id

      $exists = $this->getPayment1($request->request->get("activity_uuid"),$request->request->get("activity_date_time"), $user->getId())->getData();
      if (is_array($exists) && count($exists) > 0) {
        return $this->result($exists[0]);
      }

      $activity = $this->activityRepo->getByUuid($request->request->get("activity_uuid"));

      $params = [
        "pay_url" => null,
        "currency_code" => "XAF",
        "id_activity" => $activity->getUuid(),
        "activity_name" => $activity->getTitle(),
        "description" => $activity->getShortDescription(),
        "amount" => 0,
        "id_company" => $activity->getCreator()?->getCompany()?->getUuid(),
        "company_name" => $activity->getCreator()?->getCompany()?->getName(),
        "phone_number" => $user->getPhoneNumber(),
        "client_name" => $user->getDisplayName(),
        "mail_address" => $user->getEmail(),
        "order_id" => $order_id,
        "activity_date_time" => $request->request->get("activity_date_time"),
        "status" => "FREE",
        "spent_fidelity_amount" => null, 
        "user_id" => $user->getId(),
        "offer_to" => $request->request->get("offer_to_name").','.$request->request->get("offer_to_number").','.$request->request->get("offer_to_email"),
        "created_at" => $now->format("Y-m-d H:i:s"),
        "payment_type" => "ACTIVITY",
      ];


      $sql = "INSERT INTO payments 
          (pay_url, currency_code, id_activity,
            activity_name, description, amount, id_company, company_name, phone_number, client_name, mail_address, order_id, activity_date_time, status, spent_fidelity_amount, offer_to, user_id, created_at, payment_type) 
          VALUES (:pay_url, :currency_code, :id_activity, 
            :activity_name, :description, :amount, :id_company, :company_name, :phone_number, :client_name, :mail_address, 
            :order_id, :activity_date_time, :status, :spent_fidelity_amount, :offer_to, :user_id, :created_at, :payment_type)";


      $conn = $this->em->getConnection();
      $stmt = $conn->prepare($sql);
      $stmt->executeQuery($params);
      $idSaved = $conn->lastInsertId();
      $params["idSaved"] = $idSaved;

      $exists = $this->getPayment(intval($idSaved))->getData();

      $activityDate = $request->request->get("activity_date_time");
      $companyName = $activity->getCreator()?->getCompany()?->getName();
      $client = $user->getPhoneNumber() ? $user->getPhoneNumber() : $user->getEmail();

      if (is_array($exists) && count($exists) > 0) {
        if ($activity->getCreator()?->getCompany()->getMetaValueByCode('_email')) {
          $template = $user->getMetaValueByCode("language","fr")=="fr"?"pay-request-free":"pay-request-free-en";

          $this->commonService->sendMail4("[sportaabe ] Réservation", $exists[0], $activity->getCreator()?->getCompany()->getMetaValueByCode('_email'), $template);
        }

        if ("prod" == $this->appEnv && $activity->getCreator()?->getCompany()->getMetaValueByCode('_phoneNumber')) {
          $s = $user->getMetaValueByCode('language', 'fr') == "fr" ? $companyName . ", " . $client . " s’enregistre pour l’activité :" . $exists[0]['activity_name'] . " du " . $activityDate . " - L'équipe sportaabe"
          : $companyName . ", " . $client . " is registering for : " . $exists[0]['activity_name'] . " on " . $activityDate . " - The sportaabe team";
          $this->commonService->sendAwsMessage($activity->getCreator()?->getCompany()->getMetaValueByCode('_phoneNumber'), $s);
        }
        $this->logger->info("Free Payment created with ID: " . $idSaved);
        return $this->result($exists[0]);
      }

      return $this->result(null);
    } else {
      return $this->result(
        $data = "It's too late to book this activity. Please try another one hours before it begins",
        $status = GenericResponseStatuses::FAILED,
        $code = Response::HTTP_CONFLICT,
        $messages = []
      );
    }
  }
  public function updateFidelityBalance(User $user, $spentFidelityAmount, $operation)
  {
    if ($operation == "deduction") {
      $balance = floatval($user->getMetaValueByCode('fidelity_balance')) - floatval($spentFidelityAmount);
    } else {
      $balance = floatval($user->getMetaValueByCode('fidelity_balance')) + floatval($spentFidelityAmount);
    }
    $user->addMeta(
      (new UserMeta())
        ->setCode('fidelity_balance')
        ->setValue($balance)
    );
    $this->persist($user);
  }



  public function getActivitiesPayments(Request $request)
  {
    try {
      $admin = $request->query->get("admin") == "true" ? true : false;

      /**  @var \App\Entity\Security\User */
      $user = $this->security->getUser();


      if ($admin) {
        if($user->getMetaValueByCode('is_admin') !== "1") {
          return $this->result("You're not an admin", 403, Response::HTTP_FORBIDDEN,  []);
        }
        $sql = "SELECT * FROM payments ORDER BY id DESC";
        $stmt = $this->em->getConnection()->prepare($sql);
        $res = $stmt->executeQuery()->fetchAllAssociative();
      } else {
        $sql = "SELECT * FROM payments where (phone_number = :user1 or 	mail_address = :user2) and payment_type = :paytype ORDER BY id DESC";
        $params = array('user1' => $user->getPhoneNumber(), 'user2' => $user->getEmail(), 'paytype' => 'ACTIVITY');
        $stmt = $this->em->getConnection()->prepare($sql);
        $res = $stmt->executeQuery($params)->fetchAllAssociative();
      }
      if ($request->query->get("last5")==="true") {
        $res = array_slice($res,0, 5);
      }

      return $this->result($res);
    } catch (\Exception $e) {
      return $this->result([]);
    }
  }
  public function getActivitiesPaymentsByCompany(Request $request)
  {
    $user = $this->security->getUser();
    $company = $user->getCompany();
    $reservations = [];
    if (!$company) {
      return $this->result([]);
    } else {
      $payments = $this->paymentRepo->findBy(["idCompany" => $company->getUuid(), "status" => "SUCCESSFUL", "paymentType" => "ACTIVITY"], ["createdAt"=>"DESC"]);
      foreach($payments as $payment)
      {
        $reservations[] = $this->toPaymentByCompanyInfo($payment);
      }
      return $this->result($reservations);
    }
  }

  public function getSuccessfulPaymentsByMethod(Request $request)
  {
      try {
          
          $sql = $request->query->get("context")==="pro" ?
                "SELECT DATE(created_at) as date, payment_method, SUM(amount) as total_amount 
                  FROM payments 
                  WHERE status = 'SUCCESSFUL' AND payment_type = 'PRO'
                  GROUP BY date, payment_method 
                  ORDER BY date"
                  :"SELECT DATE(created_at) as date, payment_method, SUM(amount) as total_amount 
                  FROM payments 
                  WHERE status = 'SUCCESSFUL' AND payment_type = 'ACTIVITY'
                  GROUP BY date, payment_method 
                  ORDER BY date";
          
          $stmt = $this->em->getConnection()->prepare($sql);
          $results = $stmt->executeQuery()->fetchAllAssociative();
          
          $data = [];
          foreach ($results as $result) {
              $date = $result['date'];
              $paymentMethod = $result['payment_method'];
              $totalAmount = $result['total_amount'];
              
              if (!isset($data[$date])) {
                  $data[$date] = ['date' => $date];
              }
              
              $data[$date][$paymentMethod] = $totalAmount;
          }
          
          $responseData = [];
          foreach ($data as $date => $paymentMethods) {
              $responseData[] = $paymentMethods;
          }
          
          return $this->result($responseData);
      } catch (\Exception $e) {
          return $this->result([]);
      }
  }

  public function getPaymentsByStatus(string $status1, string $status2)
  {
    try {
      $sql = "SELECT * FROM payments where (status = :status1 or 	status = :status2) and payment_type = 'ACTIVITY' ORDER BY id DESC";
      $params = array('status1' => $status1, 'status2' => $status2);
      $stmt = $this->em->getConnection()->prepare($sql);
      return $stmt->executeQuery($params)->fetchAllAssociative();
    } catch (\Exception $e) {
      return [];
    }
  }

  public function getPayment1(string $activity_uuid, string $activity_date_time, int $user_id)
  {
    try {

      $sql = "SELECT * FROM payments where id_activity = :activity_uuid AND activity_date_time = :activity_date_time AND user_id = :user_id AND status IN ( 'INITIATED','PENDING') ORDER BY id DESC";
      $params = array('activity_uuid' => $activity_uuid,'activity_date_time' => $activity_date_time, 'user_id'=>$user_id); 
      $stmt = $this->em->getConnection()->prepare($sql);

      return $this->result($stmt->executeQuery($params)->fetchAllAssociative());
    } catch (\Exception $e) {
      return $this->result(null);
    }
  }

  public function getPayment(mixed $id)
  {
    try {
      if (is_int($id)) {
        $sql = "SELECT * FROM payments where id = :id ORDER BY id DESC";
        $params = array('id' => $id);
      } else {
        $sql = "SELECT * FROM payments where order_id = :order_id ORDER BY id DESC";
        $params = array('order_id' => $id);
      }

      $stmt = $this->em->getConnection()->prepare($sql);
      

      return $this->result($stmt->executeQuery($params)->fetchAllAssociative());
    } catch (\Exception $e) {
      return $this->result(null);
    }
  }

  public function removePayment(mixed $id)
  {
    try {
      $payment = $this->paymentRepo->findOneBy(['orderId' => $id, 'status' => ['INITIATED', 'PENDING', 'FAILED']]);
      if (!$payment) {
        return $this->result(['message' => 'payment not found or cannot be removed'], 404, Response::HTTP_NOT_FOUND);
      }
      if($payment->getPaymentType() === "ACTIVITY") {
        $this->delete($payment);
        return $this->result(['message' => 'payment removed'], 200, Response::HTTP_OK);
      }
      if($payment->getPaymentType() === "PRO"){
        $subscription = $this->subscriptionRepo->findOneBy(['payment' => $payment]);
        if($subscription){
          $this->delete($subscription);
          $this->logger->info('Subscription removed : {id}', [
            'id' => $subscription->getId(),     
          ]);
          $this->delete($payment);
          $this->logger->info('Payment removed : {id}', [
            'id' => $id,
          ]);
          return $this->result(['message' => 'payment and linked subscription removed'], 200, Response::HTTP_OK);

        }
        $this->delete($payment);
        $this->logger->info('Payment removed : {id}', [
          'id' => $id,
        ]);
        return $this->result(['message' => 'payment removed'], 200, Response::HTTP_OK);


      }
      $this->delete($payment);
    } catch (\Exception $e) {
      return $this->result(null, $e->getMessage());
    }
  }

  

  /**
   * Send MTN Mobile Money payment request
   */
  public function sendPaymentResquest($order, $customer_details, $methodId)
  {
    if ($this->isOrange($methodId)) {
      return OrangePayment::sendPaymentResquest($order,$this);
    }
    $transactionUuid = $this->commonService->uuid();
    $header = $this->setRequestHeader('payment_or_status_request', $transactionUuid);
    if (!$header) {
      $this->writeInlogFile('Setting payment request header failed', 'ERROR');
      return false;
    }

    $this->writeInlogFile('Setting payment request body ...', 'INFO');
    $data_string = $this->setRequestBody('payment_request', $order, null, $customer_details);
    if (!$data_string) {
      $this->writeInlogFile('Setting payment request body failed', 'ERROR');
      return false;
    }

    $this->writeInlogFile('Sending payment request ...', 'INFO');
    $endpoint = $this->getRequestUrl("createTransaction", []);
    $results = $this->sendRequest( $header, $data_string, $endpoint, true);
    if (is_numeric($results)) {
      if ($results == 202) {
        return [
          'status' => true,
          'tnxid' => $transactionUuid,
        ];
      }
    }
    return false;
  }

  /**
   * Send MTN Mobile Money payout request
   */
  public function sendPayoutResquest($order, $customer_details)
  {
    $transactionUuid = $this->commonService->uuid();
    $header = $this->setRequestHeader('payment_or_status_request', $transactionUuid);
    if (!$header) {
      $this->writeInlogFile('Setting payment request header failed', 'ERROR');
      return false;
    }

    $this->writeInlogFile('Setting payment request body ...', 'INFO');
    $data_string = $this->setRequestBody('payout_request', $order, null, $customer_details);
    if (!$data_string) {
      $this->writeInlogFile('Setting payment request body failed', 'ERROR');
      return false;
    }

    $this->writeInlogFile('Sending payment request ...', 'INFO');
    $endpoint = $this->getRequestUrl("createPayout", []);
    $results = $this->sendRequest( $header, $data_string, $endpoint, true);
    if (is_numeric($results)) {
      if ($results == 202) {

        return [
          'status' => true,
          'tnxid' => $transactionUuid,
        ];
      }
    }
    return false;
  }

  /**
   * Get MTN Mobile Money payment request
   */
  public function getPaymentResquest($tnxid, $methodId)
  {
    if ($this->isOrange($methodId)) {
      return OrangePayment::checkPaymentStatus($tnxid, $this);
    }
    $this->writeInlogFile('Initiating get payment request ...', 'INFO');
    $this->writeInlogFile('Setting get payment request header ...', 'INFO');

    $header = $this->setRequestHeader('get_payment_request');
    if (!$header) {
      $this->writeInlogFile('Setting get payment request header failed', 'ERROR');
      return false;
    }

    $url = $this->getRequestUrl('getTransaction', ['referenceId' => $tnxid]);

    $this->writeInlogFile('Sending get payment request ...', 'INFO');
    $results = $this->sendRequest( $header, null, $url, false, false);

    if (isset($results['externalId'])) {
      $this->writeInlogFile(' get payment request succeed ...', 'INFO');
      return $results;
    }
    return false;
  }

  /**
   * Generates access token
   */
  public function generateAccessToken($methodId)
  {
    if ($this->isOrange($methodId)) {
      return OrangePayment::generateAccessToken($this);
    }
    $this->writeInlogFile('Initiating Access Token creation request ...', 'INFO');
    $header = $this->setRequestHeader('access_token_request');
    if (!$header) {
      $this->writeInlogFile('Setting Access Token request header failed', 'ERROR');
      return false;
    }

    $this->writeInlogFile('Setting Access Token request body ...', 'INFO');
    $data_string = $this->setRequestBody('access_token_request');

    $this->writeInlogFile('Sending Access Token request ...', 'INFO');
    $tokenUrl = $this->getRequestUrl("token", []);
    $results = $this->sendRequest($header, $data_string, $tokenUrl);
    if (!$results) {
      return false;
    }

    if (!isset($results['access_token'])) {
      return false;
    }
    $this->writeInlogFile('Token creation successfull !!! New Token = ' . $results['access_token'], 'INFO');
    return $results['access_token'];
  }

  /**
   * Generates access token
   */
  public function checkNumber($tel)
  {
    $this->writeInlogFile('Initiating check number request ...', 'INFO');
    $header = $this->setRequestHeader('check_number_request');

    $this->writeInlogFile('Setting check number request body ...', 'INFO');
    $data_string = $this->setRequestBody('check_number_request');

    $this->writeInlogFile('Sending check number request ...', 'INFO');
    $endpointParams = array(
      "accountHolderId" => $tel,
    );
    $checkUrl = $this->getRequestUrl("accountholderActive", $endpointParams);
    $results = $this->sendRequest( $header, $data_string, $checkUrl, false, false);
    if (!$results) {
      return false;
    }

    if (!isset($results['result'])) {
      return false;
    }

    $this->writeInlogFile('Check number request successfull !!! Result = ' .  \json_encode($results), 'INFO');
    return $results['result'];
  }

  /**
   * Generates api user and api key
   */
  public function generateCredentials( $primaryKey, $providerCallbackHost)
  {
    $this->writeInlogFile('Initiating Api user creation request ...', 'INFO');
    $xReferenceId = $this->commonService->uuid();
    $header = [
      'X-Reference-Id: ' . $xReferenceId,
      'Content-Type: ' . 'application/json',
      'Ocp-Apim-Subscription-Key: ' . $primaryKey,
    ];

    $this->writeInlogFile('Setting Api user request body ...', 'INFO');
    $data_string =  \json_encode(['providerCallbackHost' => $providerCallbackHost]);
    $this->writeInlogFile('Sending Api user request ...', 'INFO');
    $apiUserUrl = $this->getRequestUrl("apiuser", []);
    $results = $this->sendRequest( $header, $data_string, $apiUserUrl, true);
    if ($results != 201) {
      return false;
    }
    $this->writeInlogFile('Api user creation successfull !!! New Api User = ' . $xReferenceId, 'INFO');
    $apiKeyUrl = $this->getRequestUrl("apikey", ['xReferenceId' => $xReferenceId]);
    $results2 = $this->sendRequest( $header, $data_string, $apiKeyUrl);
    if (!isset($results2['apiKey'])) {
      return false;
    }
    $this->writeInlogFile('Api key successfull !!! New Api key = ' .  \json_encode($results2), 'INFO');
    return [$xReferenceId, $results2['apiKey']];
  }

  /**
   * Sets the request header
   */
  public function setRequestHeader($operation, $transactionUuid = "")
  {

    $header = array();

    switch ($operation) {
      case 'payment_or_status_request':
        $h = [
          'Authorization: Bearer ' . $this->accessToken,
          'Content-Type: application/json',
          "X-Target-Environment: " . $this->targetEnvironment,
          'Ocp-Apim-Subscription-Key: ' . $this->subscriptionKey,
          "X-Reference-Id: " . $transactionUuid,
          'Accept: application/json',
        ];

        return $h;
      case 'get_payment_request':
        return [
          'Authorization: Bearer ' . $this->accessToken,
          'Content-Type: application/json',
          "X-Target-Environment: " . $this->targetEnvironment,
          'Ocp-Apim-Subscription-Key: ' . $this->subscriptionKey,
          'Accept: application/json',
        ];
      case 'check_number_request':
        return [
          'Authorization: Bearer ' . $this->accessToken,
          "X-Target-Environment: " . $this->targetEnvironment,
          'Ocp-Apim-Subscription-Key: ' . $this->subscriptionKey,
          'Accept: application/json',
        ];
      case 'access_token_request':
        $encodedString = base64_encode($this->apiUser . ':' . $this->apiKey);
        return [
          'Ocp-Apim-Subscription-Key: ' . $this->subscriptionKey,
          'Authorization: Basic ' . $encodedString,
          'Accept: application/json',
        ];

      default:
        return false;
    }
  }

  /**
   * Sets request body
   */
  public function setRequestBody($operation,  $order = null, $payment_details = null, $customer_details = null)
  {

    switch ($operation) {
      case 'payment_request':
        $data = [
          "amount" => (float) $order['amount'],
          "currency" => $this->currency,
          "externalId" => $order['reference'],
          'payer' => [
            "partyIdType" => 'MSISDN',
            "partyId" => $customer_details['customer_tel'],
          ],
          "payerMessage" => $this->payerMessage,
          "payeeNote" => $this->payeeNote,
        ];

        return  \json_encode($data);
      case 'payout_request':
        $data = [
          "amount" => (float) $order['amount'],
          "currency" => $this->currency,
          "externalId" => $order['reference'],
          'payee' => [
            "partyIdType" => 'MSISDN',
            "partyId" => $customer_details['customer_tel'],
          ],
          "payerMessage" => $this->payerMessage,
          "payeeNote" => $this->payeeNote,
        ];
        return  \json_encode($data);
      case 'status_request':
        $data = array();
        $data['order_id'] = $payment_details->ref_order;
        $data['amount'] = $payment_details->payment_amount;
        $data['pay_token'] = $payment_details->pay_token;
        return  \json_encode($data);

      case 'access_token_request':
        return [];
      case 'check_number_request':
        return [];
      default:
        return false;
    }
  }

  /**
   * Send cURL request
   */
  public function sendRequest( $header, $data_string, $url, $returnCode = false, $post = true)
  {

    $this->writeInlogFile('Request header ' .  \json_encode($header), 'INFO');
    $this->writeInlogFile('Request body ' .  \json_encode($data_string), 'INFO');
    $this->writeInlogFile('Request URL ' . $url, 'INFO');

    $ch = \curl_init();
    \curl_setopt($ch, CURLOPT_URL, $url);
    \curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    \curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    \curl_setopt($ch, CURLOPT_HTTPHEADER, $header);

    if ($data_string) {
      \curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
    } else {
      \curl_setopt($ch, CURLOPT_POSTFIELDS, null);
    }
    if ($post) {
      \curl_setopt($ch, CURLOPT_POST, true);
    } else {
      \curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
    }

    $result_response = \curl_exec($ch);

    $results = array();

    if (curl_errno($ch) > 0) {
      $this->writeInlogFile('There was a cURL error: ' . \curl_error($ch), 'ERROR');
      \curl_close($ch);
      return false;
    } else {
      $http_code = \curl_getinfo($ch, CURLINFO_HTTP_CODE);
      $this->writeInlogFile('Request response  ' . $result_response, 'INFO');
      $this->writeInlogFile('Request response  code ' . $http_code, 'INFO');

      if ($returnCode) {
        return $http_code;
      }

      $results =  \json_decode($result_response, true);
      \curl_close($ch);

      if ($results) {
        return $results;
      } else {
        $this->writeInlogFile('Decoding JSON failed with the following message: ' . json_last_error_msg(), 'ERROR');
        return false;
      }
    }
  }

  /**
   * Check access token integrity
   */
  public function check_access_token()
  {

    if (empty($this->accessToken)) {
      $this->writeInlogFile('Access token is not provided. a new one should be generated.', 'INFO');
      return false;
    }
    if ($this->accessTokenMode != $this->productionMode) {
      $this->writeInlogFile('Access token not matching production mode. a new one should be generated.', 'INFO');
      return false;
    }

    return true;
  }

  /**
   * URL
   */
  public function getRequestUrl(string $endpointName, array $params = []): string
  {
    $s = isset($this->gatewayService) ? $this->gatewayService : 'collection';
    switch ($endpointName) {
      case 'token':
        $urlSegment = '/' . $s . '/token/'; // trailing slash mandatory
        break;

      case 'createTransaction':
        $urlSegment = '/collection/v1_0/requesttopay';
        break;
      case 'createPayout':
        $urlSegment = '/disbursement/v1_0/transfer';
        break;

      case 'getTransaction':
        $urlSegment = "/collection/v1_0/requesttopay/{$params['referenceId']}";
        break;
      case 'getPayout':
        $urlSegment = "/disbursement/v1_0/transfer/{$params['referenceId']}";
        break;

      case 'balance':
        $urlSegment = '/collection/v1_0/account/balance';
        break;

      case 'accountholderActive':
        $urlSegment = "/collection/v1_0/accountholder/msisdn/{$params['accountHolderId']}/active";
        break;

      case 'apiuser':
        $urlSegment = '/v1_0/apiuser';
        break;
      case 'apikey':
        $urlSegment = '/v1_0/apiuser/' . $params['xReferenceId'] . "/apikey";
        break;
      default:
        throw new \Exception("Unknown Api endpoint - {$endpointName}.");
        break;
    }

    return $this->apiUrl . $urlSegment;
  }

    /**
   * Writes in the payments log files
   */
  public function writeInLogFile($data, $level)
  {
    $day = gmdate("Y-m-d");
    $logfile = $this->appKernel->getProjectDir() . '/var/log/momo_payments_' . $day . '.log';
    error_log("\r\n" . '[' . gmdate("Y-m-d H:i:s") . '] ' . $level . ' ' . $data, 3, $logfile);
  }

     /**
   * Writes vardump in the log files
   */
  public function writeInLogFile1($data, $level)
  {
    $day = gmdate("Y-m-d");
    $logfile2 = $this->appKernel->getProjectDir() . '/var/log/vardumps' . $day . '.log';
    error_log("\r\n" . '[' . gmdate("Y-m-d H:i:s") . '] ' . $level . ' ' . $data, 3, $logfile2);
  }

  private function getResult($data, $status = 'ok', $code = 200)
  {
    return $this->result(new GenericResponse($data, $status, $code, []));
  }

  public function processPayment(Request $request)
  {
    $body = $request->request->all();

    $payment = $this->getPayment(intval($body["paymentRequest"]))->getData()[0];
    $this->writeInlogFile1('PAYMENT DETAILS ======   ' . json_encode($payment), 'DEBUG');

    if ($payment == null) {
      return $this->getResult(null, 'nok', 404);
    }

    $methodId = $body["methodId"];

    if ($methodId == "mtn" || $methodId == "orange") {
      //$token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJSMjU2In0.eyJjbGllbnRJZCI6IjdlODU3MWNmLTI2NTUtNDJiMi04ZTc4LTBiMGUwMjFkMjdhYyIsImV4cGlyZXMiOiIyMDIzLTExLTAxVDEyOjM2OjMyLjU4MCIsInNlc3Npb25JZCI6IjUxNDQ5ZTZjLThmNDMtNGExYi1hYTI4LTQzZjBmYzc3MDczNyJ9.VdYMMNgqk-5HNwKx4C9pt7jC6zlu_PxMDQQk8ZJzJHDrHF72fG45EeYKytEXC17nJtCQMN0-Kx2h5auTVPdxd3BIXd-pjLdGVmyl7qUXdRkvAhhkD2ZcE7UujJ54U5A395IiXrwr5H6T6LoQet7vG3O88i9RYvGK5HJ80AbGY5f7kKJK-R-Qd0RDH8YLOSlQTQSGCQYAwcY-LDAMTZoSirs95QZhHikMJllg0f8zu3cDfPsEjW8C6ygqAAjCNdFJ5Rw8lyAbRXyDLVb2lfzjFeG6ef8o07u72yuWdKSa1XjR-hdFD3YnMnuAE1XVBZjtWBpExr4gFuG_O4wVQwZ5iw";
      //$token = "*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";

      $token = $this->generateAccessToken($methodId);
      if ($token) {
        $this->accessToken = $token;

        if ($methodId == "mtn") {
          $phone = $body["customer"]["mtnPayPhone"];
          /*$chectNumber = [
            "result" => true
          ];*/
          $phone = str_replace("+", "", $phone);
          if (strpos($phone, '237') !== 0) {
              $phone = '237' . $phone;
          }
          $phone = strpos($phone, '237') !== 0 ? '237' . $phone : $phone;
          $chectNumber = $this->checkNumber($phone);
          if ($chectNumber) {
            $customer_details = array(
              'customer_tel' => $phone,
            );
            $order = array(
              'amount' => $payment['amount'],
              'reference' => $payment['order_id'],
            );
            /*$result = [
              "status" => true,
              "tnxid" => "1a7c88a6-7f8a-4da8-938e-d2315c2b9259"
            ];*/
            $result =  $this->sendPaymentResquest($order, $customer_details, $methodId);
            if ($result) {
              //dump($result);
              if (isset($result['tnxid'])) {
                $this->writeInlogFile('Result ======   ' . json_encode($result), 'INFO');
                $payment['transaction_id'] = $result['tnxid'];
                $payment['payment_method'] = "mtn";
                $payment['status'] =  "PENDING";

                $sql = "UPDATE payments set transaction_id = :transaction_id, payment_method = :payment_method, status = :status WHERE order_id = :order_id";
                $data = [
                  'transaction_id' => $payment['transaction_id'],
                  'payment_method' => $payment['payment_method'],
                  'status' => $payment['status'],
                  'order_id' => $payment['order_id'],
                ];

                $this->updateSql($sql, $data);
                return $this->result($payment);
              }
            }
          }
        } else if ($methodId == "orange") {
          $order = array(
            'ref_order' => $payment['order_id'],
            'amount' => $payment['amount'],
          );
          $this->writeInlogFile1('ORDER ======   ' . json_encode($order), 'DEBUG');

          $result = $this->sendPaymentResquest($order, array(), $methodId);
          /*$result = [
            "ref_order" => "7d6f1311-cb10-41b0-a1fd-13ce83074af2",
            "amount" => "50.00",
            "status" => "PENDING",
            "pay_token" => "v1lvpyhga0gacgzhsac1qeb3p4wni1xhp41bzord5zlyj46hhamkns9svgistn8b",
            "notif_token" => "hedmvdylzlrutmoa5verxtafjneak5rs",
            "payment_url" => "https://mpayment.orange-money.com/cm/mpayment/abstract/v1lvpyhga0gacgzhsac1qeb3p4wni1xhp41bzord5zlyj46hhamkns9svgistn8b"
          ];*/
          $this->writeInlogFile1('CONTENU ======   ' . json_encode(isset($result['pay_token'])), 'DEBUG');
          if (isset($result['pay_token'])) {
            $this->writeInlogFile('Result ======   ' . json_encode($result), 'INFO');
            $this->writeInlogFile1('RESULTAT ======   ' . json_encode($result), 'DEBUG');

            $payment['payment_method'] = "orange";
            $payment['orange_pay_url'] = $result['payment_url'];
            $payment['pay_token'] = $result['pay_token'];
            $payment['notif_token'] = $result['notif_token'];
            $payment['status'] = $result['status'];

            $sql = "UPDATE payments set pay_token = :pay_token,  notif_token = :notif_token, orange_pay_url = :orange_pay_url, payment_method = :payment_method, status = :status WHERE order_id = :order_id";

            $data = [
              'pay_token' => $payment['pay_token'],
              'notif_token' => $payment['notif_token'],
              'orange_pay_url' => $payment['orange_pay_url'],
              'payment_method' => $payment['payment_method'],
              'status' => $payment['status'],
              'order_id' => $payment['order_id'],
            ];
            
            $this->updateSql($sql, $data);
            return $this->result($payment);
          }
        }
      }
    }

    throw new \Exception("Erreur lors du traitement du paiement.");
  }

  public function checkPayment(int $id)
  {
    $payment = $this->getPayment($id)->getData()[0];

    if ($payment == null) {
      return $this->getResult(null, 'nok', 404);
    }

    $methodId = $payment['payment_method'];

    if ($methodId == "mtn" || $methodId == "orange") {
      $token = $this->generateAccessToken( $methodId);
      if ($token) {
        $this->accessToken = $token;
        $em = $this->em;
        $user = $this->userRepo->findOneBy(["id"=>$payment["user_id"]]);

        if ("mtn" === $methodId) {
          $tnx = $this->getPaymentResquest($payment['transaction_id'], $methodId);
          if ($tnx) {
            $this->writeInlogFile('get Payment request successfull !!!', 'INFO');
            if (isset($tnx['status'])) {
              $payment['status'] = $tnx['status'];
              $payment['financial_transaction_id'] = $tnx['financialTransactionId'];



              $sql = "UPDATE payments set financial_transaction_id = :financial_transaction_id,  status = :status WHERE order_id = :order_id";
              $data = [
                'financial_transaction_id' => $payment['financial_transaction_id'],
                'status' => $payment['status'],
                'order_id' => $payment['order_id'],
              ];
              $this->updateSql($sql, $data);

              if ("SUCCESSFUL" === $payment['status']) {

                if ($payment['payment_type'] == "PRO") {

                  $sql = "UPDATE company_subscriptions set active = :active, status = :status WHERE payment_id = :payment_id";
                  $data = [
                    'active' => true,
                    'status' => "ACTIVE",
                    'payment_id' => $payment['id'],
                  ];
                  $this->updateSql($sql, $data);
                  $this->logger->info('payment update successful !!!');
                  $company = $user->getCompany();
                  $company->setActive(true);
                  $this->persist($company);
                  
                  if(!empty($payment['mail_address'])){
                    $template = "pay-subscription-success";
                    if(!is_null($user))
                    {
                      $template = $user->getMetaValueByCode("language","fr")=="fr"?"pay-subscription-success":"pay-subscription-success-en";
    
                    }
                    $this->commonService->sendMail7($payment['mail_address'], "[sportaabe ] Paiement d'abonnement réussi", $payment, $template);
                  }
                  if (!empty($payment['phone_number'])) {
    
                    $message = $user->getMetaValueByCode("language","fr")=="fr"? "Votre paiement d'abonnement ayant pour Transaction ID: ".$payment['financial_transaction_id']." et pour Order ID: ".$payment['order_id']." pour un montant de ".$payment['amount']."XAF a réussi. Merci d'utiliser sportaabe":
                    "Your subscription payment with Transaction ID: ".$payment['financial_transaction_id']." and Order ID: ".$payment['order_id']." for an amount of ".$payment['amount']."XAF is successful. Thanks for using sportaabe";
                    
                    $this->commonService->sendAwsMessage($payment['phone_number'], $message);
                  }
                }elseif($payment['payment_type'] == "LICENSE"){

                  $sql = "UPDATE licenses set active = :active, status = :status WHERE payment_id = :payment_id";
                  $data = [
                    'active' => true,
                    'status' => "ACTIVE",
                    'payment_id' => $payment['id'],
                  ];
                  $this->updateSql($sql, $data);
                  $this->logger->info('payment update successful !!!');

                  
                  if(!empty($payment['mail_address'])){
                    $template = "pay-insurance-success";
                    if(!is_null($user))
                    {
                      $template = $user->getMetaValueByCode("language","fr")=="fr"?"pay-insurance-success":"pay-insurance-success-en";
    
                    }
                    $this->commonService->sendMail8($payment['mail_address'], "[sportaabe ] Paiement de licence réussi", $payment, $template);
                  }
                  if (!empty($payment['phone_number'])) {
    
                    $message = $user->getMetaValueByCode("language","fr")=="fr"? "Votre paiement de licence sportaabe ayant pour Transaction ID: ".$payment['financial_transaction_id']." et pour Order ID: ".$payment['order_id']." pour un montant de ".$payment['amount']."XAF a réussi. Merci d'utiliser sportaabe":
                    "Your sportaabe license payment with Transaction ID: ".$payment['financial_transaction_id']." and Order ID: ".$payment['order_id']." for an amount of ".$payment['amount']."XAF is successful. Thanks for using sportaabe";
                    
                    $this->commonService->sendAwsMessage($payment['phone_number'], $message);
                  }
                
                }elseif($payment['payment_type'] == "ACTIVITY"){
                  if(!empty($payment['mail_address'])){
                  $template = "pay-success";
                  if(!is_null($user))
                  {
                    $template = $user->getMetaValueByCode("language","fr")=="fr"?"pay-success":"pay-success-en";

                  }
                  
                  $this->commonService->sendMail6($payment['mail_address'], "[sportaabe ] Paiement réussi", $payment, $template);
                
                  if(!empty($payment['offer_to'])){
                    $offer_to = explode(',', $payment["offer_to"]);
                    if(!empty($offer_to[2])){
                      if(!is_null($user))
                      {
                        $template = $user->getMetaValueByCode("language","fr")=="fr"?"pay-success-offer":"pay-success-offer-en";
    
                      }
                      $this->commonService->sendMail6($payment['mail_address'], "[sportaabe ] Réservation à votre nom", $payment, $template);
                    }
                  }
                  }
                if (!empty($payment['phone_number'])) {
                  $message = $user->getMetaValueByCode("language","fr")=="fr"? "Votre paiement ayant pour Transaction ID: ".$payment['financial_transaction_id']." et pour Order ID: ".$payment['order_id']." pour un montant de ".$payment['amount']."XAF a réussi. Merci d'utiliser sportaabe":
                  "Your payment with Transaction ID: ".$payment['financial_transaction_id']." and Order ID: ".$payment['order_id']." for an amount of ".$payment['amount']."XAF is successful. Thanks for using sportaabe";
                  $this->commonService->sendAwsMessage($payment['phone_number'], $message);

                  if(!empty($payment['offer_to'])){
                    $offer_to = explode(',', $payment["offer_to"]);
                    if(!empty($offer_to[1])){
                      if(!is_null($user))
                      {
                        $message = $user->getMetaValueByCode("language","fr")=="fr"? $payment["client_name"]." vous offre une réservation pour l'activité : ".$payment['activity_name']." chez : ".$payment['company_name']." qui se déroulera le ".$payment['activity_date_time'].". Merci d'utiliser sportaabe":
                        $payment["client_name"]." is offering you a reservation for : ".$payment['activity_name']." at : ".$payment['company_name']." which will take place on ".$payment['activity_date_time'].". Thanks for using sportaabe";
                        
                      }
                      $this->commonService->sendAwsMessage($payment['phone_number'], $message);
                    }
                  }
                }
              }
              
              return $this->result($payment);
            }
            }}
        } else if ("orange" === $methodId) {
          $order = array(
            'ref_order' => $payment['order_id'],
            'amount' => $payment['amount'],
            'pay_token' => $payment['pay_token'],
          );
          $tnx = $this->getPaymentResquest($order, $methodId);
          if (isset($tnx['status'])) {
            $this->writeInlogFile('get Payment request successfull !!!', 'INFO');

            $payment['status'] = $tnx['status'];
            $payment['financial_transaction_id'] = $tnx['txnid'];


            $sql = "UPDATE payments set financial_transaction_id = :financial_transaction_id,  status = :status WHERE order_id = :order_id";
            $data = [
              'financial_transaction_id' => $payment['financial_transaction_id'],
              'status' => $payment['status'],
              'order_id' => $payment['order_id'],
            ];
            $this->updateSql($sql, $data);
            $this->logger->info('payment update successful !!!');
            $company = $user->getCompany();
            $company->setActive(true);
            $this->persist($company);

            
            if ("SUCCESSFUL" === $payment['status']) {
              if ($payment['payment_type'] == "PRO") {

                $sql = "UPDATE company_subscriptions set active = :active, status = :status WHERE payment_id = :payment_id";
                $data = [
                  'active' => true,
                  'status' => "ACTIVE",
                  'payment_id' => $payment['id'],
                ];
                $this->updateSql($sql, $data);

                if(!empty($payment['mail_address'])){
                  $template = "pay-subscription-success";
                  if(!is_null($user))
                  {
                    $template = $user->getMetaValueByCode("language","fr")=="fr"?"pay-subscription-success":"pay-subscription-success-en";
  
                  }
                  $this->commonService->sendMail7($payment['mail_address'], "[sportaabe ] Paiement d'abonnement réussi", $payment, $template);
                }
                if (!empty($payment['phone_number'])) {
                  $plan = strtoupper($payment['description']);  
                  $message = $user->getMetaValueByCode("language","fr")=="fr"? "Votre paiement d'abonnement {$plan} ayant pour Transaction ID: ".$payment['financial_transaction_id']." pour un montant de ".$payment['amount']."XAF a réussi. Merci d'utiliser sportaabe":
                  "Your {$plan} subscription payment with Transaction ID: ".$payment['financial_transaction_id']." for an amount of ".$payment['amount']."XAF is successful. Thanks for using sportaabe";
                  
                  $this->commonService->sendAwsMessage($payment['phone_number'], $message);
                }
              }elseif($payment['payment_type'] == "LICENSE"){

                $sql = "UPDATE licenses set active = :active, status = :status WHERE payment_id = :payment_id";
                $data = [
                  'active' => true,
                  'status' => "ACTIVE",
                  'payment_id' => $payment['id'],
                ];
                $this->updateSql($sql, $data);
                $this->logger->info('payment update successful !!!');

                
                if(!empty($payment['mail_address'])){
                  $template = "pay-insurance-success";
                  if(!is_null($user))
                  {
                    $template = $user->getMetaValueByCode("language","fr")=="fr"?"pay-insurance-success":"pay-insurance-success-en";
  
                  }
                  $this->commonService->sendMail8($payment['mail_address'], "[sportaabe ] Paiement de licence réussi", $payment, $template);
                }
                if (!empty($payment['phone_number'])) {
  
                  $message = $user->getMetaValueByCode("language","fr")=="fr"? "Votre paiement de licence sportaabe ayant pour Transaction ID: ".$payment['financial_transaction_id']." et pour Order ID: ".$payment['order_id']." pour un montant de ".$payment['amount']."XAF a réussi. Merci d'utiliser sportaabe":
                  "Your sportaabe license payment with Transaction ID: ".$payment['financial_transaction_id']." and Order ID: ".$payment['order_id']." for an amount of ".$payment['amount']."XAF is successful. Thanks for using sportaabe";
                  
                  $this->commonService->sendAwsMessage($payment['phone_number'], $message);
                }
              
              }else{

              if(!empty($payment['mail_address'])){
                $template = "pay-success";
                if(!is_null($user))
                {
                  $template = $user->getMetaValueByCode("language","fr")=="fr"?"pay-success":"pay-success-en";

                }
                $this->commonService->sendMail6($payment['mail_address'], "[sportaabe ] Paiement réussi", $payment, $template);
                if(!empty($payment['offer_to'])){
                  $offer_to = explode(',', $payment["offer_to"]);
                  if(!empty($offer_to[2])){
                    if(!is_null($user))
                    {
                      $template = $user->getMetaValueByCode("language","fr")=="fr"?"pay-success_offer":"pay-success_offer-en";
  
                    }
                    $this->commonService->sendMail6($payment['mail_address'], "[sportaabe ] Réservation à votre nom", $payment, $template);
                  }
                }
              }
              if (!empty($payment['phone_number'])) {

                $message = $user->getMetaValueByCode("language","fr")=="fr"? "Votre paiement ayant pour Transaction ID: ".$payment['financial_transaction_id']." et pour Order ID: ".$payment['order_id']." pour un montant de ".$payment['amount']."XAF a réussi. Merci d'utiliser sportaabe":
                "Your payment with Transaction ID: ".$payment['financial_transaction_id']." and Order ID: ".$payment['order_id']." for an amount of ".$payment['amount']."XAF is successful. Thanks for using sportaabe";
                
                $this->commonService->sendAwsMessage($payment['phone_number'], $message);

                if(!empty($payment['offer_to'])){
                  $offer_to = explode(',', $payment["offer_to"]);
                  if(!empty($offer_to[1])){
                    if(!is_null($user))
                    {
                      $message = $user->getMetaValueByCode("language","fr")=="fr"? $payment["client_name"]." vous offre une réservation pour l'activité : ".$payment['activity_name']." chez : ".$payment['company_name']." qui se déroulera le ".$payment['activity_date_time'].". Merci d'utiliser sportaabe":
                      $payment["client_name"]." is offering you a reservation for : ".$payment['activity_name']." at : ".$payment['company_name']." which will take place on ".$payment['activity_date_time'].". Thanks for using sportaabe";
                      
                    }
                    $this->commonService->sendAwsMessage($payment['phone_number'], $message);
                  }
                }
              }
            }            
            return $this->result($payment);
          }
        }
      }
     }
       return $this->result($payment);
    }
    throw new \Exception("Erreur lors du traitement.".$methodId);
  }
  public function checkPayReservation()
  {
    $this->writeInlogFile('======  CRON START =====', 'INFO');

    //reset bonus points on 01/01 00:00 _ we will create a separate endpoint+ cronjob for this later
    if (date('m-d H:i') === '01-01 00:00') {
          $users= $this->userRepo->findAll();
          foreach ($users as $user) {
            $user->addMeta(
              (new UserMeta())
                ->setCode('fidelity_balance')
                ->setValue('10000'));
                $this->persist($user);
          }

        }
    $payments = $this->getPaymentsByStatus("INITIATED", "NOTIFIED");
   
    $rappels = [];
    $expireds = [];
    if (count($payments) > 0) {
      foreach ($payments as $p) {
        $dt1 = $p['created_at'];

        $dt = $p['activity_date_time'];
        $dt = date_create_from_format('d/m/Y, H:i', $dt);
        $user = $this->userRepo->findOneBy(["id"=>$p["user_id"]]);

        $datetime = new DateTime();
        

        if (!empty($dt)) {

          $endDtExpired = clone $dt;

          $endDtRappel = clone $dt;

          //reminder delay setting
          $reminder_delay=$this->settingRepo->findOneBy(["code"=>"reminder_delay"])->getValue();
          
          $endDtRappel->modify('-'.($reminder_delay?$reminder_delay:'1').' hours'); 

          if ($dt1 <= $dt) {
            if ($datetime >= $endDtExpired) {
              if ("INITIATED" == $p['status'] || "NOTIFIED" == $p['status']) {
                $p['status'] = "EXPIRED";
                $sql = "UPDATE payments set status = :status WHERE order_id = :order_id";
                $data = [
                  'status' => $p['status'],
                  'order_id' => $p['order_id'],
                ];
                $this->updateSql($sql, $data);
                $expireds[] = $p;
              }
            } else if ($datetime >= $endDtRappel) {
              
              if ("INITIATED" == $p['status']) {
                $p['status'] = "NOTIFIED";
                $sql = "UPDATE payments set status = :status WHERE order_id = :order_id";
                $data = [
                  'status' => $p['status'],
                  'order_id' => $p['order_id'],
                ];
                $this->updateSql($sql, $data);
                $rappels[] = $p;
              }
            }
          }
        }
      }
      if (count($rappels) > 0) {
        foreach ($rappels as $rappel) {
          if($rappel['mail_address']){
            $template = $user->getMetaValueByCode("language","fr")=="fr"?"pay-remind":"pay-remind-en";
            $this->commonService->sendMail3($rappel['mail_address'], "[sportaabe ] Rappel de paiement", $rappel, $template, false);
          }
          if($rappel['phone_number']){
            $s = $user->getMetaValueByCode("language","fr")=="fr"?"RAPPEL : Vous avez un paiement sportaabe en attente pour ". $this->commonService->cutActivityName($rappel['activity_name']) .". Lien: " . $rappel['pay_url']:
            "REMINDER : You have a pending sportaabe payment for ". $this->commonService->cutActivityName($rappel['activity_name']) .". Link: " . $rappel['pay_url'];
            $this->commonService->sendAwsMessage($rappel['phone_number'], $s);
          }
        }
      }
      if (count($expireds) > 0) {
        
        foreach ($expireds as $expired) {
          if($expired['mail_address']){
            $template = $user->getMetaValueByCode("language", "fr")=="fr"?"pay-expired":"pay-expired-en";
            $this->commonService->sendMail3($expired['mail_address'], "[sportaabe ] Lien de paiement expiré", $expired, $template, false);
          }
          if($expired['phone_number']){
            $s = $user->getMetaValueByCode("language", "fr")=="fr"?"Votre lien de paiement sportaabe pour ". $this->commonService->cutActivityName($expired['activity_name']) ." a expiré. Veuillez réessayer via l'application sportaabe.":
            "Your sportaabe payment link for ". $this->commonService->cutActivityName($expired['activity_name']) ." has expired. Please retry via sportaabe.";
            $this->commonService->sendAwsMessage($expired['phone_number'], $s);
          }

          //let's restore user bonus spent when initiating payment
          if($expired['spentFidelityAmount']){
            $this->userService->updateFidelityBalance($this->userRepo->findOneBy(["id" => $expired['user_id']]),$expired['spentFidelityAmount'],"restore");
          } 
        }
      }
    }
    $this->writeInlogFile('======  CRON EXIT ===== STATS : Rappels count ' . \strval(count($rappels)) . ', Expired count ' . \strval(count($expireds)), 'INFO');
    return $this->result(["rappels" => $rappels, "expireds" => $expireds]);
  }
  public function isOrange($m)
  {
    return "orange" === $m;
  }
}


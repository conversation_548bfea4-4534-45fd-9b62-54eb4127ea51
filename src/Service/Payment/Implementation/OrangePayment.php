<?php
namespace App\Service\Payment\Implementation;
/**
 * OrangePayment class.
 */

 class OrangePayment
 {
 
   /**
    * Send Orange Money payment request
    */
   public static function sendPaymentResquest($order,$gateway)
   {
     $gateway->writeInlogFile('Initiating payment request ...', 'INFO');
     $gateway->writeInlogFile('Setting payment request header ...', 'INFO');
     $header = OrangePayment::setRequestHeader('payment_or_status_request',$gateway);
     if (!$header) {
       $gateway->writeInlogFile('Setting payment request header failed', 'ERROR');
       return false;
     }
 
     $gateway->writeInlogFile('Setting payment request body ...', 'INFO');
     $data_string = OrangePayment::setRequestBody('payment_request', $gateway, $order);
     if (!$data_string) {
       $gateway->writeInlogFile('Setting payment request body failed', 'ERROR');
       return false;
     }
 
     $gateway->writeInlogFile('Sending payment request ...', 'INFO');
     $results = OrangePayment::sendRequest($gateway, $header, $data_string, $gateway->paymentUrl);
     if (!$results) {
       return false;
     }
     $gateway->writeInlogFile('contenu du resultat =====> '.$results['message'], 'INFO');
     switch ($results['message']) {
       case 'OK':
         $gateway->writeInlogFile('Payment request successfull !!!', 'INFO');
         $order["status"] = "PENDING";
         $order["pay_token"] = $results['pay_token'];
         $order["notif_token"] = $results['notif_token'];
         $order["payment_url"] = $results['payment_url'];
         return $order;
 
       default:
         return false;
     }
   }
 
   public static function checkPaymentStatus($order, $gateway)
   {
     $gateway->writeInlogFile('Checking payment status ...', 'INFO');
     $gateway->writeInlogFile('Setting request status header ...', 'INFO');
 
     $header = OrangePayment::setRequestHeader('payment_or_status_request', $gateway);
     $gateway->writeInlogFile('Setting request status body ...', 'INFO');
     $data_string = OrangePayment::setRequestBody('status_request', $gateway, null, $order);
     if (!$data_string) {
       $gateway->writeInlogFile('Setting request status body failed', 'ERROR');
       return false;
     }
 
     $gateway->writeInlogFile('Sending status request ...', 'INFO');
     $results = OrangePayment::sendRequest($gateway, $header, $data_string, $gateway->statusUrl);
     if (!$results) {
       return false;
     }
     switch ($results['status']) {
       case 'SUCCESS':
         $gateway->writeInlogFile('The payment status is : SUCCESS', 'INFO');
         $order["status"] = "SUCCESSFUL";
         $order["txnid"] = $results['txnid'];
         return $order;
 
       default:
         $gateway->writeInlogFile('The payment did not succeed', 'ERROR');
         $order["status"] = $results['status'];
         $order["txnid"] = NULL;
         return $order;
     }
   }
 
   /**
    * Generates access token
    */
   public static function generateAccessToken($gateway)
   {
     $gateway->writeInlogFile('Initiating Access Token creation request ...', 'INFO');
     $header = OrangePayment::setRequestHeader('access_token_request', $gateway);
     if (!$header) {
       $gateway->writeInlogFile('Setting Access Token request header failed', 'ERROR');
       return false;
     }
 
     $gateway->writeInlogFile('Setting Access Token request body ...', 'INFO');
     $data_string = OrangePayment::setRequestBody('access_token_request', $gateway);
     if (!$data_string) {
       $gateway->writeInlogFile('Setting Access Token request body failed', 'ERROR');
       return false;
     }
 
     $gateway->writeInlogFile('Sending Access Token request ...', 'INFO');
     $results = OrangePayment::sendRequest($gateway, $header, $data_string, $gateway->tokenUrl);
     if (!$results) {
       return false;
     }
 
     if ($results['access_token']) {
       $gateway->writeInlogFile('Token creation successfull !!! New Token = ' . $results['access_token'], 'INFO');
       $gateway->writeInlogFile('Access token value updated !!!', 'INFO');
       return $results['access_token'];
     } else {
       return false;
     }
   }
 
   /**
    * Sets the request header
    */
   public static function setRequestHeader($operation, $gateway)
   {
 
     $header = array();
 
     switch ($operation) {
       case 'payment_or_status_request':
         $header[] = "Content-Type:application/json";
         $header[] = "Accept:application/json";
         $header[] = "Authorization:Bearer " . $gateway->accessToken;
         return $header;
 
       case 'access_token_request':
         $header[] = "Content-Type: application/x-www-form-urlencoded";
         $header[] = "Authorization:" . $gateway->authHeader;
         return $header;
 
       default:
         return false;
     }
   }
 
   /**
    * Sets request body
    */
   public static function setRequestBody($operation, $gateway, $order = null, $payment_details = null)
   {
 
     switch ($operation) {
       case 'payment_request':
         $data = array();
         $data['merchant_key'] = $gateway->merchantKey;
         $data['currency'] = $gateway->currencyOrange;
         $data['order_id'] = $order["ref_order"];
         $data['amount'] = (float) $order["amount"];
         $data['return_url'] = $gateway->baseLink . '/public/payments/' . $order["ref_order"] . '&key=r';
         $data['cancel_url'] = $gateway->baseLink . '/public/payments/' . $order["ref_order"] . '&key=c';
         $data['notif_url'] = $gateway->baseLink . '/public/payments/' . $order["ref_order"] . '&key=n';
         $data['lang'] = $gateway->language;
         $data['reference'] = $gateway->merchantName;
         return  \json_encode($data);
 
       case 'status_request':
         $data = array();
         $data['order_id'] = $payment_details["ref_order"];
         $data['amount'] = $payment_details["amount"];
         $data['pay_token'] = $payment_details["pay_token"];
         return  \json_encode($data);
 
       case 'access_token_request':
         $data = "grant_type=client_credentials";
         return $data;
 
       default:
         return false;
     }
   }
 
   /**
    * Send cURL request
    */
   public static function sendRequest($gateway, $header, $data_string, $url)
   {
 
     $gateway->writeInlogFile('Request header ' .  \json_encode($header), 'INFO');
     $gateway->writeInlogFile('Request body ' .  \json_encode($data_string), 'INFO');
     $gateway->writeInlogFile('Request URL ' . $url, 'INFO');
 
     $ch = \curl_init();
     \curl_setopt($ch, CURLOPT_URL, $url);
     \curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
     \curl_setopt($ch, CURLOPT_POST, true);
     \curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
     \curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
     \curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
 
     $result_response = \curl_exec($ch);
 
     $results = array();
 
     if (curl_errno($ch) > 0) {
       $gateway->writeInlogFile('There was a cURL error: ' . \curl_error($ch), 'ERROR');
       \curl_close($ch);
       return false;
     } else {
       $gateway->writeInlogFile('Request response  ' . $result_response, 'INFO');
       $results =  \json_decode($result_response, true);
       \curl_close($ch);
 
       if ($results) {
         return $results;
       } else {
         $gateway->writeInlogFile('Decoding JSON failed with the following message: ' . json_last_error_msg(), 'ERROR');
         return false;
       }
     }
   }
 }
 
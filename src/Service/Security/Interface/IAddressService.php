<?php

namespace App\Service\Security\Interface;

use App\Entity\Security\Address;
use App\Entity\Security\User;
use Symfony\Component\HttpFoundation\Request;

interface IAddressService
{

  /**
   * Does something interesting
   *
   * @param Request $request  Where something interesting takes place
   * 
   * @throws Some_Exception_Class If something interesting cannot happen
   * <AUTHOR> <<EMAIL>>
   * @return Address
   */
  public function create(Request $request, array $payload): Address;


  /**
   * Does something interesting
   *
   * @param string  $uuid  Where something interesting takes place
   * @param bool  $throwIfNotFound  Where something interesting takes place
   * 
   * @throws Some_Exception_Class If something interesting cannot happen
   * <AUTHOR> <<EMAIL>>
   * @return Address|null
   */
  public function getByUuid(string $uuid, bool $throwIfNotFound = true): Address|null;
}

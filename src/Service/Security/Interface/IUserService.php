<?php

namespace App\Service\Security\Interface;

use App\Dto\GenericResponse;
use App\Entity\Security\User;
use Symfony\Component\HttpFoundation\Request;

interface IUserService
{

  /**
   * Does something interesting
   *
   * @param Request $request  Where something interesting takes place
   * 
   * @throws Some_Exception_Class If something interesting cannot happen
   * <AUTHOR> <<EMAIL>>
   * @return GenericResponse
   */
  public function authenticate(Request $request): GenericResponse;

  /**
   * Does something interesting
   *
   * @param string  $identifier  Where something interesting takes place
   * 
   * @throws Some_Exception_Class If something interesting cannot happen
   * <AUTHOR> <<EMAIL>>
   * @return User
   */
  public function getUserByIdentifier(string $identifier): User|null;

  /**
   * Does something interesting
   *
   * @param string $identifier  Where something interesting takes place
   * 
   * @throws Some_Exception_Class If something interesting cannot happen
   * <AUTHOR> <<EMAIL>>
   * @return User
   */
  public function resetUserToken(string $identifier): void;

  /**
   * Does something interesting
   *
   * @param string $identifier  Where something interesting takes place
   * 
   * @throws Some_Exception_Class If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return User
   */
  public function getCurrentUser(): User|null;

  /**
   * Does something interesting
   *
   * @param string $uuid
   * @param Request $request  Where something interesting takes place
   * 
   * @throws Some_Exception_Class If something interesting cannot happen
   * <AUTHOR> <PERSON>ONGNUY <<EMAIL>>
   * @return GenericResponse
   */
  public function update(string $uuid, Request $request): GenericResponse;

  /**
   * Does something interesting
   *
   * @param Request $request  Where something interesting takes place
   * 
   * @throws Some_Exception_Class If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return GenericResponse
   */
  public function findOne(string $uuid, Request $request): GenericResponse;

    /**
   * Does something interesting
   *
   * 
   * @throws Some_Exception_Class If something interesting cannot happen
   * <AUTHOR> RAYE <<EMAIL>>
   * @return GenericResponse
   */
  public function getUsers(Request $request): GenericResponse;

  /**
   * Does something interesting
   *
   * @param Request $request  Where something interesting takes place
   * 
   * @throws Some_Exception_Class If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return GenericResponse
   */
  public function info(): GenericResponse;
}

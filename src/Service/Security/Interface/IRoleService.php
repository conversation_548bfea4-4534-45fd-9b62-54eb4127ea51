<?php

namespace App\Service\Security\Interface;

use App\Dto\GenericResponse;
use App\Entity\Security\Role;
use Symfony\Component\HttpFoundation\Request;

interface IRoleService
{
  /**
   * Does something interesting
   *
   * @param Request $request  Where something interesting takes place
   * 
   * @throws Some_Exception_Class If something interesting cannot happen
   * <AUTHOR> <<EMAIL>>
   * @return GenericResponse
   */
  public function findAll(Request $request): GenericResponse;

  /**
   * Does something interesting
   *
   * @param Request $request  Where something interesting takes place
   * 
   * @throws Some_Exception_Class If something interesting cannot happen
   * <AUTHOR> <<EMAIL>>
   * @return GenericResponse
   */
  public function findOne(string $uuid, Request $request): GenericResponse;

  /**
   * Does something interesting
   *
   * @param Request $request  Where something interesting takes place
   * 
   * @throws Some_Exception_Class If something interesting cannot happen
   * <AUTHOR> <<EMAIL>>
   * @return GenericResponse
   */
  public function create(Request $request): GenericResponse;

  /**
   * Does something interesting
   * @param string $uuid
   * @param Request $request  Where something interesting takes place
   * 
   * @throws Some_Exception_Class If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return GenericResponse
   */
  public function replace(string $uuid, Request $request): GenericResponse;

  /**
   * Does something interesting
   *
   * @param string $uuid
   * @param Request $request  Where something interesting takes place
   * 
   * @throws Some_Exception_Class If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return GenericResponse
   */
  public function update(string $uuid, Request $request): GenericResponse;

  /**
   * Does something interesting
   *
   * @param string $uuid
   * @param Request $request  Where something interesting takes place
   * 
   * @throws Some_Exception_Class If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return GenericResponse
   */
  public function remove(string $uuid, Request $request): GenericResponse;

  /**
   * Does something interesting
   *
   * @param string  $uuid  Where something interesting takes place
   * @param bool  $throwIfNotFound  Where something interesting takes place
   * 
   * @throws Some_Exception_Class If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return Role
   */
  public function getByUuid(string $uuid, bool $throwIfNotFound = true): Role|null;
}

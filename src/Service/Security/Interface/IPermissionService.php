<?php

namespace App\Service\Security\Interface;

use App\Entity\Security\Permission;

interface IPermissionService
{

  /**
   * Does something interesting
   *
   * @param string  $uuid  Where something interesting takes place
   * @param bool  $throwIfNotFound  Where something interesting takes place
   * 
   * @throws Some_Exception_Class If something interesting cannot happen
   * <AUTHOR> <<EMAIL>>
   * @return Permission|null
   */
  public function getByUuid(string $uuid, bool $throwIfNotFound = true): Permission|null;
}

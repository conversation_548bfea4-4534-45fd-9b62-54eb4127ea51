<?php

namespace App\Service\Security\Implementation;

use App\Entity\Security\Permission;
use App\Mapper\Security\Interface\IPermissionMapper;
use App\Repository\Security\PermissionRepository;
use App\Service\Traits\BaseServiceTrait;
use App\Service\Traits\LogServiceTrait;
use App\Service\Security\Interface\IPermissionService;

class PermissionService implements IPermissionService
{
  use BaseServiceTrait, LogServiceTrait;

  private IPermissionMapper $mapper;

  private PermissionRepository $repository;

  public function __construct(IPermissionMapper $addressMapper, PermissionRepository $repository)
  {
    $this->mapper = $addressMapper;
    $this->repository = $repository;
  }

  public function getByUuid(string $uuid, bool $throwIfNotFound = true): Permission|null
  {
    return $this->repository->getByUuid($uuid, $throwIfNotFound);
  }
}

<?php

namespace App\Service\Security\Implementation;

use App\Entity\Security\Address;
use App\Mapper\Security\Interface\IAddressMapper;
use App\Repository\Security\AddressRepository;
use App\Service\Traits\BaseServiceTrait;
use App\Service\Traits\LogServiceTrait;
use App\Service\Security\Interface\IAddressService;
use Symfony\Component\HttpFoundation\Request;

class AddressService implements IAddressService
{
  use BaseServiceTrait, LogServiceTrait;

  private IAddressMapper $mapper;

  private AddressRepository $repository;

  public function __construct(IAddressMapper $addressMapper, AddressRepository $repository)
  {
    $this->mapper = $addressMapper;
    $this->repository = $repository;
  }

  public function create(Request $request, array $payload): Address
  {
    $address = $this->mapper->fromCreateRequest($request, $payload);
    $this->persist($address);
    return $address;
  }

  public function getByUuid(string $uuid, bool $throwIfNotFound = true): Address|null
  {
    return $this->repository->getByUuid($uuid, $throwIfNotFound);
  }
}

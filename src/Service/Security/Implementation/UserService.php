<?php

namespace App\Service\Security\Implementation;

use Exception;
use App\Enum\LoginTypes;
use App\Dto\GenericResponse;
use Psr\Log\LoggerInterface;
use App\Entity\Security\User;
use App\Enum\UserStatusTypes;
use App\Constant\DefaultValues;
use App\Mapper\BaseMapperTrait;
use App\Constant\PatchOperations;
use App\Entity\Security\UserMeta;
use App\Entity\Company\CompanyMeta;
use App\Service\Traits\LogServiceTrait;
use App\Dto\Security\In\ProfileUpdateIn;
use App\Form\Security\ProfileUpdateType;
use App\Service\Traits\BaseServiceTrait;
use App\Constant\GenericResponseStatuses;
use App\Repository\Security\UserRepository;
use Symfony\Bundle\SecurityBundle\Security;
use App\Repository\Shared\SettingRepository;
use App\Exception\RequestValidationException;
use Symfony\Component\HttpFoundation\Request;
use App\Mapper\Security\Interface\IUserMapper;
use Symfony\Component\HttpFoundation\Response;
use App\Exception\UnprocessableRequestException;
use App\Service\Security\Interface\IUserService;
use App\Service\Security\Interface\IAddressService;
use App\Service\Shared\Implementation\CommonService;
use App\Service\Company\Implementation\CompanyService;
use App\Repository\Security\AuthentificationCodeRepository;

class UserService implements IUserService
{
  use BaseServiceTrait, LogServiceTrait, BaseMapperTrait;

  private IUserMapper $mapper;

  private UserRepository $repository;

  private IAddressService $addressService;

  private Security $security;

  private CommonService $commonService;
  private UserRepository $userRepo;
  private SettingRepository $settingRepository;
  private AuthentificationCodeRepository $otpRepository;
  private CompanyService $companyService;

  public function __construct(
    IUserMapper $userMapper,
    UserRepository $repository,
    Security $security,
    IAddressService $addressService,
    CommonService $commonService,
    UserRepository $userRepo,
    SettingRepository $settingRepository,
    AuthentificationCodeRepository $otpRepository,
    CompanyService $companyService
  ) {
    $this->mapper = $userMapper;
    $this->repository = $repository;
    $this->security = $security;
    $this->addressService = $addressService;
    $this->commonService = $commonService;
    $this->userRepo = $userRepo;
    $this->settingRepository = $settingRepository;
    $this->otpRepository = $otpRepository;
    $this->companyService = $companyService;
  }

  public function authenticate(Request $request): GenericResponse
  {
    $user = $this->mapper->fromAuthenticateRequest($request);

    $this->persist($user);

    if (empty($user->getAuthentificationCode())) {
      throw new RequestValidationException("Authentication code cannot be empty !!!");
    }
  //send auth code unless it's a demo account
  if($user->getEmail()!=='<EMAIL>'){
    if($this->settingRepository->getByCode("SEND_REAL_OTP_SMS")->getValue()==='1')
    {   
        $template = $user->getMetaValueByCode("language","fr") == "fr" ? "send-otp" : "sent-otp-en";
        $message = $user->getMetaValueByCode("language","fr") == "fr" ? "Utilisez ce code pour vous authentifier dans sportaabe : " : "Use this code to log in sportaabe : ";
        match ($user->getAuthentificationCode()->getType()) {
        LoginTypes::EMAIL->name => $this->commonService->sendMail($user->getEmail(), $user->getMetaValueByCode("language","fr") == "en" ? "One-Time Password" :'Code d\'authentification', $user->getAuthentificationCode()->getCode(), $template),
        LoginTypes::PHONE->name => $this->commonService->sendAwsMessage($user->getPhoneNumber(), $message . $user->getAuthentificationCode()->getCode()),
        default => throw new RequestValidationException("Login type is not valid !!!") 
        };
    }  
  }

    return $this->result($this->mapper->toTinyOut($user));
  }

  public function registerProfile(Request $request)
  {
    $profileUpdateIn = new ProfileUpdateIn();
    $form = $this->formFactory->create(ProfileUpdateType::class, $profileUpdateIn);
    $form->submit($request->request->all(), true);

    if (false === $form->isValid()) {
      $this->logger->error('Form not valid: ' . \json_encode($form->getErrors()));
      throw new RequestValidationException($form);
    }

    /**  @var \App\Entity\Security\User */
    $user = $this->security->getUser();

    $user->setFirstName($profileUpdateIn->getFirstName());
    $user->setLastName($profileUpdateIn->getLastName());
    $user->setDateOfBirth($profileUpdateIn->getBirthDate());

    match ($profileUpdateIn->getLoginType()) {
      LoginTypes::EMAIL->name => $user->setPhoneNumber($profileUpdateIn->getPhoneNumber()),
      LoginTypes::PHONE->name => $user->setEmail($profileUpdateIn->getEmail()),
      default => ""
    };
    $user->addMeta((new UserMeta())
    ->setCode("language")
    ->setValue("fr")
    );

    $this->persist($user);
    $this->logger->info('User created: {username}', [
      'username' => $user->getDisplayName(),
  ]);


    return $this->result($this->toUserInfo($user));
  }

  public function updateProfile(Request $request)
  {
    $profileUpdateIn = new ProfileUpdateIn();
    $form = $this->formFactory->create(ProfileUpdateType::class, $profileUpdateIn);
    $body = $request->request->all();
    $form->submit($body, true);

    if (false === $form->isValid()) {
      $this->logger->error('Form not valid: ' . \json_encode($form->getErrors()));
      throw new RequestValidationException($form);
    }

    /**  @var \App\Entity\Security\User */
    $user = $this->security->getUser();

    $user->setFirstName($profileUpdateIn->getFirstName());
    $user->setLastName($profileUpdateIn->getLastName());
    $user->setDateOfBirth($profileUpdateIn->getBirthDate());
    if (!empty(intval($body['weight']))) {
      $user->setWeight($body['weight']);
    }
    if (!empty(intval($body['height']))) {
      $user->setHeight($body['height']);
    }

    $user->setGender($body['gender']);

    $metas = ["address", "city", "country", "avatar"];
    foreach ($metas as $key) {
      if (!empty($body[$key])) {
        $user->addMeta(
          (new UserMeta())
            ->setCode($key)
            ->setValue($body[$key])
        );
      }
    }

    match ($profileUpdateIn->getLoginType()) {
      LoginTypes::EMAIL->name => $user->setPhoneNumber($profileUpdateIn->getPhoneNumber()),
      LoginTypes::PHONE->name => $user->setEmail($profileUpdateIn->getEmail()),
      default => ""
    };

    $this->persist($user);

    $this->logger->info('User updated: {username}', [
      'username' => $user->getDisplayName(),
    ]);

    return $this->result($this->toUserInfo($user));
  }

  public function setLanguage(Request $request)
  {
    $body = $request->request->all();

    /**  @var \App\Entity\Security\User */
    $user = $this->security->getUser();

    if (!empty($body["language"])) {
      $user->addMeta(
        (new UserMeta())
          ->setCode("language")
          ->setValue($body["language"])
      );
    }
    $this->persist($user);

    return $this->result($this->toUserInfo($user));
  }
  public function updateProfileAdmin(Request $request)
  {
    $myUser = $this->security->getUser();
    $profileUpdateIn = new ProfileUpdateIn();
    $form = $this->formFactory->create(ProfileUpdateType::class, $profileUpdateIn);
    $body = $request->request->all();
    $body['birthDate'] = $body['birthDate'] ? \DateTime::createFromFormat('d-m-Y', $body['birthDate'])->format('Y-m-d') : null;
    $form->submit($body, true);

    if (false === $form->isValid()) {
      $this->logger->error('Form not valid: ' . \json_encode($form->getErrors()));
      throw new RequestValidationException($form);
    }

    $user = $this->userRepo->getByUuid($body['uuid']);

    $user->setFirstName($profileUpdateIn->getFirstName());
    $user->setLastName($profileUpdateIn->getLastName());
    $user->setDateOfBirth($profileUpdateIn->getBirthDate());
    $user->setActive($body['active']);
    if (!empty(intval($body['weight']))) {
      $user->setWeight($body['weight']);
    }
    if (!empty(intval($body['height']))) {
      $user->setHeight($body['height']);
    }

    $user->setGender($body['gender']);

    $metas = ["address", "city", "country", "avatar"];
    foreach ($metas as $key) {
      if (!empty($body[$key])) {
        $user->addMeta(
          (new UserMeta())
            ->setCode($key)
            ->setValue($body[$key])
        );
      }
    }
    if(isset($body['admin'])){
     // dd($body['admin']);
      $user->addMeta(
        (new UserMeta())
          ->setCode('is_admin')
          ->setValue(intval($body['admin']))
      );
      if($body['admin']=="0"){
        $user->addMeta(
          (new UserMeta())
            ->setCode('is_super_admin')
            ->setValue(intval($body['admin']))
        );
      }

    }
    if(isset($body['superadmin']) && $myUser?->getMetaValueByCode('is_super_admin',"0")=="1"){
      $user->addMeta(
        (new UserMeta())
          ->setCode('is_super_admin')
          ->setValue($body['superadmin'])
      );
    }


    match ($profileUpdateIn->getLoginType()) {
      LoginTypes::EMAIL->name => $user->setPhoneNumber($profileUpdateIn->getPhoneNumber()),
      LoginTypes::PHONE->name => $user->setEmail($profileUpdateIn->getEmail()),
      default => ""
    };

    $this->persist($user);

    $this->logger->info('User updated by admin: {username}', [
      'username' => $user->getDisplayName(),
    ]);

    return $this->result($this->toUserAdminInfo($user));
  }

  public function removalRequest($option)
  {
    /**  @var \App\Entity\Security\User */
    $user = $this->security->getUser();
    $userStatus = $user->getStatus();
    $template="";
    $subject = "";

    if($option=="1"){
      $user->setDescription($userStatus);
      $user->setStatus(UserStatusTypes::PENDING_REMOVAL->value);
      $template = $user->getMetaValueByCode("language","fr") === "fr"?"requestremoval":"request-removal-en";
      $subject = "Demande de suppression de compte utilisateur";
    }else{
      $user->setStatus($user->getDescription());
      $user->setDescription("");
      
      $template = $user->getMetaValueByCode("language","fr") === "fr"?"cancelremoval":"cancelremoval-en";

      $subject = "Annulation de la suppression de compte utilisateur";


    }
    $this->persist($user);

    $admins = array("<EMAIL>", "<EMAIL>");

    $this->commonService->sendAdmin2($admins, $user, $subject, $template);

    return $this->result($this->toUserInfo($user));
  }
  public function getAllAuthCodes()
  {
    $codes = $this->otpRepository->findAll();
    $output=[];
    
    foreach ($codes as $code) {
      $output[]=[
        'user' => $code->getUser()->getPhoneNumber()?$code->getUser()->getPhoneNumber():$code->getUser()->getEmail(),
        'code' => $code->getCode(),
        'generated_code' => $code->getGeneratedCode(),
        'trials_code' => $code->getTrialsCode()
        
      ];
    }
    return $this->result($output);
    
  }

  public function deleteUsersAdmin(string $uuid)
  {

    $user = $this->userRepo->getByUuid($uuid);
    
    if (!is_null($user->getCompany()))
    {
      $result = $this->companyService->deleteCompanyAdmin($user->getCompany()->getUuid());  
    }
    $data = [
      'id' => $user->getUuid(),
      'lastname' => $user->getLastName(),
      'company' => isset($result)?$result->getData():'NOT A PRO'

    ];
  
    $this->delete($user);

    $this->logger->info("User deleted by admin: " . $user->getLastName());

    return $this->result($data);
  }


  public function getUserByIdentifier(string $identifier): User|null
  {
    return $this->repository->loadUserByIdentifier($identifier);
  }

  public function resetUserToken(string $identifier): void
  {
    $user = $this->getUserByIdentifier($identifier);
    if (!empty($user)) {
      $this->delete($user->getAuthentificationCode());
      $user->setAuthentificationCode(null);
      $this->persist($user);
    }
  }

  public function getCurrentUser(): User|null
  {
    return $this->security->getUser();
  }

  private function addAddress(string $uuid, Request $request, array $payload): mixed
  {
    $address = $this->addressService->create($request, $payload);
    $user = $this->repository->getByUuid($uuid);
    /// $user =  $this->addressService->linkWithUser($address, $user);
    $outputFormat = $request->query->get("output", DefaultValues::DEFAULT_REPLACE_OUTPUT_FORMAT);
    $output = $this->mapper->toOut($outputFormat, $user);
    return $output;
  }

  public function update(string $uuid, Request $request): GenericResponse
  {
    $results = [];
    $operations = $this->mapper->fromUpdateRequest($uuid, $request);
    foreach ($operations as $operation) {
      $results[] = match ($operation->getType()) {
        PatchOperations::USERS_ADD_ADDRESS => $this->addAddress($uuid, $request, $operation->getPayload()),
        default => throw new RequestValidationException("Patch operation is no valid : " . $operation->getType()),
      };
    }
    return $this->result($results);
  }
  public function getUsers(Request $request): GenericResponse
  {
    if($request->query->get("admin")=="true"){
      $users = $this->repository->findAdminUsers();
      //dd($users);
    }else{
      $users = $this->repository->findAll();
    }
    $output=[];
    if ($request->query->get("last5")==="true") {
      
      $users = array_slice($users,-5, 5);
      $users = array_reverse($users);
    }
    
    foreach ($users as $user) {
      $output[]=$this->toUserAdminInfo($user);
    }
    return $this->result($output);
  }

  public function findOne(string $uuid, Request $request): GenericResponse
  {
    $outputFormat = $request->query->get("output", DefaultValues::DEFAULT_FIND_ONE_OUTPUT_FORMAT);
    $user = $this->repository->getByUuid($uuid);

    $output = $this->mapper->toOut($outputFormat, $user);

    return $this->result($output);
  }

  public function findOneById(string $id): GenericResponse
  {
    $user = $this->repository->find($id);
    if (is_null($user)) {
      return $this->result(['message' => 'User not found'], 404, Response::HTTP_NOT_FOUND);

    }
    return $this->result($this->toUserInfo($user));
  }

  public function info(): GenericResponse
  {
    $user = $this->getCurrentUser();
/*     if(is_null($user)){
      return $this->result(null, GenericResponseStatuses::UNAUTHORIZED, 401, []);
    } */
    return $this->result($this->toUserInfo($user));
  }

  public function isSuperAdmin(): GenericResponse
  {
    $user = $this->getCurrentUser();
    return $this->result($user->getMetaValueByCode('is_super_admin',"0") == "1");
  }
  
    //old users don't have bonuses nor category so this function is here to fix it
    public function resetBonus()
    {
      /**  @var \App\Entity\Security\User */
      $user = $this->security->getUser();
      if ($user->getMetaValueByCode('is_super_admin') == "1") {
        $users = $this->userRepo->findAll();
        foreach ($users as $user) {
          $user->addMeta(
            (new UserMeta())
              ->setCode('fidelity_balance')
              ->setValue('10000')
          )->addMeta(
            (new UserMeta())
              ->setCode('category')
              ->setValue('1')
          );
          $this->persist($user);
        }
        $this->logger->info("All users fidelity balance set to 10000 and category set to 1: ");
          
        return $this->result("Users fidelity balance successfull reset to 10000 and category to 1");
      }
      return $this->result("You are not allowed to do this! please contact an Admin !");
    }

    public function updateFidelityBalance(User $user, $spentFidelityAmount, $operation)
    {
      if ($operation == "deduction") {
        $balance = floatval($user->getMetaValueByCode('fidelity_balance')) - floatval($spentFidelityAmount);
      } else {
        $balance = floatval($user->getMetaValueByCode('fidelity_balance')) + floatval($spentFidelityAmount);
      }
      $user->addMeta(
        (new UserMeta())
          ->setCode('fidelity_balance')
          ->setValue($balance)
      );
      $this->persist($user);
    }
}

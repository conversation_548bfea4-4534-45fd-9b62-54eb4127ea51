<?php

namespace App\Service\Tournament\Interface;

use App\Dto\GenericResponse;
use Symfony\Component\HttpFoundation\Request;

interface IEncounterService
{
    /**
     * Create a new encounter (admin only)
     */
    public function createEncounter(Request $request): GenericResponse;

    /**
     * Get all encounters
     */
    public function getAllEncounters(Request $request): GenericResponse;

    /**
     * Get encounter by ID
     */
    public function getEncounter(string $id): GenericResponse;

    /**
     * Update encounter (admin only)
     */
    public function updateEncounter(Request $request, string $id): GenericResponse;

    /**
     * Delete encounter (admin only)
     */
    public function deleteEncounter(string $id): GenericResponse;

    /**
     * Add event to encounter (admin only) - goals, cards, etc.
     */
    public function addEventToEncounter(Request $request): GenericResponse;

    /**
     * Remove event from encounter (admin only)
     */
    public function removeEventFromEncounter(string $eventId): GenericResponse;

    /**
     * Get encounters by tournament
     */
    public function getEncountersByTournament(string $tournamentId): GenericResponse;

    /**
     * Get encounters by team
     */
    public function getEncountersByTeam(string $teamId): GenericResponse;

    /**
     * Get upcoming encounters
     */
    public function getUpcomingEncounters(): GenericResponse;

    /**
     * Get completed encounters
     */
    public function getCompletedEncounters(): GenericResponse;

    /**
     * Update encounter score (admin only)
     */
    public function updateEncounterScore(Request $request, string $id): GenericResponse;
}

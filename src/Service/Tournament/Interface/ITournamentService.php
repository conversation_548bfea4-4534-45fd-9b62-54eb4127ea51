<?php

namespace App\Service\Tournament\Interface;

use App\Dto\GenericResponse;
use Symfony\Component\HttpFoundation\Request;

interface ITournamentService
{
    /**
     * Create a new tournament (admin only)
     */
    public function createTournament(Request $request): GenericResponse;

    /**
     * Get all tournaments
     */
    public function getAllTournaments(Request $request): GenericResponse;

    /**
     * Get tournament by ID
     */
    public function getTournament(string $id): GenericResponse;

    /**
     * Update tournament (admin only)
     */
    public function updateTournament(Request $request, string $id): GenericResponse;

    /**
     * Delete tournament (admin only)
     */
    public function deleteTournament(string $id): GenericResponse;

    /**
     * Add company to tournament (admin only)
     */
    public function addCompanyToTournament(Request $request): GenericResponse;

    /**
     * Remove company from tournament (admin only)
     */
    public function removeCompanyFromTournament(Request $request): GenericResponse;

    /**
     * Get tournament statistics
     */
    public function getTournamentStatistics(string $id): GenericResponse;

    /**
     * Get tournaments by company
     */
    public function getTournamentsByCompany(int $companyId): GenericResponse;

    /**
     * Get tournament standings/rankings
     */
    public function getTournamentStandings(string $id): GenericResponse;
}

<?php

namespace App\Service\Tournament\Implementation;

use App\Dto\GenericResponse;
use App\Dto\ResponseDtoBuilder;
use App\Entity\Tournament\Tournament;
use App\Repository\Tournament\TournamentRepository;
use App\Repository\Company\CompanyRepository;
use App\Service\Tournament\Interface\ITournamentService;
use App\Service\Traits\BaseServiceTrait;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\Security;

class TournamentService implements ITournamentService
{
    use BaseServiceTrait;

    private TournamentRepository $tournamentRepository;
    private CompanyRepository $companyRepository;
    private EntityManagerInterface $entityManager;
    private Security $security;

    public function __construct(
        TournamentRepository $tournamentRepository,
        CompanyRepository $companyRepository,
        EntityManagerInterface $entityManager,
        Security $security
    ) {
        $this->tournamentRepository = $tournamentRepository;
        $this->companyRepository = $companyRepository;
        $this->entityManager = $entityManager;
        $this->security = $security;
    }

    public function createTournament(Request $request): GenericResponse
    {
        try {
            // Check if user is admin
            if (!$this->isAdmin()) {
                return ResponseDtoBuilder::buildErrorResponse('Access denied. Admin privileges required.');
            }

            $data = json_decode($request->getContent(), true);

            $tournament = new Tournament();
            $tournament->setName($data['name'])
                      ->setDescription($data['description'] ?? '')
                      ->setCategory($data['category'])
                      ->setStartDate(new \DateTime($data['startDate']))
                      ->setEndDate(new \DateTime($data['endDate']))
                      ->setLocation($data['location'] ?? null)
                      ->setRules($data['rules'] ?? null)
                      ->setMaxTeams($data['maxTeams'] ?? null)
                      ->setStatus($data['status'] ?? 'DRAFT')
                      ->setActive(true);

            $this->tournamentRepository->save($tournament, true);

            return ResponseDtoBuilder::buildSuccessResponse([
                'tournament' => $this->formatTournamentData($tournament)
            ], 'Tournament created successfully');

        } catch (\Exception $e) {
            return ResponseDtoBuilder::buildErrorResponse('Error creating tournament: ' . $e->getMessage());
        }
    }

    public function getAllTournaments(Request $request): GenericResponse
    {
        try {
            $status = $request->query->get('status');
            $category = $request->query->get('category');
            $active = $request->query->getBoolean('active', true);

            $criteria = ['active' => $active];
            if ($status) $criteria['status'] = $status;
            if ($category) $criteria['category'] = $category;

            $tournaments = $this->tournamentRepository->findBy($criteria, ['startDate' => 'ASC']);

            $formattedTournaments = array_map(
                fn($tournament) => $this->formatTournamentData($tournament),
                $tournaments
            );

            return ResponseDtoBuilder::buildSuccessResponse([
                'tournaments' => $formattedTournaments,
                'total' => count($formattedTournaments)
            ]);

        } catch (\Exception $e) {
            return ResponseDtoBuilder::buildErrorResponse('Error fetching tournaments: ' . $e->getMessage());
        }
    }

    public function getTournament(string $id): GenericResponse
    {
        try {
            $tournament = $this->tournamentRepository->findOneBy(['uuid' => $id, 'active' => true]);

            if (!$tournament) {
                return ResponseDtoBuilder::buildErrorResponse('Tournament not found');
            }

            return ResponseDtoBuilder::buildSuccessResponse([
                'tournament' => $this->formatTournamentData($tournament, true)
            ]);

        } catch (\Exception $e) {
            return ResponseDtoBuilder::buildErrorResponse('Error fetching tournament: ' . $e->getMessage());
        }
    }

    public function updateTournament(Request $request, string $id): GenericResponse
    {
        try {
            // Check if user is admin
            if (!$this->isAdmin()) {
                return ResponseDtoBuilder::buildErrorResponse('Access denied. Admin privileges required.');
            }

            $tournament = $this->tournamentRepository->findOneBy(['uuid' => $id, 'active' => true]);

            if (!$tournament) {
                return ResponseDtoBuilder::buildErrorResponse('Tournament not found');
            }

            $data = json_decode($request->getContent(), true);

            if (isset($data['name'])) $tournament->setName($data['name']);
            if (isset($data['description'])) $tournament->setDescription($data['description']);
            if (isset($data['category'])) $tournament->setCategory($data['category']);
            if (isset($data['startDate'])) $tournament->setStartDate(new \DateTime($data['startDate']));
            if (isset($data['endDate'])) $tournament->setEndDate(new \DateTime($data['endDate']));
            if (isset($data['location'])) $tournament->setLocation($data['location']);
            if (isset($data['rules'])) $tournament->setRules($data['rules']);
            if (isset($data['maxTeams'])) $tournament->setMaxTeams($data['maxTeams']);
            if (isset($data['status'])) $tournament->setStatus($data['status']);

            $this->tournamentRepository->save($tournament, true);

            return ResponseDtoBuilder::buildSuccessResponse([
                'tournament' => $this->formatTournamentData($tournament)
            ], 'Tournament updated successfully');

        } catch (\Exception $e) {
            return ResponseDtoBuilder::buildErrorResponse('Error updating tournament: ' . $e->getMessage());
        }
    }

    public function deleteTournament(string $id): GenericResponse
    {
        try {
            // Check if user is admin
            if (!$this->isAdmin()) {
                return ResponseDtoBuilder::buildErrorResponse('Access denied. Admin privileges required.');
            }

            $tournament = $this->tournamentRepository->findOneBy(['uuid' => $id, 'active' => true]);

            if (!$tournament) {
                return ResponseDtoBuilder::buildErrorResponse('Tournament not found');
            }

            $tournament->setActive(false);
            $this->tournamentRepository->save($tournament, true);

            return ResponseDtoBuilder::buildSuccessResponse([], 'Tournament deleted successfully');

        } catch (\Exception $e) {
            return ResponseDtoBuilder::buildErrorResponse('Error deleting tournament: ' . $e->getMessage());
        }
    }

    public function addCompanyToTournament(Request $request): GenericResponse
    {
        try {
            // Check if user is admin
            if (!$this->isAdmin()) {
                return ResponseDtoBuilder::buildErrorResponse('Access denied. Admin privileges required.');
            }

            $data = json_decode($request->getContent(), true);
            $tournamentId = $data['tournamentId'];
            $companyId = $data['companyId'];

            $tournament = $this->tournamentRepository->findOneBy(['uuid' => $tournamentId, 'active' => true]);
            $company = $this->companyRepository->findOneBy(['uuid' => $companyId, 'active' => true]);

            if (!$tournament) {
                return ResponseDtoBuilder::buildErrorResponse('Tournament not found');
            }

            if (!$company) {
                return ResponseDtoBuilder::buildErrorResponse('Company not found');
            }

            $tournament->addCompany($company);
            $this->tournamentRepository->save($tournament, true);

            return ResponseDtoBuilder::buildSuccessResponse([], 'Company added to tournament successfully');

        } catch (\Exception $e) {
            return ResponseDtoBuilder::buildErrorResponse('Error adding company to tournament: ' . $e->getMessage());
        }
    }

    public function removeCompanyFromTournament(Request $request): GenericResponse
    {
        try {
            // Check if user is admin
            if (!$this->isAdmin()) {
                return ResponseDtoBuilder::buildErrorResponse('Access denied. Admin privileges required.');
            }

            $data = json_decode($request->getContent(), true);
            $tournamentId = $data['tournamentId'];
            $companyId = $data['companyId'];

            $tournament = $this->tournamentRepository->findOneBy(['uuid' => $tournamentId, 'active' => true]);
            $company = $this->companyRepository->findOneBy(['uuid' => $companyId, 'active' => true]);

            if (!$tournament) {
                return ResponseDtoBuilder::buildErrorResponse('Tournament not found');
            }

            if (!$company) {
                return ResponseDtoBuilder::buildErrorResponse('Company not found');
            }

            $tournament->removeCompany($company);
            $this->tournamentRepository->save($tournament, true);

            return ResponseDtoBuilder::buildSuccessResponse([], 'Company removed from tournament successfully');

        } catch (\Exception $e) {
            return ResponseDtoBuilder::buildErrorResponse('Error removing company from tournament: ' . $e->getMessage());
        }
    }

    public function getTournamentStatistics(string $id): GenericResponse
    {
        // Implementation will be added in next part
        return ResponseDtoBuilder::buildSuccessResponse([]);
    }

    public function getTournamentsByCompany(int $companyId): GenericResponse
    {
        // Implementation will be added in next part
        return ResponseDtoBuilder::buildSuccessResponse([]);
    }

    public function getTournamentStandings(string $id): GenericResponse
    {
        // Implementation will be added in next part
        return ResponseDtoBuilder::buildSuccessResponse([]);
    }

    private function formatTournamentData(Tournament $tournament, bool $detailed = false): array
    {
        $data = [
            'id' => $tournament->getId(),
            'uuid' => $tournament->getUuid(),
            'name' => $tournament->getName(),
            'description' => $tournament->getDescription(),
            'category' => $tournament->getCategory(),
            'startDate' => $tournament->getStartDate()?->format('Y-m-d'),
            'endDate' => $tournament->getEndDate()?->format('Y-m-d'),
            'location' => $tournament->getLocation(),
            'status' => $tournament->getStatus(),
            'maxTeams' => $tournament->getMaxTeams(),
            'createdAt' => $tournament->getCreatedAt()?->format('Y-m-d H:i:s'),
        ];

        if ($detailed) {
            $data['rules'] = $tournament->getRules();
            $data['companies'] = $tournament->getCompanies()->map(fn($company) => [
                'id' => $company->getId(),
                'uuid' => $company->getUuid(),
                'name' => $company->getName()
            ])->toArray();
            $data['encountersCount'] = $tournament->getEncounters()->count();
        }

        return $data;
    }

    private function isAdmin(): bool
    {
        $user = $this->security->getUser();
        if (!$user) return false;

        // Check if user has admin meta
        foreach ($user->getMetas() as $meta) {
            if ($meta->getCode() === 'is_admin' && $meta->getValue() === '1') {
                return true;
            }
            if ($meta->getCode() === 'is_super_admin' && $meta->getValue() === '1') {
                return true;
            }
        }

        return false;
    }
}

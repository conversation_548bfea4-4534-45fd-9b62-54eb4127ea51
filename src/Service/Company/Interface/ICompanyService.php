<?php

namespace App\Service\Company\Interface;

use App\Dto\GenericResponse;
use App\Entity\Company\Company;
use Symfony\Component\HttpFoundation\Request;

interface ICompanyService
{

  /**
   * Does something interesting
   *
   * @param string  $uuid  Where something interesting takes place
   * @param bool  $throwIfNotFound  Where something interesting takes place
   * 
   * @throws Some_Exception_Class If something interesting cannot happen
   * <AUTHOR> <<EMAIL>>
   * @return Company|null
   */
  public function getByUuid(string $uuid, bool $throwIfNotFound = true): Company|null;
  
     /**
   * Does something interesting
   *
   * 
   * <AUTHOR> RAY<PERSON> <<EMAIL>>
   * @return GenericResponse
   */
  public function findAll();
  

   /**
   * Does something interesting
   *
   * @param Request $request  Where something interesting takes place
   * 
   * <AUTHOR> RAYE <<EMAIL>>
   * @return GenericResponse
   */
  public function findOne(string $uuid, Request $request): GenericResponse;
}

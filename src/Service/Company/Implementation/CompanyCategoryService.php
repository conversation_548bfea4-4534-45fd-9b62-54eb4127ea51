<?php

namespace App\Service\Company\Implementation;

use App\Constant\DataFixtureFileNames;
use App\Constant\DefaultValues;
use App\Dto\GenericResponse;
use App\Entity\Company\CompanyCategory;
use App\Mapper\Company\Interface\ICompanyCategoryMapper;
use App\Repository\Company\CompanyCategoryRepository;
use App\Service\Traits\BaseServiceTrait;
use App\Service\Traits\LogServiceTrait;
use App\Service\Company\Interface\ICompanyCategoryService;
use App\Service\Traits\DataFixturesServiceTrait;
use Doctrine\Persistence\ObjectManager;
use Symfony\Component\HttpFoundation\Request;

class CompanyCategoryService implements ICompanyCategoryService
{
  use BaseServiceTrait, LogServiceTrait, DataFixturesServiceTrait;

  private ICompanyCategoryMapper $mapper;

  private CompanyCategoryRepository $repository;

  public function __construct(ICompanyCategoryMapper $mapper, CompanyCategoryRepository $repository)
  {
    $this->mapper = $mapper;
    $this->repository = $repository;
  }

  public function getByUuid(string $uuid, bool $throwIfNotFound = true): CompanyCategory|null
  {
    return $this->repository->getByUuid($uuid, $throwIfNotFound);
  }

  public function loadFixtures(ObjectManager $manager): void
  {
    $data = $this->csvFileToArray(DataFixtureFileNames::COMPANY_CATEGORY);
    foreach ($data as $value) {
      $existingCategory = $this->getByUuid($value["UUID"], false);
      $companyCategory = empty($existingCategory) ? $this->mapper->fromArray($value) : $this->mapper->fromArrayUpdate($value, $existingCategory);
      $manager->persist($companyCategory);
    }
    $manager->flush();
  }

  public function findAll(Request $request): GenericResponse
  {
    $params = $this->mapper->fromFindAllRequest($request);
    $data = $this->repository->getItems($params);

    $outputs = $this->mapper->toOutArray($params->getOutput(), ...$data['items']);
    return $this->result([
      'items' => $outputs,
      'total' => $data['total'],
      'totalFiltered' => count($outputs)
    ]);
  }

  public function findOne(string $uuid, Request $request): GenericResponse
  {
    $outputFormat = $request->query->get("output", DefaultValues::DEFAULT_FIND_ONE_OUTPUT_FORMAT);
    $setting = $this->repository->getByUuid($uuid);
    $output = $this->mapper->toOut($outputFormat, $setting);

    return $this->result($output);
  }
}

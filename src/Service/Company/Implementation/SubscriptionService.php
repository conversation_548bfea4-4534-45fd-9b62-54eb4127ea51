<?php

namespace App\Service\Company\Implementation;

use DateTime;
use DateTimeImmutable;
use App\Service\Shared\Implementation\CommonService;
use App\Entity\Payment\Payment;
use App\Mapper\BaseMapperTrait;
use App\Entity\Company\Subscription;
use App\Service\Traits\LogServiceTrait;
use App\Entity\Company\SubscriptionPlan;
use App\Repository\Company\CompanyRepository;
use App\Service\Traits\BaseServiceTrait;
use Symfony\Bundle\SecurityBundle\Security;
use App\Repository\Shared\SettingRepository;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Repository\Company\SubscriptionRepository;
use App\Repository\Company\SubscriptionPlanRepository;
use App\Repository\Payment\PaymentRepository;

class SubscriptionService{
    use BaseServiceTrait, LogServiceTrait, BaseMapperTrait;
    private SubscriptionRepository $subscriptionRepo;
    private SettingRepository $settingRepository;
    private CommonService $commonService;
    private Security $security;
    private CompanyRepository $companyRepo;
    private SubscriptionPlanRepository $subscriptionPlanRepo;
    private PaymentRepository $paymentRepo;
    public function __construct(
      Security $security, 
      SubscriptionRepository $subscriptionRepo, 
      SettingRepository $settingRepository, 
      CommonService $commonService, 
      SubscriptionPlanRepository $subscriptionPlanRepo,
      CompanyRepository $companyRepo,
      PaymentRepository $paymentRepo
      ){
      $this->security = $security;
      $this->settingRepository = $settingRepository;
      $this->subscriptionRepo = $subscriptionRepo;
      $this->commonService = $commonService;
      $this->subscriptionPlanRepo = $subscriptionPlanRepo;
      $this->companyRepo = $companyRepo;
      $this->paymentRepo = $paymentRepo;
    }
    public function subscribePro(Request $request, $payUrl = null)
    {
      /**  @var \App\Entity\Security\User */
      $user = $this->security->getUser();
  
      if (!$user->getCompany()) {
        $this->logger->warning('User not associated to company');
        return $this->result(["message" => "User not associated to any company"], 404, Response::HTTP_NOT_FOUND);
      }
      $order_id = preg_replace('/-/', '', $this->commonService->uuid());//unique order_id
      $subscriptionPlan = $this->subscriptionPlanRepo->findOneBy(["name" => $request->get("plan"), "active" => true]);
      if (!$subscriptionPlan) {
        $this->logger->warning('No {plan} active Subscription plan found !', [
          'plan' => $request->get("plan"),
        ]);
        return $this->result(["message" => "No corresponding active Subscription plan found"], 404, Response::HTTP_NOT_FOUND);
      }
      $payment = new Payment();
      $payment->setUser($user);
      $payment->setPayUrl($payUrl.$order_id);
      $payment->setCurrencyCode("XAF");
      $payment->setCompanyName($user->getCompany()->getName());
      $payment->setAmount($subscriptionPlan->getPrice());
      $payment->setPhoneNumber($user->getPhoneNumber());
      $payment->setClientName($user->getDisplayName());
      $payment->setDescription($subscriptionPlan->getName());
      $payment->setMailAddress($user->getEmail());
      $payment->setOrderID($order_id);
      $payment->setStatus("INITIATED");
      $payment->setPaymentType("PRO");
      $this->persist($payment);
  
      $this->logger->info('Company payment created : {order_id}', [
        'order_id' => $order_id,
      ]);

      $activeSubscriptions = $this->subscriptionRepo->findBy(["company" => $user->getCompany(), "active" => true]);

      if (count($activeSubscriptions) > 0) {
        $this->logger->info('Company already have an active subscription');
        $mostRecentSubscription = $activeSubscriptions[count($activeSubscriptions) - 1];
      }
  
      $subscription = new Subscription();
      $subscription->setPlan($subscriptionPlan);
      $subscription->setCompany($user->getCompany());
      !isset($mostRecentSubscription)?$subscription->setStartDate(new DateTimeImmutable()):$subscription->setStartDate($mostRecentSubscription->getEndDate());
      $subscription->setEndDate($subscription->getStartDate()->modify( "+" . $subscriptionPlan->getDuration() ." days" ));
      $subscription->setPayment($payment);
      $subscription->setDescription($subscriptionPlan->getName());
      $subscription->setStatus("INITIATED");
      $subscription->setActive(false);  
      $this->persist($subscription);
      $this->logger->info('Subscription created : {plan}', [
        'plan' => $subscriptionPlan->getName(),
      ]);
      
       //notify user
      if ($user->getEmail()) {
          $template = $user->getMetaValueByCode("language","fr")=="fr" ? "pay-subscription-request":"pay-subscription-request-en";
  
          $this->commonService->sendMail2("[sportaabe ] Lien de paiement d'abonnement", $payment, $template);
      } 
  
      if ($user->getPhoneNumber()) {
        $s = $user->getMetaValueByCode('language','fr') == "fr" ? "Hello, pour payer pour l'abonnement " . strtoupper($subscriptionPlan->getName()) . ", utilisez ce lien: " . $payment->getPayUrl()." Merci d'utiliser sportaabe."
        :"Hello, to pay for " . strtoupper($subscriptionPlan->getName()) . " subscription, use this link: " . $payment->getPayUrl()." Thanks for using sportaabe";
        $this->commonService->sendAwsMessage($user->getPhoneNumber(), $s);
      }
      
  
      return $this->result($this->toSubscriptionInfo($subscription));
    }

    public function updateSubscriptionAdmin(Request $request)
    {
        $subscription = $this->subscriptionRepo->findOneBy(["id" => $request->get("id")]);
        if (gettype($subscription) !== "object" || empty($subscription)) {
            $this->logger->warning('Subscription not found : {id}', [
                'id' => $request->get("id"),
            ]);
            return $this->result(["message" => "Subscription not found"], 404, Response::HTTP_NOT_FOUND);
        }
        $subscription->setActive($request->get("active"));
        $date = new DateTimeImmutable($request->get("endDate"));
        $subscription->setEndDate($date);
        $this->persist($subscription);
        $this->logger->info('Subscription updated : {id}', [
            'id' => $request->get("id"),
        ]);
        return $this->result($this->toSubscriptionAdminInfo($subscription));
    }

    public function getSubscriptionsAdmin()
    {
      try{
            $subscriptions = $this->subscriptionRepo->findBy([], ['createdAt' => 'DESC']);
            $subs = [];
            foreach($subscriptions as $subscription){
              $subs[] = $this->toSubscriptionAdminInfo($subscription);

            }
            return $this->result($subs);

        } catch (\Exception $e) {
            return $this->result([]);
        }
    }

    public function getSubscriptionsPayments(Request $request)
    {
        try {
            $admin = $request->query->get("admin") == "false" ? false : true;

            /** @var \App\Entity\Security\User */
            $user = $this->security->getUser();

            $qb = $this->paymentRepo->createQueryBuilder('p');

            if ($admin) {
                $qb->orderBy('p.id', 'DESC');
            } else {
                $qb->andWhere('p.phoneNumber = :user1 OR p.mailAddress = :user2')
                  ->andWhere('p.paymentType = :paytype')
                  ->setParameter('user1', $user->getPhoneNumber())
                  ->setParameter('user2', $user->getEmail())
                  ->setParameter('paytype', 'PRO')
                  ->orderBy('p.id', 'DESC');
            }

            if ($request->query->get("last5") === "true") {
                $qb->setMaxResults(5);
            }

            $results = $qb->getQuery()->getResult();
            $payments = [];
            foreach($results as $result){
              $payment = $this->toSubscriptionPaymentInfo($result,$this->subscriptionRepo);
              if($payment){
                $payments [] = $payment;
              }
            }

            return $this->result($payments);

        } catch (\Exception $e) {
            return $this->result([]);
        }
    }
    public function removeSubscriptionPayment(mixed $id)
    {
      try {
        $subscription = $this->subscriptionRepo->findOneBy(['id' => $id, 'status' => ['INITIATED', 'EXPIRED'], 'active' => false]);
        if (!$subscription) {
          return $this->result(['message' => 'subscription not found or cannot be removed'], 404, Response::HTTP_NOT_FOUND);
        }
        $this->delete($subscription);
        if($subscription->getStatus() == 'INITIATED'){
          try{
            $this->delete($subscription->getPayment());
            $this->logger->info('Subscription and linked payment removed : {id}', [
              'id' => $id,
            ]);

          }
          catch(\Exception $e){
            $this->logger->error($e->getMessage());
          }
            
        }else{
          $this->logger->info('Subscription removed : {id}', [
            'id' => $id,
          ]);
          return $this->result(['message' => 'subscription removed'], 200, Response::HTTP_OK);
          
        }
      } catch (\Exception $e) {
        return $this->result(null, $e->getMessage());
      }
    }

    public function removeSubscriptionPlan(mixed $id)
    {
      try {
        $subscriptionPlan = $this->subscriptionPlanRepo->findOneBy(['id' => $id, 'active' => false]);
        if (!$subscriptionPlan) {
          return $this->result(['message' => 'subscription plan not found or cannot be removed'], 404, Response::HTTP_NOT_FOUND);
        }

        if($subscriptionPlan->getSubscriptions()->count()==0)
        {
          $this->delete($subscriptionPlan);
          $this->logger->info('Subscription Plan removed : {id}', [
              'id' => $id,
            ]);
        }
        else{
          return $this->result(['message' => 'subscription plan cannot be removed because it has one or more subscriptions linked'], 404, Response::HTTP_NOT_FOUND);
        }
      
      } catch (\Exception $e) {
        return $this->result(null, $e->getMessage());
      }
    }

    public function getSubscriptionPlans(Request $request)
    {
      $user = $this->security->getUser();

      $subscriptionPlans = $request->query->get('admin') == "true"  && $user->getMetaValueByCode("is_admin") == true
      ? $this->subscriptionPlanRepo->findBy(array(), array('price' => 'ASC')):
      $this->subscriptionPlanRepo->findBy(array("active" => "1"), array('price' => 'ASC'));

      $plans = [];
      if ($request->query->get('admin') == "true")
      {
        foreach($subscriptionPlans as $subscriptionPlan){
          $plans [] = $this->toSubscriptionPlanAdminInfo($subscriptionPlan);
        }
      }
      else{
        foreach($subscriptionPlans as $subscriptionPlan){
          $plans [] = $this->toSubscriptionPlanInfo($subscriptionPlan);
        }
      }
      $plans = [];
    foreach($subscriptionPlans as $subscriptionPlan){
      $plans [] = ($request->query->get('admin') == "true") 
        ? $this->toSubscriptionPlanAdminInfo($subscriptionPlan) 
        : $this->toSubscriptionPlanInfo($subscriptionPlan);
    }
      return $this->result($plans);
    }
    public function getSubscriptionPlan(mixed $id)
    {
      if(is_numeric($id)){
        $subscriptionPlan = $this->subscriptionPlanRepo->find($id);
      }else{
        $subscriptionPlan = $this->subscriptionPlanRepo->findOneBy(["name" => $id]);
      }
      if (!$subscriptionPlan) {
        $this->logger->warning('Subscription plan not found : {id}', [
          'id' => $id,
        ]);
        return $this->result(["message" => "Subscription plan not found"], 404, Response::HTTP_NOT_FOUND);
      }
      return $this->result($subscriptionPlan);
    }
    public function updateSubscriptionPlan(Request $request, mixed $id)
    {
      $subscriptionPlan = $this->getSubscriptionPlan($id)->getData();
      if (gettype($subscriptionPlan) !== "object" || empty($subscriptionPlan)) {
        $this->logger->warning('Subscription plan not found : {id}', [
          'id' => $id,
        ]);
        return $this->result(["message" => "Subscription plan not found"], 404, Response::HTTP_NOT_FOUND);
      }
      $user = $this->security->getUser();
      $isSuperAdmin = $user->getMetaValueByCode('is_super_admin',0);
      if($isSuperAdmin != 1){
        return $this->result(["message" => "Only super admins can update subscription plans"], 403, Response::HTTP_FORBIDDEN);
      }
      $subscriptionPlan->setName($request->get("name"));
      $subscriptionPlan->setDescription($request->get("description"));
      $subscriptionPlan->setPrice($request->get("price"));
      $subscriptionPlan->setDuration($request->get("duration"));
      $this->persist($subscriptionPlan);
      $this->logger->info('Subscription plan updated : {plan}', [
        'plan' => $subscriptionPlan->getName(),
      ]);
      return $this->result($this->toSubscriptionPlanInfo($subscriptionPlan));
    }
    public function toggleSubscriptionPlan(mixed $id)
    {
      $subscriptionPlan = $this->getSubscriptionPlan($id)->getData();
      if (gettype($subscriptionPlan) !== "object" || empty($subscriptionPlan)) {
        $this->logger->warning('Subscription plan not found : {id}', [
          'id' => $id,
        ]);
        return $this->result(["message" => "Subscription plan not found"], 404, Response::HTTP_NOT_FOUND);
      }
      $user = $this->security->getUser();
      $isSuperAdmin = $user->getMetaValueByCode('is_super_admin',0);
      if($isSuperAdmin != 1){
        return $this->result(["message" => "Only super admins can enable or disable subscription plans"], 403, Response::HTTP_FORBIDDEN);
      }
      if($subscriptionPlan->isActive()) {
        $subscriptionPlan->setActive(false);
        $this->persist($subscriptionPlan);
        $this->logger->info('Subscription plan disabled : {plan}', [
          'plan' => $subscriptionPlan->getName(),
        ]);
      }else{
        $subscriptionPlan->setActive(true);
        $this->persist($subscriptionPlan);
        $this->logger->info('Subscription plan enabled : {plan}', [
          'plan' => $subscriptionPlan->getName(),
        ]);
      }
      return $this->result($this->toSubscriptionPlanInfo($subscriptionPlan));
    }
    public function createSubscriptionPlan(Request $request)
    {
      $user = $this->security->getUser();
      $isSuperAdmin = $user->getMetaValueByCode('is_super_admin',0);
      if($isSuperAdmin != 1){
        return $this->result(["message" => "Only super admins can create subscription plans"], 403, Response::HTTP_FORBIDDEN);
      }
      $subscriptionPlan = new SubscriptionPlan();
      $subscriptionPlan->setName($request->get("name"));
      $subscriptionPlan->setDescription($request->get("description"));
      $subscriptionPlan->setPrice($request->get("price"));
      $subscriptionPlan->setDuration($request->get("duration"));
      $subscriptionPlan->setActive(false);
      $this->persist($subscriptionPlan);
      $this->logger->info('Subscription plan created : {plan}', [
        'plan' => $subscriptionPlan->getName(),
      ]);
      return $this->result($this->toSubscriptionPlanInfo($subscriptionPlan));
    }
    public function checkCompanyTrials()
    {
      $companies = $this->companyRepo->findBy(["active" => true]);
      foreach ($companies as $company) {
        if (!empty($company->getMetaValueByCode('_trialEndDate'))) {
          $trialEndDate = $company->getMetaValueByCode('_trialEndDate');
          $trialEndDate = new DateTime($trialEndDate);
          $subscription = $this->subscriptionRepo->findOneBy(["active" => true, "company" => $company]);
  
          if ($trialEndDate < new DateTime() && is_null($subscription) ) {
            $this->logger->info('Company trial expired : {companyname}', [
              'companyname' => $company->getName(),
            ]);
            $company->setActive(false);
            $this->persist($company);
          }
        }
      }
      return $this->result([]);
      
    }
    public function checkSubscriptions(){
        $subscriptions = $this->subscriptionRepo->findBy(["active" => true]);

        if(empty($subscriptions)){
          return $this->result([]);
        }
        $notified = [];
        $expireds = [];
        $reminder_delay=$this->settingRepository->findOneBy(["code"=>"reminder_delay"])?->getValue();
    
        foreach ($subscriptions as $subscription) {
          if($subscription->getEndDate() < new DateTime()){
            $subscription->setStatus("EXPIRED");
            $subscription->setActive(false);
            $this->persist($subscription);
            $company = $subscription->getCompany();
            $subs = $this->subscriptionRepo->findBy(["active" => true, "company" => $company]);
            if(empty($subs)){
              $expireds[] = $subscription;
              $company->setActive(false);
              $this->persist($company);
            }
            
          }elseif(new \DateTime() >= $subscription->getEndDate()->modify('-'.($reminder_delay??'1').' days') && $subscription->getStatus() != "NOTIFIED"){
            $subscription->setStatus("NOTIFIED");
            $this->persist($subscription);
            $company = $subscription->getCompany();
            $subs = $this->subscriptionRepo->findBy(["active" => true, "company" => $company]);
            if(empty($subs)){
              $notified[] = $subscription;
            }
          }
        }
        //dd($expireds);
    
        if( count($notified) > 0){
    
          foreach ($notified as $n) {
    
            if($n->getPayment()->getMailAddress()){
              $template = $n->getPayment()->getUser()->getMetaValueByCode("language","fr")=="fr"?"subscription-remind":"subscription-remind-en";
              $this->commonService->sendMail3($n->getPayment()->getMailAddress(), "[sportaabe ] Rappel d'expiration d'abonnement", $n, $template, false);
            }
            if($n->getPayment()->getPhoneNumber()){
              $plan_name = strtoupper($n->getDescription());
              $end_date = $n->getEndDate()->format('d/m/Y H:i:s')." {$n->getEndDate()->getTimezone()->getName()}";
              $s = $n->getPayment()->getUser()->getMetaValueByCode("language","fr")=="fr"?"RAPPEL- ABONNEMENT: Votre abonnement {$plan_name} arrivera à expiration ce {$end_date}. Pensez à vous réabonner pour continuer de bénéficier des avantages PRO de sportaabe.":"REMINDER - SUBSCRIPTION: Your subscription {$plan_name} will expire on {$end_date}. Please do not forget to renew to benefit from PRO features of sportaabe.";
              $this->commonService->sendAwsMessage($n->getPayment()->getPhoneNumber(), $s);
            }
        }
      }
        if( count($expireds) > 0){
    
          foreach ($expireds as $e) {
    
            if($e->getPayment()->getMailAddress()){
              $template = $e->getPayment()->getUser()->getMetaValueByCode("language","fr")=="fr"?"subscription-expired":"subscription-expired-en";
              $this->commonService->sendMail3($e->getPayment()->getMailAddress(), "[sportaabe ] Abonnement Expiré", $e, $template, false);
            }
            if($e->getPayment()->getPhoneNumber()){
              $plan_name = strtoupper($e->getDescription());
              $end_date = $e->getEndDate()->format('d/m/Y H:i:s')." {$e->getEndDate()->getTimezone()->getName()}";
              $s = $e->getPayment()->getUser()->getMetaValueByCode("language","fr")=="fr"?"ABONNEMENT EXPIRE: Votre abonnement {$plan_name} a expiré ce {$end_date}. Veuillez vous réabonner pour bénéficier des avantages PRO de sportaabe." : "SUBSCRIPTION EXPIRED: Your subscription {$plan_name} has expired on {$end_date}. Please renew to benefit from PRO features of sportaabe.";
              $this->commonService->sendAwsMessage($e->getPayment()->getPhoneNumber(), $s);
            }
        }
      }

    $notifs=[];
    $exps=[];

    foreach ($notified as $notif) {
      $notifs[] = $this->toSubscriptionInfo($notif);
    }
    foreach ($expireds as $exp) {
      $exps[] = $this->toSubscriptionInfo($exp);
    }
    
        return $this->result(["notified" => $notifs, "expireds" => $exps]);
    }

}

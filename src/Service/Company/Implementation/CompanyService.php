<?php

namespace App\Service\Company\Implementation;

use App\Dto\GenericResponse;
use App\Constant\DefaultValues;
use App\Entity\Company\Company;
use App\Mapper\BaseMapperTrait;
use App\Entity\Company\CompanyMeta;
use App\Service\Traits\LogServiceTrait;
use App\Service\Traits\BaseServiceTrait;
use App\Repository\Security\UserRepository;
use Symfony\Bundle\SecurityBundle\Security;
use App\Repository\Company\CompanyRepository;
use Symfony\Component\HttpFoundation\Request;
use App\Repository\Activity\ActivityRepository;
use App\Mapper\Company\Interface\ICompanyMapper;
use App\Service\Company\Interface\ICompanyService;

class CompanyService implements ICompanyService
{
  use BaseServiceTrait, LogServiceTrait, BaseMapperTrait;

  private ICompanyMapper $mapper;
  private Security $security;
  private CompanyRepository $companyRepo;
  private ActivityRepository $activityRepo;

  private UserRepository $userRepo;

  public function __construct(ICompanyMapper $addressMapper, Security $security, CompanyRepository $companyRepo, ActivityRepository $activityRepo, UserRepository $userRepo)
  {
    $this->mapper = $addressMapper;
    $this->security = $security;
    $this->companyRepo = $companyRepo;
    $this->activityRepo = $activityRepo;
    $this->userRepo = $userRepo;
  }

  public function business(string $uuid)
  {
    return $this->result($this->toCompanyInfo($this->companyRepo->getByUuid($uuid)));
  }

  public function updateCompany(Request $request)
  {
    $body = $request->request->all();

    /**  @var \App\Entity\Security\User */
    $user = $this->security->getUser();

    if (!empty($user->getCompany())) {
      $company = $user->getCompany();
      $company->setName(ucwords(strtolower($body["_name"])));

      $company->setDescription($body["_contacts"]["_description"]);

      $metas = ["_type", "_siren", "_activity", "_coverImage", "_profileImage", "_profileImage", "_backCardPicture", "_frontCardPicture", "_idProfessional", "_website", "_email", "_phoneNumber", "_address", "_zipCode", "_latitude", "_longitude", "_city", "_country"];
      foreach ($metas as $key) {

        if (array_key_exists($key, $body) && !empty($body[$key]) && $key !== "_address") {
          $company->addMeta(
            (new CompanyMeta())
              ->setCode($key)
              ->setValue($body[$key])
          );
        } else if (!empty($body["_contacts"][$key])) {
          $company->addMeta(
            (new CompanyMeta())
              ->setCode($key)
              ->setValue($body["_contacts"][$key])
          );
        } else if (!empty($body["_address"][$key])) {
          $company->addMeta(
            (new CompanyMeta())
              ->setCode($key)
              ->setValue($body["_address"][$key])
          );
        }
      }
      $hours = "";
      foreach ($body["_openingHours"] as $value) {
        $hours .= '|' . $value['_startTime'] . ',' . $value['_endTime'] . ',' . $value['_openingDays'] . ',' . $value['_isPaused'];
      }
      $company->addMeta(
        (new CompanyMeta())
          ->setCode('_openingHours')
          ->setValue($hours)
      );

      $this->persist($company);
      $this->logger->info('Company updated : {companyname}', [
        'companyname' => $company->getName(),
      ]);
      $user->setCompany($company);
    }


    return $this->result($this->toUserInfo($user));
  }

  public function updateCompanyAdmin(Request $request)
  {
    $body = $request->request->all();
    //dd($body);

    if (!empty($body)) {
      $company = $this->companyRepo->getByUuid($body["uuid"]);
      $company->setName($body["name"]);

      $company->setDescription($body["description"]);
      $company->setActive($body["active"]);
      $metas = ["_type", "_siren", "_activity", "_coverImage", "_profileImage", "_profileImage", "_backCardPicture", "_frontCardPicture", "_idProfessional", "_website", "_email", "_phoneNumber", "_address", "_zipCode", "_latitude", "_longitude", "_city", "_country", "_membership"];
      foreach ($metas as $key) {

        if (array_key_exists($key, $body) && !empty($body[$key]) && $key !== "_address") {
          $company->addMeta(
            (new CompanyMeta())
              ->setCode($key)
              ->setValue($body[$key])
          );
        } else if (!empty($body[$key])) {
          $company->addMeta(
            (new CompanyMeta())
              ->setCode($key)
              ->setValue($body[$key])
          );
        } else if (!empty($body[$key])) {
          $company->addMeta(
            (new CompanyMeta())
              ->setCode($key)
              ->setValue($body[$key])
          );
        }
      }
      /*  $hours = "";
      foreach ($body["_activeDays"] as $value) {
        $hours .= '|' . $value['_startTime'] . ',' . $value['_endTime'] . ',' . $value['_openingDays'] . ',' . $value['_isPaused'];
      }
      $company->addMeta(
        (new CompanyMeta())
          ->setCode('_openingHours')
          ->setValue($hours)
      );  */

      $this->persist($company);
      $this->logger->info('Company updated by admin : {companyname}', [
        'companyname' => $company->getName(),
      ]);
    }


    return $this->result($this->toBusinessAdminInfo($company));
  }


  public function createCompany(Request $request)
  {
    $body = $request->request->all();

    /**  @var \App\Entity\Security\User */
    $user = $this->security->getUser();


    $company = new Company();
    $company->setName(ucwords(strtolower($body["_name"])));
    $company->setActive(true);

    $metas = ["_type", "_siren", "_membership", "_activity", "_coverImage", "_profileImage", "_backCardPicture", "_frontCardPicture", "_idProfessional", "_website", "_email", "_phoneNumber", "_address", "_zipCode", "_latitude", "_longitude", "_city", "_country"];
    foreach ($metas as $key) {
      if ($key == "_address") {
        $company->addMeta(
          (new CompanyMeta())
            ->setCode($key)
            ->setValue($body["_address"][$key])
        );
        continue;
      }
      if (!empty($body[$key])) {
        $company->addMeta(
          (new CompanyMeta())
            ->setCode($key)
            ->setValue($body[$key])
        );
      } else if (!empty($body["_contacts"][$key])) {
        $company->addMeta(
          (new CompanyMeta())
            ->setCode($key)
            ->setValue($body["_contacts"][$key])
        );
      } else if (!empty($body["_address"][$key])) {
        $company->addMeta(
          (new CompanyMeta())
            ->setCode($key)
            ->setValue($body["_address"][$key])
        );
      }
    }

    $company->addMeta(
      (new CompanyMeta())
        ->setCode('_is_verified')
        ->setValue(0)
    );


    $this->persist($company);
    $trialEndDate = $company->getCreatedAt()->modify('+14 days');
    $trialEndDateFormatted = $trialEndDate->format('Y-m-d H:i:s');    
    $company->addMeta(
        (new CompanyMeta())
            ->setCode('_trialEndDate')
            ->setValue($trialEndDateFormatted)
    );
    $user->setCompany($company);
    $this->persist($user);
    $this->logger->info('Company created : {companyname} - Trial end date : {trialEndDateFormatted}', [
      'companyname' => $company->getName(),
      'trialEndDateFormatted' => $trialEndDateFormatted,
    ]);
    return $this->result($this->toCompanyInfo($company));
  }

  public function deleteCompanyAdmin(string $uuid)
  {
    $sportaabe_promotion_owner = $this->userRepo->findOneBy(["company"=>$this->companyRepo->findOneBy(["name"=>"sportaabe promotion"])]);

    //dd($sportaabe_promotion_owner);

    $company = $this->companyRepo->getByUuid($uuid);
    $data = [
      'id' => $company->getUuid(),
      'title' => $company->getName()
    ];
    $pro =  $this->userRepo->findOneBy(["company"=>$company]);

    if(!is_null($pro)){
      $pro->setCompany(NULL);
      $this->persist($pro);
    }
    $activities = $this->activityRepo->findBy(["creator"=>$pro]);
    //$sportaabe_promotion_owner = $this->userRepo->findOneBy([["company"]=>$this->companyRepo->findOneBy(["name"=>"sportaabe promotion"])]);

    if(!is_null($activities) && !is_null($sportaabe_promotion_owner)){
      foreach ($activities as $activity) {
        $activity->setCreator($sportaabe_promotion_owner)
        ->setOwner($sportaabe_promotion_owner)
        ->setActive(0);
        $this->persist($activity);
      }
    }
    
    $this->delete($company);
    $this->logger->info("Company deleted by admin: " . $company->getName());

    return $this->result($data);
  }


  public function getByUuid(string $uuid, bool $throwIfNotFound = true): Company|null
  {
    return $this->companyRepo->getByUuid($uuid, $throwIfNotFound);
  }
  public function findAll()
  {
    $companies = $this->companyRepo->findAll();
    $output=[];
    
    foreach ($companies as $company) {
      $output[]=$this->toBusinessAdminInfo($company);
    }
    return $this->result($output);
  }
  
  public function findOne(string $uuid, Request $request): GenericResponse
  {
    $company = $this->companyRepo->getByUuid($uuid);
    $output = $this->toBusinessInfo($company);
    return $this->result($output);
  }
}

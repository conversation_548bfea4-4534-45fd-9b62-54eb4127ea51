<?php

namespace App\Service\Traits;

use Psr\Log\LoggerInterface;
use Symfony\Contracts\Service\Attribute\Required;

trait LogServiceTrait
{
  #[Required]
  public LoggerInterface $logger;

  public function info($value)
  {
    $this->logger->info('Example de log ' . $value);
  }

  public function error($value)
  {
    $this->logger->error('Example de log ' . $value);
  }

  public function writeInLogFile($data, $level)
  {
    $day = gmdate("Y-m-d");
    $logfile = $this->kernel->getProjectDir() . '/var/log/sportaabe_' . $day . '.log';
    error_log("\r\n" . '[' . gmdate("Y-m-d H:i:s") . '] ' . $level . ' ' . $data, 3, $logfile);
  }
}

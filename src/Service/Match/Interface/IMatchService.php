<?php

namespace App\Service\Match\Interface;

use App\Entity\Match\Match;
use Symfony\Component\HttpFoundation\Request;

interface IMatchService
{
    public function createMatch(Request $request): Match;
    public function updateMatch(Request $request, string $uuid): Match;
    public function getMatch(string $uuid, bool $throwIfNotFound = true): ?Match;
    public function getMatches(Request $request): array;
    public function addGoal(Request $request, string $matchUuid): Match;
    public function updateScore(Request $request, string $matchUuid): Match;
}
<?php

namespace App\Service\Chat\Implementation;

use Mailgun\Mailgun;
use App\Dto\GenericResponse;
use App\Constant\DefaultValues;
use App\Entity\Chat\Attachment;
use App\Mapper\BaseMapperTrait;
use App\Entity\Activity\Activity;
use App\Entity\Activity\ActivityMeta;
use App\Constant\DataFixtureFileNames;
use Kreait\Firebase\Contract\Database;
use App\Service\Traits\LogServiceTrait;
use Doctrine\Persistence\ObjectManager;
use App\Service\Traits\BaseServiceTrait;
use App\Entity\Activity\ActivityCategory;
use App\Repository\Chat\RequestRepository;
use App\Entity\Chat\Request as ChatRequest;
use App\Repository\Chat\ResponseRepository;
use App\Repository\Security\UserRepository;
use Symfony\Bundle\SecurityBundle\Security;
use App\Entity\Chat\Response as ChatResponse;
use App\Repository\Company\CompanyRepository;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Repository\Activity\ActivityRepository;
use App\Service\Chat\Interface\ISupportService;
use App\Service\Traits\DataFixturesServiceTrait;
use App\Service\Activity\Interface\IActivityService;
use App\Service\Shared\Implementation\CommonService;
use App\Repository\Activity\ActivityCategoryRepository;
use App\Mapper\Activity\Interface\IActivityCategoryMapper;
use App\Service\Activity\Interface\IActivityCategoryService;

class SupportService implements ISupportService
{
  use BaseServiceTrait, BaseMapperTrait, LogServiceTrait, DataFixturesServiceTrait;

  private IActivityCategoryMapper $mapper;
  private Security $security;
  private CompanyRepository $companyRepo;

  private ActivityCategoryRepository $repository;
  private CommonService $commonService;
  private ActivityRepository $activityRepo;
  private RequestRepository $requestRepo;
  private UserRepository $userRepo;
  private Database $firebaseRealTimeDb;
  private ResponseRepository $responseRepo;

  public function __construct(IActivityCategoryMapper $mapper, ActivityCategoryRepository $repository, Security $security, CompanyRepository $companyRepo, CommonService $commonService, ActivityRepository $activityRepo, RequestRepository $requestRepo, UserRepository $userRepo, Database $firebaseRealTimeDb, ResponseRepository $responseRepo)
  {
    $this->mapper = $mapper;
    $this->repository = $repository;
    $this->security = $security;
    $this->companyRepo = $companyRepo;
    $this->commonService = $commonService;
    $this->activityRepo = $activityRepo;
    $this->requestRepo = $requestRepo;
    $this->userRepo = $userRepo;
    $this->firebaseRealTimeDb = $firebaseRealTimeDb;
    $this->responseRepo = $responseRepo;
  }

  public function getRequests(Request $request)
  {
    try {
      $admin = $request->query->get("admin") == "false" ? false : true;
      $status = $request->query->get("status") == "true" ? true : false;
      $sort = $request->query->get("sort") == "updated_at" ? "updated_at" : "id";

      /**  @var \App\Entity\Security\User */
      $user = $this->security->getUser();

      if ($admin) {
        $sql = "SELECT * FROM requests ORDER BY {$sort} DESC";
        $stmt = $this->em->getConnection()->prepare($sql);
        $res = $stmt->executeQuery()->fetchAllAssociative();
      } else {
        $sql = "SELECT * FROM requests where user = :user1 or user = :user2 ORDER BY id DESC";
        $params = array('user1' => $user->getPhoneNumber(), 'user2' => $user->getEmail());
        $stmt = $this->em->getConnection()->prepare($sql);
        $res = $stmt->executeQuery($params)->fetchAllAssociative();
      }

      $count = 0;

      foreach ($res as $k => $v) {
        $ress = $this->getRes($v["id"]);

        if (count($ress) > 0) {
          
          if (intval($v["closed"]) == 1) {
            $v["status"] = 3;
          } else if (boolval($ress[0]["is_admin"]) && !boolval($v["closed"])) {
            $v["status"] = 1;
            $count = $count + 1;
          } else if (!boolval($ress[0]["is_admin"]) && !boolval($v["closed"])) {
            $v["status"] = 2;
          } else {
            $v["status"] = 0;
          }
          $res[$k] = $v;

        }
      }
      

      if ($status) {
        return $this->result(array("count" => $count));
      }
      $data = [];
      foreach ($res as $value) {
        $responses = $this->getRes($value["id"]);
        if (count($responses) > 0) {
          $value["last_response"] = $responses[0];
        }
        $data[] = $value;
      }
      return $this->result($data);
    } catch (\Exception $e) {
      return $this->result([]);
    }
  }
  private function getRes($id)
  {
    $sql = "SELECT * FROM responses where request_id = :request_id ORDER BY id DESC";
    $params = array('request_id' => $id);
    $stmt = $this->em->getConnection()->prepare($sql);
    return $stmt->executeQuery($params)->fetchAllAssociative();
  }

  public function getRequest(Request $request, string $id)
  {
    try {
      $sql = "SELECT * FROM requests where id = :id ORDER BY id DESC";
      $params = array('id' => $id);

      $stmt = $this->em->getConnection()->prepare($sql);
      return $this->result($stmt->executeQuery($params)->fetchAllAssociative());
    } catch (\Exception $e) {
      return $this->result(null);
    }
  }

  public function getRequestsResponses(Request $request, string $id)
  {
    try {
      return $this->result($this->getRes($id));
    } catch (\Exception $e) {
      return $this->result(null);
    }
  }

  public function getRequestsAttachments(Request $request, string $id)
  {
    try {
      $sql = "SELECT * FROM attachments where request_id = :request_id ORDER BY id DESC";
      $params = array('request_id' => $id);

      $stmt = $this->em->getConnection()->prepare($sql);

      return $this->result($stmt->executeQuery($params)->fetchAllAssociative());
    } catch (\Exception $e) {
      return $this->result(null);
    }
  }


  public function addRequests(Request $request)
  {
    /**  @var \App\Entity\Security\User */
    $user = $this->security->getUser();
    try {

      $userrequest = new ChatRequest();
      $userrequest->setUser($user->getUserIdentifier());
      $userrequest->setClosed(0);
      $userrequest->setCreator($user);
      $userrequest->setTitre($request->request->get("titre"));
      $userrequest->setDescription($request->request->get("description"));
      $this->persist($userrequest);


      $admins = array("<EMAIL>");
      $template = $user->getMetaValueByCode("language", "fr")=="fr"?"new-request":"new-request-en";
      

      try {
        # Instantiate the client.
        $mgClient = Mailgun::create("**************************************************");
        $idSaved = $userrequest->getId();
        $data = array(
          "title" => $request->request->get("titre"),
          "content" => $request->request->get("description"),
        );
        $params = array(
          'from' => "sportaabe " . ' <' . "<EMAIL>" . '>',
          'to' => $admins,
          'subject' => "Nouvelle requête" . " - ID #" . $idSaved,
          'template' => $template,
          'h:X-Mailgun-Variables' =>  \json_encode($data),
        );
        # Make the call to the client.
        $result = $mgClient->messages()->send("mg.afrik.sportaabe.com", $params);
        
      } catch (\Throwable $th) {
        throw $th;
      }
      return $this->result($idSaved);
    } catch (\Exception $e) {
      return $this->result($e->getMessage());
    }
  }


  public function addRequestAttachment(Request $request, string $id)
  {
    $sql = " INSERT INTO attachments (link,request_id) VALUES (:link, :request_id)";

    $params = array(
      'link' => $request->request->get("link"),
      'request_id' => $id,
    );

    $stmt = $this->em->getConnection()->prepare($sql);
    $stmt->executeStatement($params);
    return $this->result($request->request->get("link"));
  }


  public function updateRequests(Request $request, string $id)
  {
    $user = $this->security->getUser();

    try {
      $closed = $request->request->get("closed") == "false" ? 0 : 1;
      $req = $this->requestRepo->find($id);
      if (!$req) {
        return $this->result("No Request found", 404);
      }

      $req->setClosed($closed);
      $req->setClosedBy($user->getDisplayName());
      $this->persist($req);
      return $this->result(true);
    } catch (\Exception $e) {
      return $this->result(null);;
    }
  }

  public function addResponse(Request $request)
  {
    /**  @var \App\Entity\Security\User */
    $user = $this->security->getUser();
    $userrequest = $this->requestRepo->findOneBy(["id" => $request->request->get("requests_id")]);

    if (!$userrequest) {
      return $this->result("No Request found", 404);
    }


    try {

      $userresponse = new ChatResponse();
      $userresponse->setUser($user->getUserIdentifier());
      $userresponse->setMessage($request->request->get("message"));
      $userresponse->setRequest($userrequest);
      $userresponse->setIsAdmin($user->getMetaValueByCode("is_admin"));
      $userresponse->setUserUuid($user->getUuid());
      $userresponse->setUserTimestamp($request->request->get("user_timestamp"));
      $userresponse->setMessageUuid($this->commonService->uuid());
      $userresponse->setUserAvatar($user->getMetaValueByCode('avatar')??'https://ui-avatars.com/api/?background=random&color=fff&length=2&bold=true&name='.$user->getDisplayName());
      $userresponse->setUserName($user->getDisplayName());
      $userresponse->setEditor($user);
      $this->persist($userresponse);

      $userrequest->setUpdatedAt(new \DateTimeImmutable());
      $this->persist($userrequest);

      
      $idSaved = $userresponse->getId();

      $req = $this->getRequest($request, $request->request->get("requests_id"))->getData();
      if (count($req) > 0) {
        $usr =  $this->userRepo->loadUserByIdentifier($req[0]["user"]);
       // dd($usr);
        if ($usr) {
          try {
            if($request->query->get('admin') == 'true')
            {
              $this->firebaseRealTimeDb->getReference('dev/requests/UID-' . $request->request->get("requests_id"))
              ->set([
                'date' => $userresponse->getUserTimestamp(),
                'message' => $userresponse->getMessage(),
                'user' => $user->getUuid(),
                'id' => $userresponse->getMessageUuid(),
                'owner' => $usr->getUuid(),
                'user_name' => $userresponse->getUserName(),
                'user_avatar' => $userresponse->getUserAvatar(),
                'request_id' => $userresponse->getRequest()->getId(),
                'response_id' => $userresponse->getId()

              ]);

            }else{
          
            $this->firebaseRealTimeDb->getReference('dev/requests/UID-' . $request->request->get("requests_id"))
              ->set([
                'date' =>  $request->request->get("user_timestamp"),
                'message' => $request->request->get("message"),
                'user' => $user->getUuid(),
                'id' => $request->request->get("message_uuid"),
                'owner' => $usr->getUuid(),
                'user_name' => $request->request->get("user_name"),
                'user_avatar' => $request->request->get("user_avatar"),
                'request_id' => $request->request->get("requests_id"),
                'response_id' => $userresponse->getId()
              ]);
            }
          } catch (\Throwable $th) {
            $this->logger->error("Firebase error" . $th->getMessage());
            //dd($th);
          }
        }
      }

      /*$admins = array("<EMAIL>");

      $u = $user->getEmail();
      $u1 = $user->getDisplayName();

      if ($request->request->get("admin") == "true") {
        $u = $admins;
        $u1 = "Administrateur";
      }

      try {
        # Instantiate the client.
        $mgClient = Mailgun::create("**************************************************");

        $data = array(
          "name" => $u1,
          "content" => $request->request->get("message"),
        );
        $params = array(
          'from' => "sportaabe " . ' <' . "<EMAIL>" . '>',
          'to' => $u,
          'subject' => "Nouvelle réponse" . " - ID #" . $idSaved,
          'template' => "new-response",
          'h:X-Mailgun-Variables' =>  \json_encode($data),
        );
        # Make the call to the client.
        $mgClient->messages()->send("mg.afrik.sportaabe.com", $params)
      } catch (\Throwable $th) {
        throw $th;
      }*/
      return $this->result($idSaved);
    } catch (\Exception $e) {
      return $this->result($e->getMessage());
    }
  }


  public function getResponsesAttachments(Request $request, string $id)
  {
    try {
      $sql = "SELECT * FROM attachments where response_id = :response_id ORDER BY id DESC";
      $params = array('response_id' => $id);

      $stmt = $this->em->getConnection()->prepare($sql);;
      return $this->result($stmt->executeQuery($params)->fetchAllAssociative());
    } catch (\Exception $e) {
      return $this->result(null);
    }
  }

  public function addResponseAttachment(Request $request, string $id)
  {
    $response = $this->responseRepo->find($id);
    if (!$response) {
      return $this->result(['message' => 'Response not found'], 404, Response::HTTP_NOT_FOUND);
    }
    $attachment = new Attachment(); 
    $attachment->setLink($request->request->get("link"));
    $attachment->setResponse( $response);
    $this->persist($attachment);

    return $this->result($request->request->get("link"));
  }


  



 
}

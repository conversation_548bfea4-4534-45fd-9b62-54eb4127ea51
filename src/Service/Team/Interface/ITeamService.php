<?php

namespace App\Service\Team\Interface;

use App\Dto\GenericResponse;
use Symfony\Component\HttpFoundation\Request;

interface ITeamService
{
    /**
     * Create a new team (company owner only)
     */
    public function createTeam(Request $request): GenericResponse;

    /**
     * Get all teams
     */
    public function getAllTeams(Request $request): GenericResponse;

    /**
     * Get team by ID
     */
    public function getTeam(string $id): GenericResponse;

    /**
     * Update team (company owner only)
     */
    public function updateTeam(Request $request, string $id): GenericResponse;

    /**
     * Delete team (company owner only)
     */
    public function deleteTeam(string $id): GenericResponse;

    /**
     * Get teams by company
     */
    public function getTeamsByCompany(string $companyId): GenericResponse;

    /**
     * Get teams by category
     */
    public function getTeamsByCategory(string $category): GenericResponse;

    /**
     * Get team statistics
     */
    public function getTeamStatistics(string $id): GenericResponse;

    /**
     * Get team players
     */
    public function getTeamPlayers(string $id): GenericResponse;
}

<?php

namespace App\Service\Team\Interface;

use App\Dto\GenericResponse;
use Symfony\Component\HttpFoundation\Request;

interface ITeamCategoryService
{
    /**
     * Create a new team category (Admin only)
     */
    public function createCategory(Request $request): GenericResponse;

    /**
     * Get all team categories
     */
    public function getAllCategories(Request $request): GenericResponse;

    /**
     * Get a specific team category by ID
     */
    public function getCategory(string $id): GenericResponse;

    /**
     * Update a team category (Admin only)
     */
    public function updateCategory(Request $request, string $id): GenericResponse;

    /**
     * Delete a team category (Admin only)
     */
    public function deleteCategory(string $id): GenericResponse;

    /**
     * Get categories by gender
     */
    public function getCategoriesByGender(string $gender): GenericResponse;

    /**
     * Get categories suitable for a specific age
     */
    public function getCategoriesForAge(int $age): GenericResponse;

    /**
     * Get categories with team counts
     */
    public function getCategoriesWithStats(): GenericResponse;

    /**
     * Reorder categories (Admin only)
     */
    public function reorderCategories(Request $request): GenericResponse;
}

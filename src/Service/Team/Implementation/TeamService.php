<?php

namespace App\Service\Team\Implementation;

use App\Dto\GenericResponse;
use App\Dto\ResponseDtoBuilder;
use App\Entity\Team\Team;
use App\Repository\Team\TeamRepository;
use App\Repository\Company\CompanyRepository;
use App\Service\Team\Interface\ITeamService;
use App\Service\Traits\BaseServiceTrait;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\Security;

class TeamService implements ITeamService
{
    use BaseServiceTrait;

    private TeamRepository $teamRepository;
    private CompanyRepository $companyRepository;
    private EntityManagerInterface $entityManager;
    private Security $security;

    public function __construct(
        TeamRepository $teamRepository,
        CompanyRepository $companyRepository,
        EntityManagerInterface $entityManager,
        Security $security
    ) {
        $this->teamRepository = $teamRepository;
        $this->companyRepository = $companyRepository;
        $this->entityManager = $entityManager;
        $this->security = $security;
    }

    public function createTeam(Request $request): GenericResponse
    {
        try {
            $user = $this->security->getUser();
            if (!$user) {
                return ResponseDtoBuilder::buildErrorResponse('Authentication required');
            }

            $data = json_decode($request->getContent(), true);

            // Get company - either from request or user's company
            $company = null;
            if (isset($data['companyId'])) {
                // Admin can specify company
                if (!$this->isAdmin()) {
                    return ResponseDtoBuilder::buildErrorResponse('Access denied. Admin privileges required to specify company.');
                }
                $company = $this->companyRepository->findOneBy(['uuid' => $data['companyId'], 'active' => true]);
            } else {
                // Company owner creates team for their own company
                $company = $user->getCompany();
            }

            if (!$company) {
                return ResponseDtoBuilder::buildErrorResponse('Company not found or user not associated with a company');
            }

            // Check if user is company owner or admin
            if (!$this->isAdmin() && $user->getCompany() !== $company) {
                return ResponseDtoBuilder::buildErrorResponse('Access denied. You can only create teams for your own company.');
            }

            $team = new Team();
            $team->setName($data['name'])
                 ->setCategory($data['category'])
                 ->setCompany($company)
                 ->setActive(true);

            $this->teamRepository->save($team, true);

            return ResponseDtoBuilder::buildSuccessResponse([
                'team' => $this->formatTeamData($team)
            ], 'Team created successfully');

        } catch (\Exception $e) {
            return ResponseDtoBuilder::buildErrorResponse('Error creating team: ' . $e->getMessage());
        }
    }

    public function getAllTeams(Request $request): GenericResponse
    {
        try {
            $category = $request->query->get('category');
            $companyId = $request->query->get('companyId');
            $active = $request->query->getBoolean('active', true);

            $criteria = ['active' => $active];
            if ($category) $criteria['category'] = $category;

            $teams = $this->teamRepository->findBy($criteria, ['name' => 'ASC']);

            // Filter by company if specified
            if ($companyId) {
                $teams = array_filter($teams, function($team) use ($companyId) {
                    return $team->getCompany() && $team->getCompany()->getUuid() === $companyId;
                });
            }

            $formattedTeams = array_map(
                fn($team) => $this->formatTeamData($team),
                $teams
            );

            return ResponseDtoBuilder::buildSuccessResponse([
                'teams' => $formattedTeams,
                'total' => count($formattedTeams)
            ]);

        } catch (\Exception $e) {
            return ResponseDtoBuilder::buildErrorResponse('Error fetching teams: ' . $e->getMessage());
        }
    }

    public function getTeam(string $id): GenericResponse
    {
        try {
            $team = $this->teamRepository->findOneBy(['uuid' => $id, 'active' => true]);

            if (!$team) {
                return ResponseDtoBuilder::buildErrorResponse('Team not found');
            }

            return ResponseDtoBuilder::buildSuccessResponse([
                'team' => $this->formatTeamData($team, true)
            ]);

        } catch (\Exception $e) {
            return ResponseDtoBuilder::buildErrorResponse('Error fetching team: ' . $e->getMessage());
        }
    }

    public function updateTeam(Request $request, string $id): GenericResponse
    {
        try {
            $user = $this->security->getUser();
            if (!$user) {
                return ResponseDtoBuilder::buildErrorResponse('Authentication required');
            }

            $team = $this->teamRepository->findOneBy(['uuid' => $id, 'active' => true]);

            if (!$team) {
                return ResponseDtoBuilder::buildErrorResponse('Team not found');
            }

            // Check if user is company owner or admin
            if (!$this->isAdmin() && $user->getCompany() !== $team->getCompany()) {
                return ResponseDtoBuilder::buildErrorResponse('Access denied. You can only update teams from your own company.');
            }

            $data = json_decode($request->getContent(), true);

            if (isset($data['name'])) $team->setName($data['name']);
            if (isset($data['category'])) $team->setCategory($data['category']);

            $this->teamRepository->save($team, true);

            return ResponseDtoBuilder::buildSuccessResponse([
                'team' => $this->formatTeamData($team)
            ], 'Team updated successfully');

        } catch (\Exception $e) {
            return ResponseDtoBuilder::buildErrorResponse('Error updating team: ' . $e->getMessage());
        }
    }

    public function deleteTeam(string $id): GenericResponse
    {
        try {
            $user = $this->security->getUser();
            if (!$user) {
                return ResponseDtoBuilder::buildErrorResponse('Authentication required');
            }

            $team = $this->teamRepository->findOneBy(['uuid' => $id, 'active' => true]);

            if (!$team) {
                return ResponseDtoBuilder::buildErrorResponse('Team not found');
            }

            // Check if user is company owner or admin
            if (!$this->isAdmin() && $user->getCompany() !== $team->getCompany()) {
                return ResponseDtoBuilder::buildErrorResponse('Access denied. You can only delete teams from your own company.');
            }

            $team->setActive(false);
            $this->teamRepository->save($team, true);

            return ResponseDtoBuilder::buildSuccessResponse([], 'Team deleted successfully');

        } catch (\Exception $e) {
            return ResponseDtoBuilder::buildErrorResponse('Error deleting team: ' . $e->getMessage());
        }
    }

    public function getTeamsByCompany(string $companyId): GenericResponse
    {
        try {
            $company = $this->companyRepository->findOneBy(['uuid' => $companyId, 'active' => true]);

            if (!$company) {
                return ResponseDtoBuilder::buildErrorResponse('Company not found');
            }

            $teams = $this->teamRepository->findBy(['company' => $company, 'active' => true], ['name' => 'ASC']);

            $formattedTeams = array_map(
                fn($team) => $this->formatTeamData($team),
                $teams
            );

            return ResponseDtoBuilder::buildSuccessResponse([
                'teams' => $formattedTeams,
                'total' => count($formattedTeams)
            ]);

        } catch (\Exception $e) {
            return ResponseDtoBuilder::buildErrorResponse('Error fetching teams: ' . $e->getMessage());
        }
    }

    public function getTeamsByCategory(string $category): GenericResponse
    {
        try {
            $teams = $this->teamRepository->findBy(['category' => $category, 'active' => true], ['name' => 'ASC']);

            $formattedTeams = array_map(
                fn($team) => $this->formatTeamData($team),
                $teams
            );

            return ResponseDtoBuilder::buildSuccessResponse([
                'teams' => $formattedTeams,
                'total' => count($formattedTeams)
            ]);

        } catch (\Exception $e) {
            return ResponseDtoBuilder::buildErrorResponse('Error fetching teams: ' . $e->getMessage());
        }
    }

    public function getTeamStatistics(string $id): GenericResponse
    {
        try {
            $team = $this->teamRepository->findOneBy(['uuid' => $id, 'active' => true]);

            if (!$team) {
                return ResponseDtoBuilder::buildErrorResponse('Team not found');
            }

            // Calculate team statistics
            $players = $team->getPlayers();
            $totalGoals = 0;
            $totalYellowCards = 0;
            $totalRedCards = 0;
            $totalAssists = 0;
            $totalMatches = 0;

            foreach ($players as $player) {
                $totalGoals += $player->getTotalGoals() ?? 0;
                $totalYellowCards += $player->getTotalYellowCards() ?? 0;
                $totalRedCards += $player->getTotalRedCards() ?? 0;
                $totalAssists += $player->getTotalAssists() ?? 0;
                $totalMatches += $player->getTotalMatches() ?? 0;
            }

            $statistics = [
                'totalPlayers' => $players->count(),
                'totalGoals' => $totalGoals,
                'totalYellowCards' => $totalYellowCards,
                'totalRedCards' => $totalRedCards,
                'totalAssists' => $totalAssists,
                'totalMatches' => $totalMatches,
                'averageGoalsPerPlayer' => $players->count() > 0 ? round($totalGoals / $players->count(), 2) : 0,
            ];

            return ResponseDtoBuilder::buildSuccessResponse([
                'team' => $this->formatTeamData($team),
                'statistics' => $statistics
            ]);

        } catch (\Exception $e) {
            return ResponseDtoBuilder::buildErrorResponse('Error fetching team statistics: ' . $e->getMessage());
        }
    }

    public function getTeamPlayers(string $id): GenericResponse
    {
        try {
            $team = $this->teamRepository->findOneBy(['uuid' => $id, 'active' => true]);

            if (!$team) {
                return ResponseDtoBuilder::buildErrorResponse('Team not found');
            }

            $players = $team->getPlayers()->filter(fn($player) => $player->isActive());

            $formattedPlayers = $players->map(function($player) {
                return [
                    'id' => $player->getId(),
                    'uuid' => $player->getUuid(),
                    'firstName' => $player->getFirstName(),
                    'lastName' => $player->getLastName(),
                    'position' => $player->getPosition(),
                    'jerseyNumber' => $player->getJerseyNumber(),
                    'totalGoals' => $player->getTotalGoals(),
                    'totalYellowCards' => $player->getTotalYellowCards(),
                    'totalRedCards' => $player->getTotalRedCards(),
                    'totalAssists' => $player->getTotalAssists(),
                    'totalMatches' => $player->getTotalMatches(),
                ];
            })->toArray();

            return ResponseDtoBuilder::buildSuccessResponse([
                'team' => $this->formatTeamData($team),
                'players' => $formattedPlayers,
                'total' => count($formattedPlayers)
            ]);

        } catch (\Exception $e) {
            return ResponseDtoBuilder::buildErrorResponse('Error fetching team players: ' . $e->getMessage());
        }
    }

    private function formatTeamData(Team $team, bool $detailed = false): array
    {
        $data = [
            'id' => $team->getId(),
            'uuid' => $team->getUuid(),
            'name' => $team->getName(),
            'category' => $team->getCategory(),
            'company' => [
                'id' => $team->getCompany()->getId(),
                'uuid' => $team->getCompany()->getUuid(),
                'name' => $team->getCompany()->getName(),
            ],
            'playersCount' => $team->getPlayers()->count(),
            'createdAt' => $team->getCreatedAt()?->format('Y-m-d H:i:s'),
        ];

        if ($detailed) {
            $data['players'] = $team->getPlayers()->map(function($player) {
                return [
                    'id' => $player->getId(),
                    'uuid' => $player->getUuid(),
                    'firstName' => $player->getFirstName(),
                    'lastName' => $player->getLastName(),
                    'position' => $player->getPosition(),
                    'jerseyNumber' => $player->getJerseyNumber(),
                ];
            })->toArray();
        }

        return $data;
    }

    private function isAdmin(): bool
    {
        $user = $this->security->getUser();
        if (!$user) return false;

        // Check if user has admin meta
        foreach ($user->getMetas() as $meta) {
            if ($meta->getCode() === 'is_admin' && $meta->getValue() === '1') {
                return true;
            }
            if ($meta->getCode() === 'is_super_admin' && $meta->getValue() === '1') {
                return true;
            }
        }

        return false;
    }
}

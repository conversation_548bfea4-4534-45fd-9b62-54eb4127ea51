<?php

namespace App\Service\Team\Implementation;

use App\Dto\GenericResponse;
use App\Dto\GenericResponseMessage;
use App\Constant\GenericResponseStatuses;
use App\Constant\GenericResponseMessageTypes;
use App\Entity\Team\TeamCategory;
use App\Repository\Team\TeamCategoryRepository;
use App\Service\Team\Interface\ITeamCategoryService;
use App\Service\Traits\BaseServiceTrait;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Core\Security;

class TeamCategoryService implements ITeamCategoryService
{
    use BaseServiceTrait;

    private TeamCategoryRepository $categoryRepository;
    private Security $security;

    public function __construct(
        TeamCategoryRepository $categoryRepository,
        Security $security,
        EntityManagerInterface $em
    ) {
        $this->categoryRepository = $categoryRepository;
        $this->security = $security;
        $this->em = $em;
    }

    public function createCategory(Request $request): GenericResponse
    {
        try {
            // Check if user is admin
            if (!$this->isAdmin()) {
                $message = new GenericResponseMessage('Access denied. Admin privileges required.', 'access_denied', GenericResponseMessageTypes::ERROR);
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_FORBIDDEN, [$message]);
            }

            $data = json_decode($request->getContent(), true);

            // Validate required fields
            if (empty($data['name']) || empty($data['code'])) {
                $message = new GenericResponseMessage('Name and code are required fields.', 'validation_error', GenericResponseMessageTypes::ERROR);
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_BAD_REQUEST, [$message]);
            }

            // Check if code is unique
            if (!$this->categoryRepository->isCodeUnique($data['code'])) {
                $message = new GenericResponseMessage('Category code already exists.', 'code_duplicate', GenericResponseMessageTypes::ERROR);
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_CONFLICT, [$message]);
            }

            // Check if name is unique
            if (!$this->categoryRepository->isNameUnique($data['name'])) {
                $message = new GenericResponseMessage('Category name already exists.', 'name_duplicate', GenericResponseMessageTypes::ERROR);
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_CONFLICT, [$message]);
            }

            $category = new TeamCategory();
            $category->setName($data['name'])
                    ->setCode($data['code'])
                    ->setDescription($data['description'] ?? '')
                    ->setMinAge($data['minAge'] ?? null)
                    ->setMaxAge($data['maxAge'] ?? null)
                    ->setMinPlayersPerTeam($data['minPlayersPerTeam'] ?? null)
                    ->setMaxPlayersPerTeam($data['maxPlayersPerTeam'] ?? null)
                    ->setGender($data['gender'] ?? null)
                    ->setRules($data['rules'] ?? null)
                    ->setSortOrder($data['sortOrder'] ?? $this->categoryRepository->getNextSortOrder())
                    ->setActive(true);

            $this->categoryRepository->save($category, true);

            $message = new GenericResponseMessage('Team category created successfully', 'category_created', GenericResponseMessageTypes::SUCCESS);
            return $this->result([
                'category' => $this->formatCategoryData($category)
            ], GenericResponseStatuses::SUCCESS, Response::HTTP_CREATED, [$message]);

        } catch (\Exception $e) {
            $message = new GenericResponseMessage('Error creating team category: ' . $e->getMessage(), 'category_creation_error', GenericResponseMessageTypes::ERROR);
            return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_INTERNAL_SERVER_ERROR, [$message]);
        }
    }

    public function getAllCategories(Request $request): GenericResponse
    {
        try {
            $includeStats = $request->query->getBoolean('includeStats', false);
            $gender = $request->query->get('gender');
            $active = $request->query->getBoolean('active', true);

            if ($includeStats) {
                $categories = $this->categoryRepository->getCategoriesWithTeamCounts();
            } elseif ($gender) {
                $categories = $this->categoryRepository->getCategoriesByGender($gender);
            } else {
                $criteria = ['active' => $active];
                $categories = $this->categoryRepository->findBy($criteria, ['sortOrder' => 'ASC', 'name' => 'ASC']);
            }

            $formattedCategories = array_map(
                fn($category) => $this->formatCategoryData($category, $includeStats),
                $categories
            );

            return $this->result([
                'categories' => $formattedCategories,
                'total' => count($formattedCategories)
            ]);

        } catch (\Exception $e) {
            $message = new GenericResponseMessage('Error fetching team categories: ' . $e->getMessage(), 'categories_fetch_error', GenericResponseMessageTypes::ERROR);
            return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_INTERNAL_SERVER_ERROR, [$message]);
        }
    }

    public function getCategory(string $id): GenericResponse
    {
        try {
            $category = $this->categoryRepository->getByUuid($id);

            if (!$category) {
                $message = new GenericResponseMessage('Team category not found', 'category_not_found', GenericResponseMessageTypes::ERROR);
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_NOT_FOUND, [$message]);
            }

            return $this->result([
                'category' => $this->formatCategoryData($category, true)
            ]);

        } catch (\Exception $e) {
            $message = new GenericResponseMessage('Error fetching team category: ' . $e->getMessage(), 'category_fetch_error', GenericResponseMessageTypes::ERROR);
            return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_INTERNAL_SERVER_ERROR, [$message]);
        }
    }

    public function updateCategory(Request $request, string $id): GenericResponse
    {
        try {
            // Check if user is admin
            if (!$this->isAdmin()) {
                $message = new GenericResponseMessage('Access denied. Admin privileges required.', 'access_denied', GenericResponseMessageTypes::ERROR);
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_FORBIDDEN, [$message]);
            }

            $category = $this->categoryRepository->getByUuid($id);

            if (!$category) {
                $message = new GenericResponseMessage('Team category not found', 'category_not_found', GenericResponseMessageTypes::ERROR);
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_NOT_FOUND, [$message]);
            }

            $data = json_decode($request->getContent(), true);

            // Check if code is unique (excluding current category)
            if (isset($data['code']) && !$this->categoryRepository->isCodeUnique($data['code'], $category->getId())) {
                $message = new GenericResponseMessage('Category code already exists.', 'code_duplicate', GenericResponseMessageTypes::ERROR);
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_CONFLICT, [$message]);
            }

            // Check if name is unique (excluding current category)
            if (isset($data['name']) && !$this->categoryRepository->isNameUnique($data['name'], $category->getId())) {
                $message = new GenericResponseMessage('Category name already exists.', 'name_duplicate', GenericResponseMessageTypes::ERROR);
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_CONFLICT, [$message]);
            }

            // Update fields
            if (isset($data['name'])) $category->setName($data['name']);
            if (isset($data['code'])) $category->setCode($data['code']);
            if (isset($data['description'])) $category->setDescription($data['description']);
            if (isset($data['minAge'])) $category->setMinAge($data['minAge']);
            if (isset($data['maxAge'])) $category->setMaxAge($data['maxAge']);
            if (isset($data['minPlayersPerTeam'])) $category->setMinPlayersPerTeam($data['minPlayersPerTeam']);
            if (isset($data['maxPlayersPerTeam'])) $category->setMaxPlayersPerTeam($data['maxPlayersPerTeam']);
            if (isset($data['gender'])) $category->setGender($data['gender']);
            if (isset($data['rules'])) $category->setRules($data['rules']);
            if (isset($data['sortOrder'])) $category->setSortOrder($data['sortOrder']);

            $this->categoryRepository->save($category, true);

            $message = new GenericResponseMessage('Team category updated successfully', 'category_updated', GenericResponseMessageTypes::SUCCESS);
            return $this->result([
                'category' => $this->formatCategoryData($category)
            ], GenericResponseStatuses::SUCCESS, Response::HTTP_OK, [$message]);

        } catch (\Exception $e) {
            $message = new GenericResponseMessage('Error updating team category: ' . $e->getMessage(), 'category_update_error', GenericResponseMessageTypes::ERROR);
            return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_INTERNAL_SERVER_ERROR, [$message]);
        }
    }

    public function deleteCategory(string $id): GenericResponse
    {
        try {
            // Check if user is admin
            if (!$this->isAdmin()) {
                $message = new GenericResponseMessage('Access denied. Admin privileges required.', 'access_denied', GenericResponseMessageTypes::ERROR);
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_FORBIDDEN, [$message]);
            }

            $category = $this->categoryRepository->getByUuid($id);

            if (!$category) {
                $message = new GenericResponseMessage('Team category not found', 'category_not_found', GenericResponseMessageTypes::ERROR);
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_NOT_FOUND, [$message]);
            }

            // Check if category has teams
            if ($category->getTeams()->count() > 0) {
                $message = new GenericResponseMessage('Cannot delete category that has teams assigned to it.', 'category_has_teams', GenericResponseMessageTypes::ERROR);
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_CONFLICT, [$message]);
            }

            $category->setActive(false);
            $this->categoryRepository->save($category, true);

            $message = new GenericResponseMessage('Team category deleted successfully', 'category_deleted', GenericResponseMessageTypes::SUCCESS);
            return $this->result([], GenericResponseStatuses::SUCCESS, Response::HTTP_OK, [$message]);

        } catch (\Exception $e) {
            $message = new GenericResponseMessage('Error deleting team category: ' . $e->getMessage(), 'category_delete_error', GenericResponseMessageTypes::ERROR);
            return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_INTERNAL_SERVER_ERROR, [$message]);
        }
    }

    public function getCategoriesByGender(string $gender): GenericResponse
    {
        try {
            $categories = $this->categoryRepository->getCategoriesByGender($gender);

            $formattedCategories = array_map(
                fn($category) => $this->formatCategoryData($category),
                $categories
            );

            return $this->result([
                'categories' => $formattedCategories,
                'total' => count($formattedCategories)
            ]);

        } catch (\Exception $e) {
            $message = new GenericResponseMessage('Error fetching categories by gender: ' . $e->getMessage(), 'categories_fetch_error', GenericResponseMessageTypes::ERROR);
            return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_INTERNAL_SERVER_ERROR, [$message]);
        }
    }

    public function getCategoriesForAge(int $age): GenericResponse
    {
        try {
            $categories = $this->categoryRepository->getCategoriesForAge($age);

            $formattedCategories = array_map(
                fn($category) => $this->formatCategoryData($category),
                $categories
            );

            return $this->result([
                'categories' => $formattedCategories,
                'total' => count($formattedCategories)
            ]);

        } catch (\Exception $e) {
            $message = new GenericResponseMessage('Error fetching categories for age: ' . $e->getMessage(), 'categories_fetch_error', GenericResponseMessageTypes::ERROR);
            return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_INTERNAL_SERVER_ERROR, [$message]);
        }
    }

    public function getCategoriesWithStats(): GenericResponse
    {
        try {
            $categories = $this->categoryRepository->getCategoriesWithTeamCounts();

            $formattedCategories = array_map(
                fn($category) => $this->formatCategoryData($category, true),
                $categories
            );

            return $this->result([
                'categories' => $formattedCategories,
                'total' => count($formattedCategories)
            ]);

        } catch (\Exception $e) {
            $message = new GenericResponseMessage('Error fetching categories with stats: ' . $e->getMessage(), 'categories_fetch_error', GenericResponseMessageTypes::ERROR);
            return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_INTERNAL_SERVER_ERROR, [$message]);
        }
    }

    public function reorderCategories(Request $request): GenericResponse
    {
        try {
            // Check if user is admin
            if (!$this->isAdmin()) {
                $message = new GenericResponseMessage('Access denied. Admin privileges required.', 'access_denied', GenericResponseMessageTypes::ERROR);
                return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_FORBIDDEN, [$message]);
            }

            $data = json_decode($request->getContent(), true);
            $categoryOrders = $data['categories'] ?? [];

            foreach ($categoryOrders as $orderData) {
                $category = $this->categoryRepository->getByUuid($orderData['uuid']);
                if ($category) {
                    $category->setSortOrder($orderData['sortOrder']);
                    $this->categoryRepository->save($category, false);
                }
            }

            $this->em->flush();

            $message = new GenericResponseMessage('Categories reordered successfully', 'categories_reordered', GenericResponseMessageTypes::SUCCESS);
            return $this->result([], GenericResponseStatuses::SUCCESS, Response::HTTP_OK, [$message]);

        } catch (\Exception $e) {
            $message = new GenericResponseMessage('Error reordering categories: ' . $e->getMessage(), 'categories_reorder_error', GenericResponseMessageTypes::ERROR);
            return $this->result(null, GenericResponseStatuses::FAILED, Response::HTTP_INTERNAL_SERVER_ERROR, [$message]);
        }
    }

    private function formatCategoryData(TeamCategory $category, bool $includeStats = false): array
    {
        $data = [
            'id' => $category->getId(),
            'uuid' => $category->getUuid(),
            'name' => $category->getName(),
            'code' => $category->getCode(),
            'description' => $category->getDescription(),
            'minAge' => $category->getMinAge(),
            'maxAge' => $category->getMaxAge(),
            'minPlayersPerTeam' => $category->getMinPlayersPerTeam(),
            'maxPlayersPerTeam' => $category->getMaxPlayersPerTeam(),
            'gender' => $category->getGender(),
            'rules' => $category->getRules(),
            'sortOrder' => $category->getSortOrder(),
            'ageRange' => $category->getAgeRange(),
            'playerRange' => $category->getPlayerRange(),
            'active' => $category->isActive(),
            'createdAt' => $category->getCreatedAt()?->format('Y-m-d H:i:s'),
            'updatedAt' => $category->getUpdatedAt()?->format('Y-m-d H:i:s'),
        ];

        if ($includeStats) {
            $data['teamsCount'] = $category->teamCount ?? $category->getTeams()->count();
        }

        return $data;
    }

    private function isAdmin(): bool
    {
        $user = $this->security->getUser();
        if (!$user) return false;

        // Check if user has admin meta
        foreach ($user->getMetas() as $meta) {
            if ($meta->getCode() === 'is_admin' && $meta->getValue() === '1') {
                return true;
            }
            if ($meta->getCode() === 'is_super_admin' && $meta->getValue() === '1') {
                return true;
            }
        }

        return false;
    }
}

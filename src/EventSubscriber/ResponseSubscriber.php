<?php

namespace App\EventSubscriber;

use App\Dto\GenericResponse;
use App\Service\Traits\LogServiceTrait;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\ResponseEvent;
use Symfony\Component\HttpKernel\KernelEvents;

class ResponseSubscriber implements EventSubscriberInterface
{
  use LogServiceTrait;

  public function onKernelResponse(ResponseEvent $event)
  {
    if (!$event->isMainRequest()) {
      return;
    }

    $content  = \json_decode($event->getResponse()->getContent(), true);
    if (!empty($content) && !array_key_exists("data", $content)) {
      $genericResponse = GenericResponse::formatResponse($content);
      try {
        $event->getResponse()->setContent(\json_encode($genericResponse->toArray()));
      } catch (\Throwable $th) {
        $event->getResponse()->setContent(\json_encode($content));
      }
    }
  }

  public static function getSubscribedEvents()
  {
    return array(
      KernelEvents::RESPONSE => [['onKernelResponse', 0]]
    );
  }
}

<?php

namespace App\EventSubscriber;

use App\Constant\GenericResponseMessageKeys;
use App\Constant\GenericResponseMessages;
use App\Constant\GenericResponseMessageTypes;
use App\Constant\GenericResponseStatuses;
use App\Dto\GenericResponse;
use App\Dto\GenericResponseMessage;
use App\Exception\DeleteConflictException;
use App\Exception\EntityNotFoundException;
use App\Exception\RequestValidationException;
use App\Exception\UnprocessableRequestException;
use App\Service\Traits\LogServiceTrait;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\ExceptionEvent;
use Symfony\Component\HttpKernel\KernelEvents;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Form\Form;

class GlobalExceptionSubscriber implements EventSubscriberInterface
{
  use LogServiceTrait;

  private function getFormErrorMessages(Form $form)
  {
    $errors = [];
    foreach ($form->getErrors() as $error) {
      $errors[] = new GenericResponseMessage($error->getMessage(), $form->getName(), GenericResponseMessageTypes::ERROR);
    }
    foreach ($form->all() as $child) {
      foreach ($child->getErrors() as $error) {
        $errors[] = new GenericResponseMessage($error->getMessage(), $child->getName(), GenericResponseMessageTypes::ERROR);
      }
    }
    return $errors;
  }

  public function onKernelException(ExceptionEvent $event)
  {
    $response = null;
    $error = null;
    $code = 500;
    $e = $event->getThrowable();

    if ($e instanceof RequestValidationException) {
      $code = 400;
      $data = $e->getData();
      if ($data instanceof Form) {
        $error = $this->getFormErrorMessages($data);
      } else if ($data instanceof GenericResponseMessage) {
        $error = $data;
      } else if (is_string($data)) {
        $error = new GenericResponseMessage($data, GenericResponseMessageKeys::REQUEST_VALIDATION_ERROR, GenericResponseMessageTypes::ERROR);
      } else {
        $error = new GenericResponseMessage(GenericResponseMessages::INVALID_REQUEST_DATA, GenericResponseMessageKeys::FORM_VALIDATION_ERROR, GenericResponseMessageTypes::ERROR);
      }
    } else if ($e instanceof UnprocessableRequestException) {
      $code = 422;
      $error = $e->getData();
    } else if ($e instanceof DeleteConflictException) {
      $code = 409;
      $error = $e->getData();
    } else if ($e instanceof EntityNotFoundException) {
      $code = 404;
      $error = $e->getData();
    } else {
      $error = new GenericResponseMessage($e->getMessage(), GenericResponseMessageKeys::SERVER_ERROR, GenericResponseMessageTypes::ERROR);
    }
    if (!empty($error)) {
      $response = (new GenericResponse(null, GenericResponseStatuses::FAILED, $code, !is_array($error) ? [$error] : $error))->toArray();
      $response = new JsonResponse($response, $code);
      $response->headers->set('Content-Type', 'application/problem+json');
      $event->setResponse($response);
    }
  }

  public static function getSubscribedEvents()
  {
    return array(
      KernelEvents::EXCEPTION => [['onKernelException', 0]]
    );
  }
}

<?php

namespace App\EventSubscriber;

use App\Service\Traits\LogServiceTrait;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use App\Service\Security\Interface\IUserService;
use Symfony\Component\Security\Http\Event\LoginSuccessEvent;

class LoginEventsSubscriber implements EventSubscriberInterface
{
  use LogServiceTrait;

  public IUserService $userService;

  public function __construct(IUserService $service)
  {
    $this->userService = $service;
  }

  public function onLoginSuccess(LoginSuccessEvent $event)
  {
    if ($event->getRequest()->get('_route') === "api_login_check") {
      $this->userService->resetUserToken($event->getAuthenticatedToken()->getUserIdentifier());
    }
  }

  public static function getSubscribedEvents()
  {
    return array(
      LoginSuccessEvent::class => [['onLoginSuccess', 128]]
    );
  }
}

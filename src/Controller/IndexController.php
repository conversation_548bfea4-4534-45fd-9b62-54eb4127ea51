<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class IndexController extends AbstractController
{

  #[Route("/", name: "home")]
  public function home(): Response
  {
    return $this->render('base.html.twig');
  }


  #[Route("/{reactRouting}", name: "app", requirements: ["reactRouting" => "^(?!api).+"], defaults: ["reactRouting" => null])]
  public function app(): Response
  {
    return $this->render('base.html.twig');
  }
}

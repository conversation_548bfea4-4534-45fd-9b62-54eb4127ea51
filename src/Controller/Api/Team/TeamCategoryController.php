<?php

namespace App\Controller\Api\Team;

use App\Controller\Api\Shared\AbstractBaseApiController;
use App\Service\Team\Implementation\TeamCategoryService;
use FOS\RestBundle\Controller\Annotations as Rest;
use OpenApi\Attributes as OA;
use Symfony\Component\HttpFoundation\Request;

#[OA\Tag(name: "team-categories")]
class TeamCategoryController extends AbstractBaseApiController
{
    private TeamCategoryService $service;

    public function __construct(TeamCategoryService $service)
    {
        $this->service = $service;
    }

    /**
     * Create a new team category (Admin only)
     */
    #[Rest\Post("/team-categories/create", name: "api_team_categories_create")]
    #[OA\Post(
        path: "/api/v2/team-categories/create",
        summary: "Create a new team category",
        description: "Create a new team category. Admin privileges required.",
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(
                type: "object",
                required: ["name", "code"],
                properties: [
                    new OA\Property(property: "name", type: "string", example: "Senior Men"),
                    new OA\Property(property: "code", type: "string", example: "SENIOR_MEN"),
                    new OA\Property(property: "description", type: "string", example: "Senior men's category"),
                    new OA\Property(property: "minAge", type: "integer", example: 18),
                    new OA\Property(property: "maxAge", type: "integer", example: 35),
                    new OA\Property(property: "minPlayersPerTeam", type: "integer", example: 11),
                    new OA\Property(property: "maxPlayersPerTeam", type: "integer", example: 25),
                    new OA\Property(property: "gender", type: "string", enum: ["MALE", "FEMALE", "MIXED"], example: "MALE"),
                    new OA\Property(property: "rules", type: "string", example: "Standard FIFA rules apply"),
                    new OA\Property(property: "sortOrder", type: "integer", example: 10)
                ]
            )
        ),
        responses: [
            new OA\Response(
                response: 201,
                description: "Team category created successfully"
            ),
            new OA\Response(
                response: 400,
                description: "Validation error"
            ),
            new OA\Response(
                response: 403,
                description: "Access denied"
            ),
            new OA\Response(
                response: 409,
                description: "Category code or name already exists"
            )
        ]
    )]
    public function createCategory(Request $request)
    {
        return $this->result($this->service->createCategory($request));
    }

    /**
     * Get all team categories
     */
    #[Rest\Get("/team-categories/list", name: "api_team_categories_list")]
    #[OA\Get(
        path: "/api/v2/team-categories/list",
        summary: "Get all team categories",
        description: "Retrieve all team categories with optional filtering",
        parameters: [
            new OA\Parameter(
                name: "includeStats",
                in: "query",
                description: "Include team counts for each category",
                schema: new OA\Schema(type: "boolean", default: false)
            ),
            new OA\Parameter(
                name: "gender",
                in: "query",
                description: "Filter by gender",
                schema: new OA\Schema(type: "string", enum: ["MALE", "FEMALE", "MIXED"])
            ),
            new OA\Parameter(
                name: "active",
                in: "query",
                description: "Filter by active status",
                schema: new OA\Schema(type: "boolean", default: true)
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: "List of team categories"
            )
        ]
    )]
    public function getAllCategories(Request $request)
    {
        return $this->result($this->service->getAllCategories($request));
    }

    /**
     * Get a specific team category
     */
    #[Rest\Get("/team-categories/show/{id}", name: "api_team_categories_show")]
    #[OA\Get(
        path: "/api/v2/team-categories/show/{id}",
        summary: "Get team category details",
        description: "Retrieve detailed information about a specific team category",
        parameters: [
            new OA\Parameter(
                name: "id",
                in: "path",
                description: "Team category UUID",
                required: true,
                schema: new OA\Schema(type: "string")
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: "Team category details"
            ),
            new OA\Response(
                response: 404,
                description: "Team category not found"
            )
        ]
    )]
    public function getCategory(string $id)
    {
        return $this->result($this->service->getCategory($id));
    }

    /**
     * Update a team category (Admin only)
     */
    #[Rest\Put("/team-categories/update/{id}", name: "api_team_categories_update")]
    #[OA\Put(
        path: "/api/v2/team-categories/update/{id}",
        summary: "Update a team category",
        description: "Update an existing team category. Admin privileges required.",
        parameters: [
            new OA\Parameter(
                name: "id",
                in: "path",
                description: "Team category UUID",
                required: true,
                schema: new OA\Schema(type: "string")
            )
        ],
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(
                type: "object",
                properties: [
                    new OA\Property(property: "name", type: "string"),
                    new OA\Property(property: "code", type: "string"),
                    new OA\Property(property: "description", type: "string"),
                    new OA\Property(property: "minAge", type: "integer"),
                    new OA\Property(property: "maxAge", type: "integer"),
                    new OA\Property(property: "minPlayersPerTeam", type: "integer"),
                    new OA\Property(property: "maxPlayersPerTeam", type: "integer"),
                    new OA\Property(property: "gender", type: "string", enum: ["MALE", "FEMALE", "MIXED"]),
                    new OA\Property(property: "rules", type: "string"),
                    new OA\Property(property: "sortOrder", type: "integer")
                ]
            )
        ),
        responses: [
            new OA\Response(
                response: 200,
                description: "Team category updated successfully"
            ),
            new OA\Response(
                response: 403,
                description: "Access denied"
            ),
            new OA\Response(
                response: 404,
                description: "Team category not found"
            ),
            new OA\Response(
                response: 409,
                description: "Category code or name already exists"
            )
        ]
    )]
    public function updateCategory(Request $request, string $id)
    {
        return $this->result($this->service->updateCategory($request, $id));
    }

    /**
     * Delete a team category (Admin only)
     */
    #[Rest\Delete("/team-categories/delete/{id}", name: "api_team_categories_delete")]
    #[OA\Delete(
        path: "/api/v2/team-categories/delete/{id}",
        summary: "Delete a team category",
        description: "Delete a team category. Admin privileges required. Cannot delete categories with teams.",
        parameters: [
            new OA\Parameter(
                name: "id",
                in: "path",
                description: "Team category UUID",
                required: true,
                schema: new OA\Schema(type: "string")
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: "Team category deleted successfully"
            ),
            new OA\Response(
                response: 403,
                description: "Access denied"
            ),
            new OA\Response(
                response: 404,
                description: "Team category not found"
            ),
            new OA\Response(
                response: 409,
                description: "Cannot delete category with teams"
            )
        ]
    )]
    public function deleteCategory(string $id)
    {
        return $this->result($this->service->deleteCategory($id));
    }

    /**
     * Get categories by gender
     */
    #[Rest\Get("/team-categories/gender/{gender}", name: "api_team_categories_by_gender")]
    #[OA\Get(
        path: "/api/v2/team-categories/gender/{gender}",
        summary: "Get categories by gender",
        description: "Retrieve team categories filtered by gender",
        parameters: [
            new OA\Parameter(
                name: "gender",
                in: "path",
                description: "Gender filter",
                required: true,
                schema: new OA\Schema(type: "string", enum: ["MALE", "FEMALE", "MIXED"])
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: "List of categories by gender"
            )
        ]
    )]
    public function getCategoriesByGender(string $gender)
    {
        return $this->result($this->service->getCategoriesByGender($gender));
    }

    /**
     * Get categories for specific age
     */
    #[Rest\Get("/team-categories/age/{age}", name: "api_team_categories_by_age")]
    #[OA\Get(
        path: "/api/v2/team-categories/age/{age}",
        summary: "Get categories for specific age",
        description: "Retrieve team categories suitable for a specific age",
        parameters: [
            new OA\Parameter(
                name: "age",
                in: "path",
                description: "Age to filter by",
                required: true,
                schema: new OA\Schema(type: "integer")
            )
        ],
        responses: [
            new OA\Response(
                response: 200,
                description: "List of categories for the age"
            )
        ]
    )]
    public function getCategoriesForAge(int $age)
    {
        return $this->result($this->service->getCategoriesForAge($age));
    }

    /**
     * Get categories with statistics
     */
    #[Rest\Get("/team-categories/stats", name: "api_team_categories_stats")]
    #[OA\Get(
        path: "/api/v2/team-categories/stats",
        summary: "Get categories with statistics",
        description: "Retrieve team categories with team counts and statistics",
        responses: [
            new OA\Response(
                response: 200,
                description: "List of categories with statistics"
            )
        ]
    )]
    public function getCategoriesWithStats()
    {
        return $this->result($this->service->getCategoriesWithStats());
    }

    /**
     * Reorder categories (Admin only)
     */
    #[Rest\Post("/team-categories/reorder", name: "api_team_categories_reorder")]
    #[OA\Post(
        path: "/api/v2/team-categories/reorder",
        summary: "Reorder team categories",
        description: "Update the sort order of team categories. Admin privileges required.",
        requestBody: new OA\RequestBody(
            required: true,
            content: new OA\JsonContent(
                type: "object",
                required: ["categories"],
                properties: [
                    new OA\Property(
                        property: "categories",
                        type: "array",
                        items: new OA\Items(
                            type: "object",
                            properties: [
                                new OA\Property(property: "uuid", type: "string"),
                                new OA\Property(property: "sortOrder", type: "integer")
                            ]
                        )
                    )
                ]
            )
        ),
        responses: [
            new OA\Response(
                response: 200,
                description: "Categories reordered successfully"
            ),
            new OA\Response(
                response: 403,
                description: "Access denied"
            )
        ]
    )]
    public function reorderCategories(Request $request)
    {
        return $this->result($this->service->reorderCategories($request));
    }
}

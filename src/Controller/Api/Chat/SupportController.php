<?php

namespace App\Controller\Api\Chat;

use OpenApi\Attributes as OA;
use Nelmio\ApiDocBundle\Annotation\Model;
use Symfony\Component\HttpFoundation\Request;
use App\Controller\Api\Shared\AbstractBaseApiController;
use FOS\RestBundle\Controller\Annotations as Rest;
use App\Service\Chat\Implementation\SupportService;


#[OA\Tag(name: "support")]
class SupportController extends AbstractBaseApiController
{
  private SupportService $service;


  public function __construct(SupportService $service)
  {
    $this->service = $service;
  }
  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/chat/support/requests", name: "api_support_requests")]
  public function getRequests(Request $request)
  {
    return $this->result($this->service->getRequests($request));
  }

  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Post("/chat/support/responses/{id}/attachments", name: "api_support_requests_2")]
  public function addResponseAttachment(Request $request, string $id)
  {
    return $this->result($this->service->addResponseAttachment($request, $id));
  }



  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/chat/support/responses/{id}/attachments", name: "api_support_requests_3")]
  public function getResponsesAttachments(Request $request, string $id)
  {
    return $this->result($this->service->getResponsesAttachments($request, $id));
  }

    /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/public/support/responses/{id}/attachments", name: "api_public_requests_3")]
  public function getAdminResponsesAttachments(Request $request, string $id)
  {
    return $this->result($this->service->getResponsesAttachments($request, $id));
  }

  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Post("/chat/support/responses", name: "api_support_requests_4")]
  public function addResponse(Request $request)
  {
    return $this->result($this->service->addResponse($request));
  }




  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Patch("/chat/support/requests/{id}", name: "api_support_requests_5")]
  public function updateRequests(Request $request, string $id)
  {
    return $this->result($this->service->updateRequests($request, $id));
  }

  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Post("/chat/support/requests/{id}/attachments", name: "api_support_requests_6")]
  public function addRequestAttachment(Request $request, string $id)
  {
    return $this->result($this->service->addRequestAttachment($request, $id));
  }
  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Post("/chat/support/requests", name: "api_support_requests_7")]
  public function addRequests(Request $request)
  {
    return $this->result($this->service->addRequests($request));
  }


  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/chat/support/requests/{id}/attachments", name: "api_support_requests_8")]
  public function getRequestsAttachments(Request $request, string $id)
  {
    return $this->result($this->service->getRequestsAttachments($request, $id));
  }

    /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/public/support/requests/{id}/attachments", name: "api_public_requests_8")]
  public function getAdminRequestsAttachments(Request $request, string $id)
  {
    return $this->result($this->service->getRequestsAttachments($request, $id));
  }

  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/chat/support/requests/{id}", name: "api_support_requests_9")]
  public function getRequest(Request $request, string $id)
  {
    return $this->result($this->service->getRequest($request, $id));
  }

    /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/public/support/requests/{id}", name: "api_public_requests_9")]
  public function getAdminRequest(Request $request, string $id)
  {
    return $this->result($this->service->getRequest($request, $id));
  }


  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/chat/support/requests/{id}/responses", name: "api_support_requests_10")]
  public function getRequestsResponses(Request $request, string $id)
  {
    return $this->result($this->service->getRequestsResponses($request, $id));
  }

    /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/public/support/requests/{id}/responses", name: "api_public_requests_10")]
  public function getAdminRequestsResponses(Request $request, string $id)
  {
    return $this->result($this->service->getRequestsResponses($request, $id));
  }


}

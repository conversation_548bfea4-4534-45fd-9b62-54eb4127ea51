<?php

namespace App\Controller\Api\Company;

use OpenApi\Attributes as OA;
use App\Dto\Shared\In\QueryFilterIn;
use Nelmio\ApiDocBundle\Annotation\Model;
use Symfony\Component\HttpFoundation\Request;
use App\Dto\Shared\Out\Setting\SettingFullOut;
use App\Dto\Company\Out\Company\CompanyFullOut;
use App\Controller\Api\Shared\AbstractBaseApiController;
use App\Service\Company\Interface\ICompanyService;
use FOS\RestBundle\Controller\Annotations as Rest;
use App\Service\Company\Interface\ICompanyCategoryService;
use App\Dto\Company\Out\CompanyCategory\CompanyCategoryFullOut;

#[OA\Tag(name: "companies")]
class CompanyCategoriesController extends AbstractBaseApiController
{
  private ICompanyCategoryService $service;
  private ICompanyService $companyService;

  public function __construct(ICompanyCategoryService $service,ICompanyService $companyService)
  {
    $this->service = $service;
    $this->companyService = $companyService;
  }

  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/companies/categories", name: "api_company_categories_findAll")]
  #[OA\Parameter(
    name: 'limit',
    in: 'query',
    description: 'Maximum number of results to return.',
    schema: new OA\Schema(type: 'integer')
  )]
  #[OA\Parameter(
    name: 'offset',
    in: 'query',
    description: 'The number of results to skip',
    schema: new OA\Schema(type: 'integer')
  )]
  #[OA\Parameter(
    name: 'search',
    in: 'query',
    description: 'Search keyword',
    schema: new OA\Schema(type: 'string')
  )]
  #[OA\Parameter(
    name: 'filters',
    in: 'query',
    description: 'Query filters',
    schema: new OA\Schema(type: 'array', items: new OA\Items(ref: new Model(type: QueryFilterIn::class)))
  )]
  #[OA\Parameter(
    name: 'output',
    in: 'query',
    description: 'Result output format',
    schema: new OA\Schema(
      type: "string",
      enum: ["tiny", "minimal", "standard", "full"],
      default: "standard"
    ),
  )]
  #[OA\Response(
    response: 200,
    description: 'Returns the rewards of an user if output = "standard" or empty',
    content: new OA\JsonContent(
      type: 'array',
      items: new OA\Items(ref: new Model(type: CompanyCategoryFullOut::class))
    )
  )]
  public function findAll(Request $request)
  {
    return $this->result($this->service->findAll($request));
  }

    /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/public/companies/list", name: "api_company_findAll")]
  #[OA\Parameter(
    name: 'limit',
    in: 'query',
    description: 'Maximum number of results to return.',
    schema: new OA\Schema(type: 'integer')
  )]
  #[OA\Parameter(
    name: 'offset',
    in: 'query',
    description: 'The number of results to skip',
    schema: new OA\Schema(type: 'integer')
  )]
  #[OA\Parameter(
    name: 'search',
    in: 'query',
    description: 'Search keyword',
    schema: new OA\Schema(type: 'string')
  )]
  #[OA\Parameter(
    name: 'filters',
    in: 'query',
    description: 'Query filters',
    schema: new OA\Schema(type: 'array', items: new OA\Items(ref: new Model(type: QueryFilterIn::class)))
  )]
  #[OA\Parameter(
    name: 'output',
    in: 'query',
    description: 'Result output format',
    schema: new OA\Schema(
      type: "string",
      enum: ["tiny", "minimal", "standard", "full"],
      default: "standard"
    ),
  )]
  #[OA\Response(
    response: 200,
    description: 'Returns the rewards of an user if output = "standard" or empty',
    content: new OA\JsonContent(
      type: 'array',
      items: new OA\Items(ref: new Model(type: CompanyFullOut::class))
    )
  )]
  public function findCompaniesAll()
  {
    return $this->result($this->companyService->findAll());
  }

  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/companies/categories/{uuid}", name: "api_company_categories_findOne")]
  #[OA\Parameter(
    name: 'output',
    in: 'query',
    description: 'Result output format',
    schema: new OA\Schema(
      type: "string",
      enum: ["tiny", "minimal", "standard", "full"],
      default: "full"
    ),
  )]
  #[OA\Response(
    response: 200,
    description: 'Returns the rewards of an user if output = "standard" or empty',
    content: new OA\JsonContent(
      type: 'array',
      items: new OA\Items(ref: new Model(type: SettingFullOut::class))
    )
  )]
  public function findOne(string $uuid, Request $request)
  {
    return $this->result($this->service->findOne($uuid, $request));
  }

  /**
   * Find the specified company.
   *
   * This returns a company identified by its uuid.
   */
  #[Rest\Get("/companies/{uuid}", name: "api_companies_findOne")]
  #[OA\Parameter(
    name: 'output',
    in: 'query',
    description: 'Result output format',
    schema: new OA\Schema(
      type: "string",
      enum: ["tiny", "minimal", "standard", "full"],
      default: "full"
    ),
  )]
  #[OA\Response(
    response: 200,
    description: 'Returns the full output of a company',
    content: new OA\JsonContent(
      type: 'array',
      items: new OA\Items(ref: new Model(type: CompanyFullOut::class))
    )
  )]
  public function findOneCompany(string $uuid, Request $request)
  {
    return $this->result($this->companyService->findOne($uuid, $request));
  }
}

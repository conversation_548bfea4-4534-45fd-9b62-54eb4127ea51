<?php

namespace App\Controller\Api\Company;

use App\Dto\GenericResponse;
use Psr\Log\LoggerInterface;
use OpenApi\Attributes as OA;
use App\Entity\Activity\Activity;
use App\Entity\Security\UserMeta;
use App\Entity\Company\Subscription;
use App\Entity\Company\SubscriptionPlan;
use Nelmio\ApiDocBundle\Annotation\Model;
use App\Dto\Company\In\SubscriptionPlanIn;
use App\Repository\Security\UserRepository;
use App\Repository\Shared\SettingRepository;
use Nelmio\ApiDocBundle\Annotation\Security;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\KernelInterface;
use App\Dto\Activity\Out\Activity\ActivityFullOut;
use App\Repository\Company\SubscriptionRepository;

use FOS\RestBundle\Controller\Annotations as Rest;
use App\Service\Shared\Implementation\CommonService;
use App\Service\Company\Implementation\CompanyService;
use App\Controller\Api\Shared\AbstractBaseApiController;
use App\Service\Company\Implementation\SubscriptionService;

#[OA\Tag(name: "companies subscriptions")]
class SubscriptionsController extends AbstractBaseApiController
{
    private SubscriptionService $service;


  
    public function __construct(SubscriptionService $service)
    {
      $this->service = $service;

    }

        /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/public/companies/subscriptions/checkcompanytrials", name: "api_companies_subscriptions_checkcompanytrials")]
  public function checkCompanyTrials()
  {
    $data = $this->service->checkCompanyTrials();
    return $this->result($data);
  }

      /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Post("/companies/subscriptions/subscribe", name: "api_companies_subscriptions_subscribe")]
  public function subscribePro(Request $request)
  {
    $payUrl = $this->container->get('request_stack')->getCurrentRequest()->getSchemeAndHttpHost() . "/public/payments/";
    $data = $this->service->subscribePro($request, $payUrl);
    return $this->result($data);
  }

    /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/companies/subscriptions/payments/list", name: "api_companies_subscriptions_payments_list")]
  public function getSubscriptionsPayments(Request $request)
  {
    $data = $this->service->getSubscriptionsPayments( $request);
    return $this->result($data);
  }

      /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/companies/admin/subscriptions/list", name: "api_companies_admin_subscriptions_list")]
  public function getSubscriptionsAdmin()
  {
    $data = $this->service->getSubscriptionsAdmin();
    return $this->result($data);
  }
  
       /**
   * For creating a new subscription plan.
   *
   * This call allows to create a new subscription plan.
   */
  #[Rest\Post("/companies/subscriptions/plans/create", name: "api_companies_subscription_plans_create")]
  #[OA\RequestBody(
    required: true,
    content: new OA\JsonContent(ref: new Model(type: SubscriptionPlanIn::class))
  )]
  #[OA\Response(
    response: 200,
    description: 'Returns the created subscription plan',
    content: new OA\JsonContent(
        type: 'array',
        items: new OA\Items(ref: new Model(type: SubscriptionPlan::class))
    )
  )]
#[Security(name: 'Bearer')]
  public function createSubscriptionPlan(Request $request)
  {
    $data = $this->service->createSubscriptionPlan($request);
    return $this->result($data);
  }

      /**
   * List the subscription plans.
   *
   * This call takes into account all subscription plans.
   */
  #[OA\Response(
    response: 200,
    description: 'Returns all the subscription plans',
    content: new OA\JsonContent(
        type: 'array',
        items: new OA\Items(ref: new Model(type: SubscriptionPlan::class))
    )
  )]

  #[Rest\Get("/companies/subscriptions/plans/list", name: "api_companies_subscription_plans_list")]
  public function getSubscriptionPlans(Request $request)
  {
    $data = $this->service->getSubscriptionPlans($request);
    return $this->result($data);
  }
      /**
   * List the details of the specified subscription plan.
   *
   * This call return the details of the specified subscription plan.
   */
  #[OA\Parameter(
    name: 'id',
    in: 'path',
    description: 'Subscription plan id',
    required: true,
  )]
  #[OA\Response(
    response: 200,
    description: 'Returns the specific subscription plan',
    content: new OA\JsonContent(
        type: 'array',
        items: new OA\Items(ref: new Model(type: SubscriptionPlan::class))
    )
  )]
  #[Security(name: 'Bearer')]
  #[Rest\Get("/companies/subscriptions/plans/{id}", name: "api_companies_subscription_plans_id")]
  public function getSubscriptionPlan(mixed $id)
  {
    $data = $this->service->getSubscriptionPlan($id);
    return $this->result($data);
  }

        /**
   * Update the details of the specified subscription plan.
   *
   * This call takes into account the specified subscription plan.
   */
  #[Rest\Patch("/companies/subscriptions/plans/{id}", name: "api_companies_subscription_plans_id_patch")]
  #[OA\Parameter(
    name: 'id',
    in: 'path',
    description: 'Subscription plan id',
    required: true,
  )]
  #[OA\RequestBody(
    required: true,
    content: new OA\JsonContent(ref: new Model(type: SubscriptionPlan::class))
  )]
  #[OA\Response(
    response: 200,
    description: 'Returns the updated subscription plan',
    content: new OA\JsonContent(
        type: 'array',
        items: new OA\Items(ref: new Model(type: SubscriptionPlan::class))
    )
  )]
  #[Security(name: 'Bearer')]
  public function updateSubscriptionPlan(Request $request, mixed $id)
  {
    $data = $this->service->updateSubscriptionPlan($request, $id);
    return $this->result($data);  
  }

          /**
   * Update the details of the specified subscription plan.
   *
   * This call takes into account the specified subscription plan.
   */
  #[Rest\Patch("/companies/admin/subscriptions/update", name: "api_companies_subscription_patch")]
  #[OA\Parameter(
    name: 'id',
    in: 'path',
    description: 'Subscription id',
    required: true,
  )]
  #[OA\RequestBody(
    required: true,
    content: new OA\JsonContent(ref: new Model(type: Subscription::class))
  )]
  #[OA\Response(
    response: 200,
    description: 'Returns the updated subscription plan',
    content: new OA\JsonContent(
        type: 'array',
        items: new OA\Items(ref: new Model(type: Subscription::class))
    )
  )]
  #[Security(name: 'Bearer')]
  public function updateSubscriptionAdmin(Request $request)
  {
    $data = $this->service->updateSubscriptionAdmin($request);
    return $this->result($data);  
  }

    /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Delete("/subscriptions/admin/remove/{id}", name: "api_commons_subscriptions_removeone")]
  public function removeSubscriptionPayment(mixed $id)
  {
    $data = $this->service->removeSubscriptionPayment($id);
    return $this->result($data);
  }
  /**
     * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Delete("/subscriptions/plans/admin/remove/{id}", name: "api_commons_subscriptions_plans_removeone")]
  public function removeSubscriptionPlan(mixed $id)
  {
    $data = $this->service->removeSubscriptionPlan($id);
    return $this->result($data);
  }

    /**
   * Enable or disable the specified subscription plan.
   *
   * This call enables or disables the specified subscription plan depending on the current status.
   */
  #[OA\Parameter(
    name: 'id',
    in: 'path',
    description: 'Subscription plan id',
    required: true,
  )]
  #[OA\Response(
    response: 200,
    description: 'Returns the enabled or disabled subscription plan',
    content: new OA\JsonContent(
        type: 'array',
        items: new OA\Items(ref: new Model(type: SubscriptionPlan::class))
    )
  )]
  #[Security(name: 'Bearer')]
  #[Rest\Post("/companies/subscriptions/plans/toggle/{id}", name: "api_companies_subscription_plan_toggle")]
  public function toggleSubscriptionPlan(mixed $id)
  {
    $data = $this->service->toggleSubscriptionPlan($id);
    return $this->result($data);
  }


  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/public/companies/subscriptions/check", name: "api_commons_companies_subscriptions_check")]
  public function checkSubscriptions()
  {

    $data = $this->service->checkSubscriptions();
    return $this->result($data);
  }










  
  
}

<?php

namespace App\Controller\Api\Company;

use App\Controller\Api\Shared\AbstractBaseApiController;
use App\Dto\Company\Out\CompanyType\CompanyTypeFullOut;
use App\Dto\Shared\In\QueryFilterIn;
use App\Service\Company\Interface\ICompanyTypeService;
use FOS\RestBundle\Controller\Annotations as Rest;
use Symfony\Component\HttpFoundation\Request;
use OpenApi\Attributes as OA;
use Nelmio\ApiDocBundle\Annotation\Model;

#[OA\Tag(name: "companies")]
class CompanyTypesController extends AbstractBaseApiController
{
  private ICompanyTypeService $service;

  public function __construct(ICompanyTypeService $service)
  {
    $this->service = $service;
  }

  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/companies/types", name: "api_company_types_findAll")]
  #[OA\Parameter(
    name: 'limit',
    in: 'query',
    description: 'Maximum number of results to return.',
    schema: new OA\Schema(type: 'integer')
  )]
  #[OA\Parameter(
    name: 'offset',
    in: 'query',
    description: 'The number of results to skip',
    schema: new OA\Schema(type: 'integer')
  )]
  #[OA\Parameter(
    name: 'search',
    in: 'query',
    description: 'Search keyword',
    schema: new OA\Schema(type: 'string')
  )]
  #[OA\Parameter(
    name: 'filters',
    in: 'query',
    description: 'Query filters',
    schema: new OA\Schema(type: 'array', items: new OA\Items(ref: new Model(type: QueryFilterIn::class)))
  )]
  #[OA\Parameter(
    name: 'output',
    in: 'query',
    description: 'Result output format',
    schema: new OA\Schema(
      type: "string",
      enum: ["tiny", "minimal", "standard", "full"],
      default: "standard"
    ),
  )]
  #[OA\Response(
    response: 200,
    description: 'Returns the rewards of an user if output = "standard" or empty',
    content: new OA\JsonContent(
      type: 'array',
      items: new OA\Items(ref: new Model(type: CompanyTypeFullOut::class))
    )
  )]
  public function findAll(Request $request)
  {
    return $this->result($this->service->findAll($request));
  }

  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Get("/companies/types/{uuid}", name: "api_company_types_findOne")]
  #[OA\Parameter(
    name: 'output',
    in: 'query',
    description: 'Result output format',
    schema: new OA\Schema(
      type: "string",
      enum: ["tiny", "minimal", "standard", "full"],
      default: "full"
    ),
  )]
  #[OA\Response(
    response: 200,
    description: 'Returns the rewards of an user if output = "standard" or empty',
    content: new OA\JsonContent(
      type: 'array',
      items: new OA\Items(ref: new Model(type: SettingFullOut::class))
    )
  )]
  public function findOne(string $uuid, Request $request)
  {
    return $this->result($this->service->findOne($uuid, $request));
  }
}

<?php

namespace App\Controller\Api\Tournament;

use App\Service\Tournament\Implementation\EncounterService;
use App\Controller\Api\Shared\AbstractBaseApiController;
use Symfony\Component\HttpFoundation\Request;
use FOS\RestBundle\Controller\Annotations as Rest;
use OpenApi\Attributes as OA;
use Nelmio\ApiDocBundle\Annotation\Security;

#[Rest\Route('/encounters')]
class EncounterController extends AbstractBaseApiController
{
    private EncounterService $service;

    public function __construct(EncounterService $service)
    {
        $this->service = $service;
    }

    /**
     * Create a new encounter (admin only)
     */
    #[Rest\Post('/create')]
    #[OA\Tag(name: 'Encounters')]
    #[OA\Post(
        path: '/api/v2/encounters/create',
        summary: 'Create a new encounter',
        description: 'Creates a new encounter between two teams (admin only)'
    )]
    #[OA\RequestBody(
        required: true,
        content: new OA\JsonContent(properties: [
            new OA\Property(property: 'homeTeamId', type: 'string'),
            new OA\Property(property: 'awayTeamId', type: 'string'),
            new OA\Property(property: 'encounterDate', type: 'string', format: 'date-time'),
            new OA\Property(property: 'location', type: 'string'),
            new OA\Property(property: 'tournamentId', type: 'string'),
            new OA\Property(property: 'status', type: 'string'),
            new OA\Property(property: 'notes', type: 'string')
        ])
    )]
    #[OA\Response(
        response: 200,
        description: 'Encounter created successfully'
    )]
    #[Security(name: 'Bearer')]
    public function createEncounter(Request $request)
    {
        return $this->result($this->service->createEncounter($request));
    }

    /**
     * Get all encounters
     */
    #[Rest\Get('/list')]
    #[OA\Get(
        path: '/api/v2/encounters/list',
        summary: 'Get all encounters',
        description: 'Retrieves all encounters with optional filtering',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'status',
        in: 'query',
        description: 'Filter by status',
        required: false,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'category',
        in: 'query',
        description: 'Filter by category',
        required: false,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'tournamentId',
        in: 'query',
        description: 'Filter by tournament',
        required: false,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 200,
        description: 'List of encounters'
    )]
    public function getAllEncounters(Request $request)
    {
        return $this->result($this->service->getAllEncounters($request));
    }

    /**
     * Get encounter by ID
     */
    #[Rest\Get('/show/{id}')]
    #[OA\Get(
        path: '/api/v2/encounters/show/{id}',
        summary: 'Get encounter by ID',
        description: 'Retrieves an encounter by ID',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'id',
        in: 'path',
        description: 'Encounter UUID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 200,
        description: 'Encounter details'
    )]
    public function getEncounter(string $id)
    {
        return $this->result($this->service->getEncounter($id));
    }

    /**
     * Update encounter (admin only)
     */
    #[Rest\Put('/update/{id}')]
    #[OA\Put(
        path: '/api/v2/encounters/update/{id}',
        summary: 'Update encounter',
        description: 'Updates an encounter (admin only)',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'id',
        in: 'path',
        description: 'Encounter UUID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\RequestBody(
        description: 'Encounter data',
        required: true,
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'encounterDate', type: 'string', format: 'date-time'),
                new OA\Property(property: 'location', type: 'string'),
                new OA\Property(property: 'status', type: 'string'),
                new OA\Property(property: 'notes', type: 'string')
            ]
        )
    )]
    #[OA\Response(
        response: 200,
        description: 'Encounter updated successfully'
    )]
    public function updateEncounter(Request $request, string $id)
    {
        return $this->result($this->service->updateEncounter($request, $id));
    }

    /**
     * Delete encounter (admin only)
     */
    #[Rest\Delete('/delete/{id}')]
    #[OA\Delete(
        path: '/api/v2/encounters/delete/{id}',
        summary: 'Delete encounter',
        description: 'Deletes an encounter (admin only)',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'id',
        in: 'path',
        description: 'Encounter UUID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 200,
        description: 'Encounter deleted successfully'
    )]
    public function deleteEncounter(string $id)
    {
        return $this->result($this->service->deleteEncounter($id));
    }

    /**
     * Add event to encounter (admin only) - goals, cards, etc.
     */
    #[Rest\Post('/add-event')]
    #[OA\Post(
        path: '/api/v2/encounters/add-event',
        summary: 'Add event to encounter',
        description: 'Adds an event (goal, card, etc.) to an encounter (admin only)',
        security: [['Bearer' => []]]
    )]
    #[OA\RequestBody(
        required: true,
        content: new OA\JsonContent(properties: [
            new OA\Property(property: 'encounterId', type: 'string'),
            new OA\Property(property: 'playerId', type: 'string'),
            new OA\Property(property: 'type', type: 'string', enum: ['GOAL', 'YELLOW_CARD', 'RED_CARD']),
            new OA\Property(property: 'minute', type: 'integer'),
            new OA\Property(property: 'description', type: 'string'),
            new OA\Property(property: 'assistPlayerId', type: 'string')
        ])
    )]
    #[OA\Response(
        response: 200,
        description: 'Event added successfully'
    )]
    public function addEventToEncounter(Request $request)
    {
        return $this->result($this->service->addEventToEncounter($request));
    }

    /**
     * Get upcoming encounters
     */
    #[Rest\Get('/upcoming')]
    #[OA\Get(
        path: '/api/v2/encounters/upcoming',
        summary: 'Get upcoming encounters',
        description: 'Retrieves upcoming encounters',
        security: [['Bearer' => []]]
    )]
    #[OA\Response(
        response: 200,
        description: 'List of upcoming encounters'
    )]
    public function getUpcomingEncounters()
    {
        return $this->result($this->service->getUpcomingEncounters());
    }

    /**
     * Get completed encounters
     */
    #[Rest\Get('/completed')]
    #[OA\Get(
        path: '/api/v2/encounters/completed',
        summary: 'Get completed encounters',
        description: 'Retrieves completed encounters',
        security: [['Bearer' => []]]
    )]
    #[OA\Response(
        response: 200,
        description: 'List of completed encounters'
    )]
    public function getCompletedEncounters()
    {
        return $this->result($this->service->getCompletedEncounters());
    }

    /**
     * Get encounters by team
     */
    #[Rest\Get('/team/{teamId}')]
    #[OA\Get(
        path: '/api/v2/encounters/team/{teamId}',
        summary: 'Get encounters by team',
        description: 'Retrieves encounters for a specific team',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'teamId',
        in: 'path',
        description: 'Team UUID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 200,
        description: 'List of team encounters'
    )]
    public function getEncountersByTeam(string $teamId)
    {
        return $this->result($this->service->getEncountersByTeam($teamId));
    }

    /**
     * Get encounters by tournament
     */
    #[Rest\Get('/tournament/{tournamentId}')]
    #[OA\Get(
        path: '/api/v2/encounters/tournament/{tournamentId}',
        summary: 'Get encounters by tournament',
        description: 'Retrieves encounters for a specific tournament',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'tournamentId',
        in: 'path',
        description: 'Tournament UUID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 200,
        description: 'List of tournament encounters'
    )]
    public function getEncountersByTournament(string $tournamentId)
    {
        return $this->result($this->service->getEncountersByTournament($tournamentId));
    }
}

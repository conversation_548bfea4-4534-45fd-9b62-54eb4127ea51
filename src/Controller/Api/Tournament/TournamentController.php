<?php

namespace App\Controller\Api\Tournament;

use App\Service\Tournament\Implementation\TournamentService;
use App\Controller\Api\Shared\AbstractBaseApiController;
use Symfony\Component\HttpFoundation\Request;
use FOS\RestBundle\Controller\Annotations as Rest;
use OpenApi\Attributes as OA;
use Nelmio\ApiDocBundle\Annotation\Security;

#[Rest\Route('/tournaments')]
class TournamentController extends AbstractBaseApiController
{
    private TournamentService $service;

    public function __construct(TournamentService $service)
    {
        $this->service = $service;
    }

    /**
     * Create a new tournament (admin only)
     */
    #[Rest\Post('/create')]
    #[OA\Tag(name: 'Tournaments')]
    #[OA\Post(
        path: '/api/v2/tournaments/create',
        summary: 'Create a new tournament',
        description: 'Creates a new tournament (admin only)'
    )]
    #[OA\RequestBody(
        required: true,
        content: new OA\JsonContent(properties: [
            new OA\Property(property: 'name', type: 'string'),
            new OA\Property(property: 'description', type: 'string'),
            new OA\Property(property: 'category', type: 'string'),
            new OA\Property(property: 'startDate', type: 'string', format: 'date'),
            new OA\Property(property: 'endDate', type: 'string', format: 'date'),
            new OA\Property(property: 'location', type: 'string'),
            new OA\Property(property: 'rules', type: 'string'),
            new OA\Property(property: 'maxTeams', type: 'integer'),
            new OA\Property(property: 'status', type: 'string')
        ])
    )]
    #[OA\Response(
        response: 200,
        description: 'Tournament created successfully'
    )]
    #[Security(name: 'Bearer')]
    public function createTournament(Request $request)
    {
        return $this->result($this->service->createTournament($request));
    }

    /**
     * Get all tournaments
     */
    #[Rest\Get('/list')]
    #[OA\Get(
        path: '/api/v2/tournaments/list',
        summary: 'Get all tournaments',
        description: 'Retrieves all tournaments with optional filtering',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'status',
        in: 'query',
        description: 'Filter by status',
        required: false,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'category',
        in: 'query',
        description: 'Filter by category',
        required: false,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Parameter(
        name: 'active',
        in: 'query',
        description: 'Filter by active status',
        required: false,
        schema: new OA\Schema(type: 'boolean')
    )]
    #[OA\Response(
        response: 200,
        description: 'List of tournaments'
    )]
    public function getAllTournaments(Request $request)
    {
        return $this->result($this->service->getAllTournaments($request));
    }

    /**
     * Get tournament by ID
     */
    #[Rest\Get('/show/{id}')]
    #[OA\Get(
        path: '/api/v2/tournaments/show/{id}',
        summary: 'Get tournament by ID',
        description: 'Retrieves a tournament by ID',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'id',
        in: 'path',
        description: 'Tournament UUID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 200,
        description: 'Tournament details'
    )]
    public function getTournament(string $id)
    {
        return $this->result($this->service->getTournament($id));
    }

    /**
     * Update tournament (admin only)
     */
    #[Rest\Put('/update/{id}')]
    #[OA\Put(
        path: '/api/v2/tournaments/update/{id}',
        summary: 'Update tournament',
        description: 'Updates a tournament (admin only)',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'id',
        in: 'path',
        description: 'Tournament UUID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\RequestBody(
        description: 'Tournament data',
        required: true,
        content: new OA\JsonContent(
            properties: [
                new OA\Property(property: 'name', type: 'string'),
                new OA\Property(property: 'description', type: 'string'),
                new OA\Property(property: 'category', type: 'string'),
                new OA\Property(property: 'startDate', type: 'string', format: 'date'),
                new OA\Property(property: 'endDate', type: 'string', format: 'date'),
                new OA\Property(property: 'location', type: 'string'),
                new OA\Property(property: 'rules', type: 'string'),
                new OA\Property(property: 'maxTeams', type: 'integer'),
                new OA\Property(property: 'status', type: 'string')
            ]
        )
    )]
    #[OA\Response(
        response: 200,
        description: 'Tournament updated successfully'
    )]
    public function updateTournament(Request $request, string $id)
    {
        return $this->result($this->service->updateTournament($request, $id));
    }

    /**
     * Delete tournament (admin only)
     */
    #[Rest\Delete('/delete/{id}')]
    #[OA\Delete(
        path: '/api/v2/tournaments/delete/{id}',
        summary: 'Delete tournament',
        description: 'Deletes a tournament (admin only)',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'id',
        in: 'path',
        description: 'Tournament UUID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 200,
        description: 'Tournament deleted successfully'
    )]
    public function deleteTournament(string $id)
    {
        return $this->result($this->service->deleteTournament($id));
    }

    /**
     * Add company to tournament (admin only)
     */
    #[Rest\Post('/add-company')]
    #[OA\Post(
        path: '/api/v2/tournaments/add-company',
        summary: 'Add company to tournament',
        description: 'Adds a company to a tournament (admin only)',
        security: [['Bearer' => []]]
    )]
    #[OA\RequestBody(
        required: true,
        content: new OA\JsonContent(properties: [
            new OA\Property(property: 'tournamentId', type: 'string'),
            new OA\Property(property: 'companyId', type: 'string')
        ])
    )]
    #[OA\Response(
        response: 200,
        description: 'Company added to tournament successfully'
    )]
    public function addCompanyToTournament(Request $request)
    {
        return $this->result($this->service->addCompanyToTournament($request));
    }

    /**
     * Remove company from tournament (admin only)
     */
    #[Rest\Post('/remove-company')]
    #[OA\Post(
        path: '/api/v2/tournaments/remove-company',
        summary: 'Remove company from tournament',
        description: 'Removes a company from a tournament (admin only)',
        security: [['Bearer' => []]]
    )]
    #[OA\RequestBody(
        required: true,
        content: new OA\JsonContent(properties: [
            new OA\Property(property: 'tournamentId', type: 'string'),
            new OA\Property(property: 'companyId', type: 'string')
        ])
    )]
    #[OA\Response(
        response: 200,
        description: 'Company removed from tournament successfully'
    )]
    public function removeCompanyFromTournament(Request $request)
    {
        return $this->result($this->service->removeCompanyFromTournament($request));
    }

    /**
     * Get tournament statistics
     */
    #[Rest\Get('/statistics/{id}')]
    #[OA\Get(
        path: '/api/v2/tournaments/statistics/{id}',
        summary: 'Get tournament statistics',
        description: 'Retrieves statistics for a tournament',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'id',
        in: 'path',
        description: 'Tournament UUID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 200,
        description: 'Tournament statistics'
    )]
    public function getTournamentStatistics(string $id)
    {
        return $this->result($this->service->getTournamentStatistics($id));
    }

    /**
     * Get tournament standings
     */
    #[Rest\Get('/standings/{id}')]
    #[OA\Get(
        path: '/api/v2/tournaments/standings/{id}',
        summary: 'Get tournament standings',
        description: 'Retrieves standings/rankings for a tournament',
        security: [['Bearer' => []]]
    )]
    #[OA\Parameter(
        name: 'id',
        in: 'path',
        description: 'Tournament UUID',
        required: true,
        schema: new OA\Schema(type: 'string')
    )]
    #[OA\Response(
        response: 200,
        description: 'Tournament standings'
    )]
    public function getTournamentStandings(string $id)
    {
        return $this->result($this->service->getTournamentStandings($id));
    }
}

<?php

namespace App\Controller\Api\Shared;

use App\Dto\GenericResponse;
use Psr\Log\LoggerInterface;
use OpenApi\Attributes as OA;
use App\Service\Shared\Implementation\CommonService;
use App\Entity\Activity\Activity;
use App\Entity\Security\UserMeta;
use App\Entity\Company\SubscriptionPlan;
use Nelmio\ApiDocBundle\Annotation\Model;
use App\Dto\Company\In\SubscriptionPlanIn;
use App\Repository\Security\UserRepository;
use App\Repository\Shared\SettingRepository;
use Nelmio\ApiDocBundle\Annotation\Security;
use Symfony\Component\HttpFoundation\Request;
use App\Controller\Api\Shared\AbstractBaseApiController;
use Symfony\Component\HttpKernel\KernelInterface;
use App\Dto\Activity\Out\Activity\ActivityFullOut;
use App\Repository\Company\SubscriptionRepository;
use FOS\RestBundle\Controller\Annotations as Rest;
use App\Service\Security\Implementation\UserService;
use App\Service\Payment\Implementation\PaymentService;

#[OA\Tag(name: "commons")]
class CommonsController extends AbstractBaseApiController
{
  /*public $subscriptionKey = "00e0e182886a4ac7bfa62a84015225e6";
  public $apiUser = "1adca557-34ec-44ea-9f64-8aacf99960c3";
  public $apiKey = "208b6dce6ef040729cf1c977b4796218";
  public $apiUrl = "https://sandbox.momodeveloper.mtn.com";
  public $targetEnvironment = "sandbox";
  public $currency = "EUR";*/

  public $subscriptionKey = "c4bd971632d24c009c1fc77e02ba983f";
  public $apiUser = "7e8571cf-2655-42b2-8e78-0b0e021d27ac";
  public $apiKey = "dc06eecfcd6247df86a413e80ea96b24";
  public $apiUrl = "https://proxy.momoapi.mtn.com";
  public $targetEnvironment = "mtncameroon";
  public $currency = "XAF";

  public $payerMessage = "Paiement sportaabe ";
  public $payeeNote = "Paiement sportaabe ";
  public $accessToken = "";

  public $mailGunSMTPHost = "smtp.mailgun.org";
  public $mailGunSMTPPort = 587;

  public $mailGunKey = "**************************************************";
  public $mailGunDomain = "mg.afrik.sportaabe.com";

  public $mailGunSenderName = "sportaabe ";
  public $mailGunSenderMail = "<EMAIL>";
  public $mailGunSenderPass = "**************************************************";

  /*public $currencyOrange = "OUV";
  public $paymentUrl = "https://api.orange.com/orange-money-webpay/dev/v1/webpayment";
  public $statusUrl = "https://api.orange.com/orange-money-webpay/dev/v1/transactionstatus";
  public $tokenUrl = "https://api.orange.com/oauth/v3/token";
  public $baseLink = "http://************";
  public $language = "fr";
  public $merchantName = "sportaabe SAS";
  public $merchantKey = "7630aa9c";
  public $authHeader = "Basic RjNVSDRqQzBxOTlCanV4MWY2Z0FPTXZPUEZsdE5NR1A6QWFPeU9lOHhMbjlIb04yRw==";
  public $productionMode = false;
  public $accessTokenOrange = "";*/

  public $currencyOrange = "XAF";
  public $paymentUrl = "https://api.orange.com/orange-money-webpay/cm/v1/webpayment";
  public $statusUrl = "https://api.orange.com/orange-money-webpay/cm/v1/transactionstatus";
  public $tokenUrl = "https://api.orange.com/oauth/v3/token";
  public $baseLink = "https://api.afrik.sportaabe.com";
  public $language = "fr";
  public $merchantName = "sportaabe";
  public $merchantKey = "5da13aba";
  public $authHeader = "Basic SGFsVmVLQTdWQTNvQW9qWktua1dRT0hRWjdCcmFvQWo6NG9xTWtCUGFEQkhmWUwwag==";
  public $productionMode = true;
  public $accessTokenOrange = "";

  private PaymentService $service;
  private UserRepository $userRepository;
  private SettingRepository $settingRepository;
  private KernelInterface $appKernel;
  private UserService $userService;
  private CommonService $commonService;

  public function __construct(PaymentService $service, UserRepository $userRepository, SettingRepository $settingRepository, KernelInterface $appKernel, UserService $userService, CommonService $commonService)  
  {
    $this->service = $service;
    $this->appKernel = $appKernel;
    $this->userRepository = $userRepository;
    $this->settingRepository = $settingRepository;
    $this->userService = $userService;
    $this->commonService = $commonService;
  }

  
  /**
   * List the rewards of the specified user.
   *
   * This call takes into account all confirmed awards, but not pending or refused awards.
   */
  #[Rest\Post("/commons/firebase/profile/refresh", name: "api_commons_firebase_profile_refresh")]
  public function refreshFirebaseProfile(Request $request)
  {
    return $this->result($this->commonService->refreshFirebaseProfile($request));
  }

  
}
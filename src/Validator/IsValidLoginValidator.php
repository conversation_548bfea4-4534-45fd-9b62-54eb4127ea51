<?php

namespace App\Validator;

use App\Dto\Security\In\AuthenticateIn;
use App\Enum\LoginTypes;
use libphonenumber\NumberParseException;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedValueException;
use libphonenumber\PhoneNumberUtil;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\Validator\Constraints as Assert;

class IsValidLoginValidator extends ConstraintValidator
{

  private ValidatorInterface $validator;

  public function __construct(ValidatorInterface $validator)
  {
    $this->validator = $validator;
  }

  /**
   * @param AuthenticateIn $authenticateIn
   */
  public function validate($authenticateIn, Constraint $constraint): void
  {

    if (!$authenticateIn instanceof AuthenticateIn) {
      throw new UnexpectedValueException($authenticateIn, AuthenticateIn::class);
    }

    if (!$constraint instanceof IsValidLogin) {
      throw new UnexpectedTypeException($constraint, IsValidLogin::class);
    }

    $login = $authenticateIn->getLogin();
    $type = $authenticateIn->getType();

    if (empty($login)) {
      return;
    }

    if ($type === LoginTypes::PHONE->value && !$this->isValidPhoneNumber($login)) {
      $this->context
        ->buildViolation($constraint->messagePhone)
        ->atPath('login')
        ->addViolation();
    } else if ($type === LoginTypes::EMAIL->value && !$this->isValidEmail($login)) {
      $this->context
        ->buildViolation($constraint->messageEmail)
        ->atPath('login')
        ->addViolation();
    }
  }

  public function isValidPhoneNumber($login): bool
  {
    try {
      return PhoneNumberUtil::getInstance()->isValidNumber(PhoneNumberUtil::getInstance()->parse($login));
    } catch (NumberParseException $e) {
    }
    return false;
  }

  public function isValidEmail($login): bool
  {
    return empty(
      ($this->validator->validate(
        $login,
        new Assert\Email()
      ))->count());
  }
}

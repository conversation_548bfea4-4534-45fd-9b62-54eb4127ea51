<?php

namespace App\Exception;

/**
 * @template T
 */
class UnprocessableRequestException extends \Exception
{
  /** @var T */
  private $data = null;

  /** @param $data T */
  public function __construct($data)
  {
    $this->data = $data;
  }

  /** @return T */
  public function getData()
  {
    return $this->data;
  }

  public function setData($data): static
  {
    $this->data = $data;

    return $this;
  }
}

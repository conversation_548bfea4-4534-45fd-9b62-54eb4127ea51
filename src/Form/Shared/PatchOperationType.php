<?php

namespace App\Form\Shared;

use App\Dto\Shared\In\PatchOperationIn;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\FormType;

class PatchOperationType extends AbstractType
{
  public function buildForm(FormBuilderInterface $builder, array $options): void
  {
    $builder
      ->add('type')
      ->add('payload', FormType::class, [
        'allow_extra_fields' => true
      ]);
  }

  public function configureOptions(OptionsResolver $resolver): void
  {
    $resolver->setDefaults([
      'data_class' => PatchOperationIn::class,
      'allow_extra_fields' => true,
      'csrf_protection' => false,
    ]);
  }
}

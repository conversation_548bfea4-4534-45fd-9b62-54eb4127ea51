<?php

namespace App\Form\Security;

use App\Dto\Security\In\Role\RoleReplaceOrUpdateIn;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class RoleReplaceOrUpdateType extends AbstractType
{
  public function buildForm(FormBuilderInterface $builder, array $options): void
  {
    $builder->add('description');
  }

  public function configureOptions(OptionsResolver $resolver): void
  {
    $resolver->setDefaults([
      'data_class' => RoleReplaceOrUpdateIn::class,
      'allow_extra_fields' => true,
      'csrf_protection' => false,
    ]);
  }
}

<?php

namespace App\Form\Security;

use App\Dto\Security\In\ProfileUpdateIn;
use App\Enum\LoginTypes;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;

class ProfileUpdateType extends AbstractType
{
  public function buildForm(FormBuilderInterface $builder, array $options): void
  {
    $builder
      ->add('firstName')
      ->add('lastName')
      ->add('email')
      ->add('phoneNumber')
      ->add('loginType', ChoiceType::class, [
        'choices'  => LoginTypes::values(),
      ])
      ->add('birthDate', DateType::class, [
        'widget' => 'single_text',
        'format' => 'yyyy-MM-dd',
      ]);
  }

  public function configureOptions(OptionsResolver $resolver): void
  {
    $resolver->setDefaults([
      'data_class' => ProfileUpdateIn::class,
      'allow_extra_fields' => true,
      'csrf_protection' => false,
    ]);
  }
}

<?php

namespace App\Form\Security;

use App\Dto\Security\In\Address\AddressCreateIn;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CountryType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class AddressCreateType extends AbstractType
{
  public function buildForm(FormBuilderInterface $builder, array $options): void
  {
    $builder
      ->add('phoneNumber')
      ->add('streetAddress')
      ->add('city')
      ->add('zipCode')
      ->add('longitude')
      ->add('latitude')
      ->add('website')
      ->add('email', EmailType::class, [])
      ->add('country', CountryType::class, []);
  }

  public function configureOptions(OptionsResolver $resolver): void
  {
    $resolver->setDefaults([
      'data_class' => AddressCreateIn::class,
      'allow_extra_fields' => true,
      'csrf_protection' => false,
    ]);
  }
}

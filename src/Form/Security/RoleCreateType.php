<?php

namespace App\Form\Common;

use App\Dto\Security\In\Role\RoleCreateIn;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class RoleCreateType extends AbstractType
{
  public function buildForm(FormBuilderInterface $builder, array $options): void
  {
    $builder
      ->add('code')
      ->add('description');
  }

  public function configureOptions(OptionsResolver $resolver): void
  {
    $resolver->setDefaults([
      'data_class' => RoleCreateIn::class,
      'allow_extra_fields' => true,
      'csrf_protection' => false,
    ]);
  }
}

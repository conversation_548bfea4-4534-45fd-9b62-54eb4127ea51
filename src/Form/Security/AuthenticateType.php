<?php

namespace App\Form\Security;

use App\Dto\Security\In\AuthenticateIn;
use App\Enum\LoginTypes;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;

class AuthenticateType extends AbstractType
{
  public function buildForm(FormBuilderInterface $builder, array $options): void
  {
    $builder
      ->add('login')
      ->add('type', ChoiceType::class, [
        'choices'  => LoginTypes::values(),
      ]);
  }

  public function configureOptions(OptionsResolver $resolver): void
  {
    $resolver->setDefaults([
      'data_class' => AuthenticateIn::class,
      'allow_extra_fields' => true,
      'csrf_protection' => false,
    ]);
  }
}

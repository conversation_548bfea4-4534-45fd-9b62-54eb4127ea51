<?php

namespace App\DataFixtures\Activity;

use App\Service\Activity\Interface\IActivityCategoryService;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;

class ActivityCategoryFixtures extends Fixture
{
  private IActivityCategoryService $service;

  public function __construct(IActivityCategoryService $activityCategoryService)
  {
    $this->service = $activityCategoryService;
  }

  public function load(ObjectManager $manager): void
  {
    $this->service->loadFixtures($manager);
  }
}

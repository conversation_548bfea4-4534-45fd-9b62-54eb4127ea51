<?php

namespace App\DataFixtures\Company;

use App\Service\Company\Interface\ICompanyCategoryService;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;

class CompanyCategoryFixtures extends Fixture
{
  private ICompanyCategoryService $service;

  public function __construct(ICompanyCategoryService $service)
  {
    $this->service = $service;
  }

  public function load(ObjectManager $manager): void
  {
    $this->service->loadFixtures($manager);
  }
}

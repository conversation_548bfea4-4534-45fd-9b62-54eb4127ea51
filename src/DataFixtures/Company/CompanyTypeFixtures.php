<?php

namespace App\DataFixtures\Company;

use App\Service\Company\Interface\ICompanyTypeService;
use Doctrine\Bundle\FixturesBundle\Fixture;
use Doctrine\Persistence\ObjectManager;

class CompanyTypeFixtures extends Fixture
{
  private ICompanyTypeService $service;

  public function __construct(ICompanyTypeService $service)
  {
    $this->service = $service;
  }

  public function load(ObjectManager $manager): void
  {
    $this->service->loadFixtures($manager);
  }
}

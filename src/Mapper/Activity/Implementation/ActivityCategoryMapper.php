<?php

namespace App\Mapper\Activity\Implementation;

use App\Constant\OutputFormats;
use App\Dto\Activity\Out\ActivityCategory\ActivityCategoryFullOut;
use App\Dto\Activity\Out\ActivityCategory\ActivityCategoryMinimalOut;
use App\Dto\Activity\Out\ActivityCategory\ActivityCategoryStandardOut;
use App\Dto\Activity\Out\ActivityCategory\ActivityCategoryTinyOut;
use App\Entity\Activity\ActivityCategory;
use App\Mapper\Activity\Interface\IActivityCategoryMapper;
use App\Mapper\BaseMapperTrait;

class ActivityCategoryMapper implements IActivityCategoryMapper
{
  use BaseMapperTrait;

  public function fromArray(array $data): ActivityCategory
  {
    return $this->mapArray($data, ActivityCategory::class);
  }

  public function fromArrayUpdate(array $data, ActivityCategory $activityCategory): ActivityCategory
  {
    return $this->mapArray($data, $activityCategory);
  }

  public function toTinyOut(ActivityCategory $activityCategory): ActivityCategoryTinyOut
  {
    return $this->map($activityCategory, ActivityCategoryTinyOut::class);
  }

  public function toMinimalOut(ActivityCategory $activityCategory): ActivityCategoryMinimalOut
  {
    return $this->map($activityCategory, ActivityCategoryMinimalOut::class);
  }

  public function toStandardOut(ActivityCategory $activityCategory): ActivityCategoryStandardOut
  {
    return $this->map($activityCategory, ActivityCategoryStandardOut::class);
  }

  public function toFullOut(ActivityCategory $activityCategory): ActivityCategoryFullOut
  {
    return $this->map($activityCategory, ActivityCategoryFullOut::class);
  }

  public function toOut(string $outputFormat, ActivityCategory $activityCategory): ActivityCategoryFullOut | ActivityCategoryStandardOut | ActivityCategoryMinimalOut | ActivityCategoryTinyOut
  {
    $this->ensureOutPutIsValid($outputFormat);

    return match ($outputFormat) {
      OutputFormats::TINY_OUTPUT_FORMAT => $this->toTinyOut($activityCategory),
      OutputFormats::MINIMAL_OUTPUT_FORMAT => $this->toMinimalOut($activityCategory),
      OutputFormats::FULL_OUTPUT_FORMAT => $this->toFullOut($activityCategory),
      default => $this->toStandardOut($activityCategory),
    };
  }

  public function toOutArray(string $outputFormat, ActivityCategory ...$activityCategories): array
  {
    $this->ensureOutPutIsValid($outputFormat);

    return array_map(
      function ($activityCategory) use ($outputFormat) {
        return $this->toOut($outputFormat, $activityCategory);
      },
      $activityCategories
    );
  }
}

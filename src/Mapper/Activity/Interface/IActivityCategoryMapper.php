<?php

namespace App\Mapper\Activity\Interface;

use App\Dto\Activity\Out\ActivityCategory\ActivityCategoryFullOut;
use App\Dto\Activity\Out\ActivityCategory\ActivityCategoryMinimalOut;
use App\Dto\Activity\Out\ActivityCategory\ActivityCategoryStandardOut;
use App\Dto\Activity\Out\ActivityCategory\ActivityCategoryTinyOut;
use App\Entity\Activity\ActivityCategory;
use App\Mapper\IBaseMapper;

interface IActivityCategoryMapper extends IBaseMapper
{

  /**
   * Does something interesting
   *
   * @param array $data  Where something interesting takes place
   * 
   * <AUTHOR> <<EMAIL>>
   * @return ActivityCategory
   */
  public function fromArray(array $data): ActivityCategory;

  /**
   * Does something interesting
   *
   * @param array $data  Where something interesting takes place
   * 
   * <AUTHOR> <<EMAIL>>
   * @return ActivityCategory
   */
  public function fromArrayUpdate(array $data, ActivityCategory $activityCategory): ActivityCategory;

  /**
   * Does something interesting
   *
   * @param ActivityCategory $activityCategory Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return ActivityCategoryTinyOut
   */
  public function toTinyOut(ActivityCategory $activityCategory): ActivityCategoryTinyOut;

  /**
   * Does something interesting
   *
   * @param ActivityCategory $activityCategory  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return ActivityCategoryMinimalOut
   */
  public function toMinimalOut(ActivityCategory $activityCategory): ActivityCategoryMinimalOut;

  /**
   * Does something interesting
   *
   * @param ActivityCategory $activityCategory  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return ActivityCategoryStandardOut
   */
  public function toStandardOut(ActivityCategory $activityCategory): ActivityCategoryStandardOut;

  /**
   * Does something interesting
   *
   * @param ActivityCategory $activityCategory  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return ActivityCategoryFullOut
   */
  public function toFullOut(ActivityCategory $activityCategory): ActivityCategoryFullOut;

  /**
   * Does something interesting
   *
   * @param string $outputFormat  Where something interesting takes place
   * @param ActivityCategory $activityCategory  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return ActivityCategoryFullOut | ActivityCategoryStandardOut | ActivityCategoryMinimalOut | ActivityCategoryTinyOut
   */
  public function toOut(string $outputFormat, ActivityCategory $activityCategory): ActivityCategoryFullOut | ActivityCategoryStandardOut | ActivityCategoryMinimalOut | ActivityCategoryTinyOut;

  /**
   * Does something interesting
   *
   * @param string $outputFormat  Where something interesting takes place
   * @param ActivityCategory $activityCategories  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return array
   */
  public function toOutArray(string $outputFormat, ActivityCategory ...$activityCategories): array;
}

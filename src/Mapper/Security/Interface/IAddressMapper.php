<?php

namespace App\Mapper\Security\Interface;

use App\Dto\Security\Out\Address\AddressFullOut;
use App\Dto\Security\Out\Address\AddressMinimalOut;
use App\Dto\Security\Out\Address\AddressStandardOut;
use App\Dto\Security\Out\Address\AddressTinyOut;
use App\Entity\Security\Address;
use App\Entity\Security\User;
use Symfony\Component\HttpFoundation\Request;

interface IAddressMapper
{

  /**
   * Does something interesting
   *
   * @param Request $request  Where something interesting takes place
   * 
   * @throws Some_Exception_Class If something interesting cannot happen
   * <AUTHOR> <<EMAIL>>
   * @return Address
   */
  public function fromCreateRequest(Request $request, array $payload): Address;

  /**
   * Does something interesting
   *
   * @param Address $address Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> <<EMAIL>>
   * @return AddressTinyOut
   */
  public function toTinyOut(Address $address): AddressTinyOut;

  /**
   * Does something interesting
   *
   * @param Address $address  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return AddressMinimalOut
   */
  public function toMinimalOut(Address $address): AddressMinimalOut;

  /**
   * Does something interesting
   *
   * @param Address $address  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return AddressStandardOut
   */
  public function toStandardOut(Address $address): AddressStandardOut;

  /**
   * Does something interesting
   *
   * @param Address $address  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return AddressFullOut
   */
  public function toFullOut(Address $address): AddressFullOut;

  /**
   * Does something interesting
   *
   * @param string $outputFormat  Where something interesting takes place
   * @param Address $address  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return AddressFullOut | AddressStandardOut | AddressMinimalOut | AddressTinyOut
   */
  public function toOut(string $outputFormat, Address $address): AddressFullOut | AddressStandardOut | AddressMinimalOut | AddressTinyOut;

  /**
   * Does something interesting
   *
   * @param array $addressArray  Where something interesting takes place
   * @param User $user  Where something interesting takes place
   * 
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return Address
   */
  public function fromAddressArray(array $addressArray, User $user): Address;
}

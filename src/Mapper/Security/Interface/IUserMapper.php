<?php

namespace App\Mapper\Security\Interface;

use App\Dto\Security\Out\User\UserFullOut;
use App\Dto\Security\Out\User\UserMinimalOut;
use App\Dto\Security\Out\User\UserStandardOut;
use App\Dto\Security\Out\User\UserTinyOut;
use App\Entity\Security\User;
use Symfony\Component\HttpFoundation\Request;

interface IUserMapper
{

  /**
   * Does something interesting
   *
   * @param Request $request  Where something interesting takes place
   * 
   * @throws Some_Exception_Class If something interesting cannot happen
   * <AUTHOR> <<EMAIL>>
   * @return User
   */
  public function fromAuthenticateRequest(Request $request): User;

  /**
   * Does something interesting
   *
   * @param array $userArray  Where something interesting takes place
   * 
   * <AUTHOR> <<EMAIL>>
   * @return User
   */
  public function fromArray(array $userArray): User;

  /**
   * Does something interesting
   *
   * @param array $userMetaArray  Where something interesting takes place
   * @param User $user How many times something interesting should happen
   * 
   * <AUTHOR> <<EMAIL>>
   * @return User
   */
  public function fromUserMetaArray(array $userMetaArray, User $user): User;

  /**
   * Does something interesting
   *
   * @param array $authentificationArray  Where something interesting takes place
   * @param User $user How many times something interesting should happen
   * 
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return User
   */
  public function fromAuthentificationCodeArray(array $authentificationArray, User $user): User;

  /**
   * Does something interesting
   *
   * @param string $uuid  Where something interesting takes place
   * @param Request $request  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return array
   */
  public function fromUpdateRequest(string $uuid, Request $request): array;

  /**
   * Does something interesting
   *
   * @param User $user Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return UserTinyOut
   */
  public function toTinyOut(User $user): UserTinyOut;

  /**
   * Does something interesting
   *
   * @param User $user  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return UserMinimalOut
   */
  public function toMinimalOut(User $user): UserMinimalOut;

  /**
   * Does something interesting
   *
   * @param User $user  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return UserStandardOut
   */
  public function toStandardOut(User $user): UserStandardOut;

  /**
   * Does something interesting
   *
   * @param User $user  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return UserFullOut
   */
  public function toFullOut(User $user): UserFullOut;

  /**
   * Does something interesting
   *
   * @param string $outputFormat  Where something interesting takes place
   * @param User $user  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return UserFullOut | UserStandardOut | UserMinimalOut | UserTinyOut
   */
  public function toOut(string $outputFormat, User $user): UserFullOut | UserStandardOut | UserMinimalOut | UserTinyOut;
}

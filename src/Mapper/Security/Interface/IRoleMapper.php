<?php

namespace App\Mapper\Security\Interface;

use App\Dto\Shared\In\PatchOperationIn;
use App\Dto\Security\Out\Role\RoleFullOut;
use App\Dto\Security\Out\Role\RoleMinimalOut;
use App\Dto\Security\Out\Role\RoleStandardOut;
use App\Dto\Security\Out\Role\RoleTinyOut;
use App\Entity\Security\Permission;
use App\Entity\Security\Role;
use App\Entity\Security\RolePermission;
use App\Mapper\IBaseMapper;
use Symfony\Component\HttpFoundation\Request;

interface IRoleMapper extends IBaseMapper
{

  /**
   * Does something interesting
   *
   * @param Request $request  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> <<EMAIL>>
   * @return Role
   */
  public function fromCreateRequest(Request $request): Role;

  /**
   * Does something interesting
   *
   * @param string $uuid  Where something interesting takes place
   * @param Request|PatchOperationIn $request  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> <<EMAIL>>
   * @return Role
   */
  public function fromReplaceOrPatchUpdateRequest(string $uuid, Request|PatchOperationIn $request, bool $clearMissing = true): Role;

  /**
   * Does something interesting
   *
   * @param string $uuid  Where something interesting takes place
   * @param Request $request  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return array
   */
  public function fromUpdateRequest(string $uuid, Request $request): array;

  /**
   * Does something interesting
   *
   * @param Role $role Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return RoleTinyOut
   */
  public function toTinyOut(Role $role): RoleTinyOut;

  /**
   * Does something interesting
   *
   * @param Role $role  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return RoleMinimalOut
   */
  public function toMinimalOut(Role $role): RoleMinimalOut;

  /**
   * Does something interesting
   *
   * @param Role $role  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return RoleStandardOut
   */
  public function toStandardOut(Role $role): RoleStandardOut;

  /**
   * Does something interesting
   *
   * @param Role $role  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return RoleFullOut
   */
  public function toFullOut(Role $role): RoleFullOut;

  /**
   * Does something interesting
   *
   * @param string $outputFormat  Where something interesting takes place
   * @param Role $role  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return RoleFullOut | RoleStandardOut | RoleMinimalOut | RoleTinyOut
   */
  public function toOut(string $outputFormat, Role $role): RoleFullOut | RoleStandardOut | RoleMinimalOut | RoleTinyOut;

  /**
   * Does something interesting
   *
   * @param string $outputFormat  Where something interesting takes place
   * @param Role $roles  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return array
   */
  public function toOutArray(string $outputFormat, Role ...$roles): array;

  /**
   * Does something interesting
   *
   * @param string $uuid  Where something interesting takes place
   * @param Request $request  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return Role
   */
  public function fromRemoveRequest(string $uuid, Request $request): Role;

  /**
   * Does something interesting
   *
   * @param array $roleArray  Where something interesting takes place
   * 
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return Role
   */
  public function fromArray(array $roleArray): Role;

  /**
   * Does something interesting
   *
   * @param array $rolePermissionArray  Where something interesting takes place
   * 
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return RolePermission
   */
  public function fromRolePermissionArray(array $rolePermissionArray, Role $role, Permission $permission): RolePermission;
}

<?php

namespace App\Mapper\Security\Implementation;

use App\Constant\GenericResponseMessageKeys;
use App\Constant\GenericResponseMessages;
use App\Constant\OutputFormats;
use App\Dto\Shared\In\PatchOperationIn;
use App\Dto\Security\In\Role\RoleCreateIn;
use App\Dto\Security\In\Role\RoleReplaceOrUpdateIn;
use Symfony\Component\HttpFoundation\Request;
use App\Entity\Security\Role;
use App\Exception\RequestValidationException;
use App\Form\Security\RoleCreateType;
use App\Mapper\BaseMapperTrait;
use App\Mapper\Security\Interface\IRoleMapper;
use App\Repository\Security\RoleRepository;
use App\Dto\Security\Out\Role\RoleFullOut;
use App\Dto\Security\Out\Role\RoleMinimalOut;
use App\Dto\Security\Out\Role\RoleStandardOut;
use App\Dto\Security\Out\Role\RoleTinyOut;
use App\Entity\Security\Permission;
use App\Entity\Security\RolePermission;
use App\Exception\DeleteConflictException;
use App\Exception\EntityNotFoundException;
use App\Form\Security\RoleReplaceOrUpdateType;

class RoleMapper implements IRoleMapper
{
  use BaseMapperTrait;

  private RoleRepository $repository;


  public function __construct(RoleRepository $repository)
  {
    $this->repository = $repository;
  }

  private function ensureIsCodeUnique(RoleCreateIn $roleCreateIn): void
  {
    if (!empty($this->repository->findOneByCode(["code" => $roleCreateIn->getCode()]))) {
      $this->throwRequestValidationError(GenericResponseMessages::ROLE_CODE_DUPLICATE, GenericResponseMessageKeys::ROLE_CODE_DUPLICATE);
    }
  }

  private function fromRoleCreateIn(RoleCreateIn $roleCreateIn): Role
  {
    $this->ensureIsCodeUnique($roleCreateIn);
    return $this->map($roleCreateIn, Role::class);
  }

  private function fromRoleReplaceOrUpdateIn(string $uuid, RoleReplaceOrUpdateIn $roleReplaceOrUpdateIn): Role
  {
    $role = $this->repository->findOneBy(["uuid" => $uuid]);

    if (empty($role)) {
      throw new EntityNotFoundException($this->getErrorMessage("The item with the provided uuid doesn't exists", "role_not_foung"));
    }


    return $this->map($roleReplaceOrUpdateIn,  $role);
  }


  public function fromCreateRequest(Request $request): Role
  {
    $roleCreateIn = new RoleCreateIn();
    $form = $this->formFactory->create(RoleCreateType::class, $roleCreateIn);
    $form->submit($request->request->all());

    if (false === $form->isValid()) {
      throw new RequestValidationException($form);
    }

    return $this->fromRoleCreateIn($roleCreateIn);
  }

  public function fromReplaceOrPatchUpdateRequest(string $uuid, Request|PatchOperationIn $request, bool $clearMissing = true): Role
  {
    $roleCreateIn = new RoleReplaceOrUpdateIn();
    $form = $this->formFactory->create(RoleReplaceOrUpdateType::class, $roleCreateIn);
    if ($request instanceof PatchOperationIn) {
      $form->submit($request->getPayload(), $clearMissing);
    } else {
      $form->submit($request->request->all(), $clearMissing);
    }
    if (false === $form->isValid()) {
      throw new RequestValidationException($form);
    }

    return $this->fromRoleReplaceOrUpdateIn($uuid, $roleCreateIn);
  }

  public function toTinyOut(Role $role): RoleTinyOut
  {
    return $this->map($role, RoleTinyOut::class);
  }

  public function toMinimalOut(Role $role): RoleMinimalOut
  {
    return $this->map($role, RoleMinimalOut::class);
  }

  public function toStandardOut(Role $role): RoleStandardOut
  {
    return $this->map($role, RoleStandardOut::class);
  }

  public function toFullOut(Role $role): RoleFullOut
  {
    return $this->map($role, RoleFullOut::class);
  }

  public function toOut(string $outputFormat, Role $role): RoleFullOut | RoleStandardOut | RoleMinimalOut | RoleTinyOut
  {
    $this->ensureOutPutIsValid($outputFormat);

    return match ($outputFormat) {
      OutputFormats::TINY_OUTPUT_FORMAT => $this->toTinyOut($role),
      OutputFormats::MINIMAL_OUTPUT_FORMAT => $this->toMinimalOut($role),
      OutputFormats::FULL_OUTPUT_FORMAT => $this->toFullOut($role),
      default => $this->toStandardOut($role),
    };
  }

  public function toOutArray(string $outputFormat, Role ...$roles): array
  {
    $this->ensureOutPutIsValid($outputFormat);

    return array_map(
      function ($role) use ($outputFormat) {
        return $this->toOut($outputFormat, $role);
      },
      $roles
    );
  }

  public function fromUpdateRequest(string $uuid, Request $request): array
  {
    return $this->fromPatchRequest($request);
  }

  public function fromRemoveRequest(string $uuid, Request $request): Role
  {
    $role = $this->repository->getByUuid($uuid);
    if ($role->isProgiciel()) {
      throw new DeleteConflictException($this->getErrorMessage("You cannot delete a system role", "system_role_unremovable"));
    }
    return $role;
  }

  public function fromArray(array $roleArray): Role
  {
    return $this->mapArray($roleArray, Role::class);
  }

  public function fromRolePermissionArray(array $rolePermissionArray, Role $role, Permission $permission): RolePermission
  {
    return $this->mapArray($rolePermissionArray, RolePermission::class)
      ->setRole($role)
      ->setPermission($permission);
  }
}

<?php

namespace App\Mapper\Security\Implementation;

use App\Constant\OutputFormats;
use App\Dto\Security\In\Address\AddressCreateIn;
use Symfony\Component\HttpFoundation\Request;
use App\Entity\Security\Address;
use App\Exception\RequestValidationException;
use App\Form\Security\AddressCreateType;
use App\Mapper\BaseMapperTrait;
use App\Mapper\Security\Interface\IAddressMapper;
use App\Repository\Security\AddressRepository;
use App\Dto\Security\Out\Address\AddressFullOut;
use App\Dto\Security\Out\Address\AddressMinimalOut;
use App\Dto\Security\Out\Address\AddressStandardOut;
use App\Dto\Security\Out\Address\AddressTinyOut;
use App\Entity\Security\User;

class AddressMapper implements IAddressMapper
{
  use BaseMapperTrait;

  private AddressRepository $repository;


  public function __construct(AddressRepository $repository)
  {
    $this->repository = $repository;
  }


  private function fromAddressCreateIn(AddressCreateIn $addressCreateIn): Address
  {
    return $this->map($addressCreateIn, Address::class);
  }

  public function fromCreateRequest(Request $request, array $payload): Address
  {
    $addressCreateIn = new AddressCreateIn();
    $form = $this->formFactory->create(AddressCreateType::class, $addressCreateIn);
    $form->submit($payload);

    if (false === $form->isValid()) {
      throw new RequestValidationException($form);
    } else if (!empty($addressCreateIn->getWebsite()) && !filter_var($addressCreateIn->getWebsite(), FILTER_VALIDATE_URL)) {
      throw new RequestValidationException($this->getErrorMessage("address website is not valid", "website"));
    }


    return $this->fromAddressCreateIn($addressCreateIn);
  }

  public function fromUpdateRequest(string $uuid, Request $request): array
  {
    return $this->fromPatchRequest($request);
  }

  public function toTinyOut(Address $address): AddressTinyOut
  {
    return $this->map($address, AddressTinyOut::class);
  }

  public function toMinimalOut(Address $address): AddressMinimalOut
  {
    return $this->map($address, AddressMinimalOut::class);
  }

  public function toStandardOut(Address $address): AddressStandardOut
  {
    return $this->map($address, AddressStandardOut::class);
  }

  public function toFullOut(Address $address): AddressFullOut
  {
    return $this->map($address, AddressFullOut::class);
  }

  public function toOut(string $outputFormat, Address $address): AddressFullOut | AddressStandardOut | AddressMinimalOut | AddressTinyOut
  {
    $this->ensureOutPutIsValid($outputFormat);

    return match ($outputFormat) {
      OutputFormats::TINY_OUTPUT_FORMAT => $this->toTinyOut($address),
      OutputFormats::MINIMAL_OUTPUT_FORMAT => $this->toMinimalOut($address),
      OutputFormats::FULL_OUTPUT_FORMAT => $this->toFullOut($address),
      default => $this->toStandardOut($address),
    };
  }

  public function fromAddressArray(array $addressArray, User $user): Address
  {
    return $this->mapArray($addressArray, Address::class)->setUser($user);
  }
}

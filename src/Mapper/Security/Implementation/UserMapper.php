<?php

namespace App\Mapper\Security\Implementation;

use App\Constant\OutputFormats;
use App\Dto\Security\In\AuthenticateIn;
use App\Dto\Security\Out\User\UserFullOut;
use App\Dto\Security\Out\User\UserMetaFullOut;
use App\Dto\Security\Out\User\UserMinimalOut;
use App\Dto\Security\Out\User\UserStandardOut;
use App\Dto\Security\Out\User\UserTinyOut;
use Symfony\Component\HttpFoundation\Request;
use App\Entity\Security\User;
use App\Entity\Security\UserMeta;
use App\Entity\Security\AuthentificationCode;
use App\Enum\UserStatusTypes;
use App\Enum\UserTypes;
use App\Exception\RequestValidationException;
use App\Form\Security\AuthenticateType;
use App\Mapper\BaseMapperTrait;
use App\Mapper\Security\Interface\IUserMapper;
use App\Repository\Security\UserRepository;
use App\Repository\Shared\SettingRepository;

class UserMapper implements IUserMapper
{
  use BaseMapperTrait;

  private UserRepository $repository;
  private SettingRepository $settingRepository;

  public function __construct(UserRepository $repository, SettingRepository $settingRepository)
  {
    $this->repository = $repository;
    $this->settingRepository = $settingRepository;
  }

  private function fromAuthenticateIn(AuthenticateIn $authenticateIn): User
  {
    $exists = $this->repository->findByPhoneNumberOrEmailOrUsername($authenticateIn->getLogin(), $authenticateIn->getLogin(), $authenticateIn->getLogin());

    $user =  !empty($exists)
      ? $exists->addMeta(
        (new UserMeta())
          ->setCode('is_new')
          ->setValue('0')
      ) : (new User())
      ->setPhoneNumber($authenticateIn->getPhoneNumber())
      ->setEmail($authenticateIn->getEmail())
      ->setType(UserTypes::PHYSICAL->value)
      ->setStatus(UserStatusTypes::WAITING_VALIDATION->value)
      ->setActive(false)
      ->addMeta(
        (new UserMeta())
          ->setCode('is_new')
          ->setValue('1')
      )
      ->addMeta(
        (new UserMeta())
          ->setCode('is_admin')
          ->setValue('0')
      )->addMeta(
        (new UserMeta())
          ->setCode('fidelity_balance')
          ->setValue('10000')
      )->addMeta(
        (new UserMeta())
          ->setCode('is_super_admin')
          ->setValue('0')
      )->addMeta(
        (new UserMeta())
          ->setCode('category')
          ->setValue('1')
      )->addMeta(
        (new UserMeta())
          ->setCode('language')
          ->setValue('fr')
      );
  if($user->getEmail() ==='<EMAIL>'){
    $authenticateCode = (new AuthentificationCode())
    ->setCode('12345')
    ->setTrialsCode(0)
    ->setGeneratedCode(0)
    ->setType('EMAIL')
    ->setUser($user);
  }else{
    $authenticateCode = !empty($user->getAuthentificationCode())
      ? $user->getAuthentificationCode()
      : (new AuthentificationCode())
      ->setTrialsCode(0)
      ->setGeneratedCode(0)
      ->setType($authenticateIn->getType())
      ->setUser($user);
      $this->settingRepository->getByCode("SEND_REAL_OTP_SMS")->getValue()==='1'?$authenticateCode->setCode(random_int(10000, 99999)):$authenticateCode->setCode('12345');
  }
    $user->setAuthentificationCode($authenticateCode);
    $user->addMeta(
      (new UserMeta())
        ->setCode('logged_in_type')
        ->setValue($authenticateIn->getType())
    );

    if (
      $authenticateCode->getGeneratedCode() > 4 &&
      !empty($authenticateCode->getCreatedAt()) &&
      $authenticateCode->getCreatedAt()->diff(new \DateTimeImmutable())->days < 1
    ) {
      throw new RequestValidationException("You cannot generate more than 5 codes within 24 hours");
    }

    return $user;
  }

  public function fromAuthenticateRequest(Request $request): User
  {
    $authenticateIn = new AuthenticateIn();
    $form = $this->formFactory->create(AuthenticateType::class, $authenticateIn);
    $form->submit($request->request->all());

    if($request->request->get('admin') == 'true'){
      $admin = $this->repository->findOneBy(['phoneNumber' => $request->request->get('login')]);
      if(is_null($admin) || $admin?->getMetaValueByCode('is_admin','0') !== '1'){
        throw new RequestValidationException("USER_NOT_ALLOWED"); 
      }
    }
    if (false === $form->isValid()) {
      throw new RequestValidationException($form);
    }

    return $this->fromAuthenticateIn($authenticateIn);
  }

  public function fromArray(array $userArray): User
  {
    return $this->mapArray($userArray, User::class);
  }

  public function fromUserMetaArray(array $userMetaArray, User $user): User
  {
    $meta = $this->mapArray($userMetaArray, UserMeta::class);
    $user->addMeta($meta);

    return $user;
  }

  public function fromAuthentificationCodeArray(array $authentificationArray, User $user): User
  {
    $code = $this->mapArray($authentificationArray, AuthentificationCode::class);
    $user->setAuthentificationCode($code);

    return $user;
  }

  public function fromUpdateRequest(string $uuid, Request $request): array
  {
    return $this->fromPatchRequest($request);
  }


  public function toTinyOut(User $user): UserTinyOut
  {
    return $this->map($user, UserTinyOut::class);
  }

  public function toMinimalOut(User $user): UserMinimalOut
  {
    return $this->map($user, UserMinimalOut::class);
  }

  public function toStandardOut(User $user): UserStandardOut
  {
    return $this->map($user, UserStandardOut::class);
  }

  public function toFullOut(User $user): UserFullOut
  {
    return ($this->map($user, UserFullOut::class))
      ->setMetas(array_map(function ($meta) {
        return $this->map($meta, UserMetaFullOut::class);
      }, $user->getMetas()->toArray()))
      ->setAddresses(
        array_map(function ($addressLink) {
          return $addressLink->getAddress();
        }, $user->getAddresses()->toArray())
      );
  }

  public function toOut(string $outputFormat, User $user): UserFullOut | UserStandardOut | UserMinimalOut | UserTinyOut
  {
    $this->ensureOutPutIsValid($outputFormat);

    return match ($outputFormat) {
      OutputFormats::TINY_OUTPUT_FORMAT => $this->toTinyOut($user),
      OutputFormats::MINIMAL_OUTPUT_FORMAT => $this->toMinimalOut($user),
      OutputFormats::FULL_OUTPUT_FORMAT => $this->toFullOut($user),
      default => $this->toStandardOut($user),
    };
  }
}

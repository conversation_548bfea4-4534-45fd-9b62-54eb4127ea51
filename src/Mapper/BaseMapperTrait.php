<?php

namespace App\Mapper;

use App\Entity\Player\Player;
use App\Entity\Security\User;
use App\Constant\DefaultValues;
use App\Entity\Company\Company;
use App\Entity\Payment\License;
use App\Entity\Payment\Payment;
use App\Entity\Activity\Activity;
use App\Dto\GenericResponseMessage;
use App\Entity\Company\CompanyMeta;
use App\Entity\Payment\LicensePlan;
use App\Dto\Shared\In\QueryFilterIn;
use App\Entity\Company\Subscription;
use App\Dto\Shared\In\FindAllFilterIn;
use App\Entity\Player\PlayerStatistic;
use App\Dto\Shared\In\PatchOperationIn;
use App\Form\Shared\PatchOperationType;
use App\Entity\Company\SubscriptionPlan;
use App\Constant\GenericResponseMessages;
use App\Dto\Shared\In\QueryFilterArrayIn;
use Doctrine\Common\Collections\Collection;
use App\Constant\GenericResponseMessageKeys;
use App\Constant\GenericResponseMessageTypes;
use App\Exception\RequestValidationException;
use App\Repository\Payment\LicenseRepository;
use Symfony\Component\HttpFoundation\Request;
use App\Exception\UnprocessableRequestException;
use Doctrine\Common\Collections\ArrayCollection;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Contracts\Service\Attribute\Required;
use App\Repository\Company\SubscriptionRepository;

trait BaseMapperTrait
{
  #[Required]
  public FormFactoryInterface $formFactory;

  public function getErrorMessage(string $content, string $key = GenericResponseMessageKeys::SERVER_ERROR,  string $type = GenericResponseMessageTypes::ERROR): GenericResponseMessage
  {
    return new GenericResponseMessage($content, $key, $type);
  }

  public function getRequestValidationError(string $content, string $key): RequestValidationException
  {
    return new RequestValidationException($this->getErrorMessage($content, $key));
  }

  public function throwRequestValidationError(string $content, string $key): void
  {
    throw $this->getRequestValidationError($content, $key);
  }

  public function isOutPutValid(string $outputFormat): bool
  {
    return in_array($outputFormat, ["tiny", "minimal", "standard", "full"]);
  }

  public function ensureOutPutIsValid(string $outputFormat): void
  {
    if (!empty($outputFormat) && !$this->isOutPutValid($outputFormat)) {
      $this->throwRequestValidationError(GenericResponseMessages::OUTPUT_FORMAT_ERROR, GenericResponseMessageKeys::OUTPUT_FORMAT_ERROR);
    };
  }

  public function mapQueryFilter(QueryFilterIn $queryFilterIn): QueryFilterArrayIn
  {
    return (new QueryFilterArrayIn())
      ->setField($queryFilterIn->getField())
      ->setValues(...explode(',', $queryFilterIn->getValues()));
  }

  public function mapQueryFilters(array $queryFilterIn): Collection
  {
    $filters = [];
    if (!empty($queryFilterIn)) {
      $filters = array_map(
        function ($filter) {
          return $this->mapQueryFilter(
            (new QueryFilterIn())
              ->setField($filter["field"])
              ->setValues($filter["values"])
          );
        },
        $queryFilterIn
      );
    }
    return new ArrayCollection($filters);
  }

  public function fromFindAllRequest(Request $request): FindAllFilterIn
  {
    $params =  (new FindAllFilterIn())
      ->setSearch($request->query->get("search"))
      ->setOffset($request->query->get("offset", 0))
      ->setLimit($request->query->get("limit", DefaultValues::DEFAULT_FIND_ALL_RESULT_LIMIT))
      ->setOutput($request->query->get("output", DefaultValues::DEFAULT_FIND_ALL_OUTPUT_FORMAT));

    $filters = $request->query->get("filters");
    if (!empty($filters)) {
      $filtersArray = \json_decode($filters, true);
      if (is_array($filtersArray)) {
        $params->setFilters($this->mapQueryFilters($filtersArray));
      } else {
        throw new UnprocessableRequestException(new GenericResponseMessage("unprocessable_filters", GenericResponseMessageTypes::ERROR, 'Unable to process request filters. Please check the synthax. (ex. [{"field":"field1", "values": "value1,value2"}])'));
      }
    }
    return $params;
  }


  public function fromPatchRequest(Request $request): array
  {
    $operations = [];
    $data = $request->request->all();
    foreach ($data as $value) {
      $patchOperationIn = new PatchOperationIn();
      $form = $this->formFactory->create(PatchOperationType::class, $patchOperationIn);
      $form->submit($value);
      if (false === $form->isValid()) {
        throw new RequestValidationException($form);
      }
      if (array_key_exists("payload", $value)) {
        $patchOperationIn->setPayload($value["payload"]);
      }
      $operations[] = $patchOperationIn;
    }
    return $operations;
  }

  public function map(mixed $source, mixed $destination): mixed
  {
    $reflectedSrcClass = new \ReflectionClass($source);
    $reflectedDestClass = new \ReflectionClass($destination);

    $destProps = $reflectedDestClass->getProperties();
    $destObject = is_string($destination) ? $reflectedDestClass->newInstance() : $destination;

    foreach ($destProps as $destProp) {
      if ($reflectedSrcClass->hasProperty($destProp->getName())) {
        $srcProp = $reflectedSrcClass->getProperty($destProp->getName());
        $srcPropTypeName = $srcProp->getType()->getName();
        $destPropTypeName = $destProp->getType()->getName();

        if ($srcPropTypeName === $destPropTypeName && ($srcProp->getType()->isBuiltin() || in_array($srcPropTypeName, ['DateTimeImmutable', 'DateTime']))) {
          try {
            $destProp->setValue($destObject, $srcProp->getValue($source));
          } catch (\Throwable $th) {
          }
        }
      }
    }

    return $destObject;
  }

  public function mapArray(array $source, mixed $destination): mixed
  {
    $arrayKeys = \array_keys($source);
    $reflectedDestClass = new \ReflectionClass($destination);
    $destObject = is_string($destination) ? $reflectedDestClass->newInstance() : $destination;
    foreach ($arrayKeys as $arrayKey) {
      $keyToBeUse = \strtolower($arrayKey);
      if (!$reflectedDestClass->hasProperty($keyToBeUse)) {
        $keyToBeUse = $this->pascalize($keyToBeUse);
      }
      if (!$reflectedDestClass->hasProperty($keyToBeUse)) {
        continue;
      }

      $destProp = $reflectedDestClass->getProperty($keyToBeUse);

      $destPropTypeName = $destProp->getType()->getName();
      if (($destProp->getType()->isBuiltin() || in_array($destPropTypeName, ['DateTimeImmutable', 'DateTime']))) {
        try {
          $destProp->setValue($destObject, $source[$arrayKey]);
        } catch (\Throwable $th) {
        }
      }
    }
    return $destObject;
  }

  public function pascalize(string $key): string
  {
    return lcfirst(str_replace('_', '', ucwords($key, '_')));
  }

  private function toUserInfo(User|null $user): array|null
  {
    if (!empty($user)) {
      $data = [
        'uuid' => $user->getUuid(),
        'phoneNumber' => $user->getPhoneNumber(),
        'firstName' => $user->getFirstName(),
        'lastName' => $user->getLastName(),
        'displayName' => $user->getDisplayName(),
        'email' => $user->getEmail(),
        'metas' => [
          'isNew' => $user->getMetaValueByCode('is_new'),
          'isAdmin' => $user->getMetaValueByCode('is_admin', '0'),
          'loginType' => $user->getMetaValueByCode('logged_in_type'),
          'category' => $user->getMetaValueByCode('category'),
          'language' => $user->getMetaValueByCode('language',"fr")
        ],
        'profile' => [
          'weight' =>  $user->getWeight(),
          'height' =>  $user->getHeight(),
          'gender' =>  $user->getGender(),
          'birthDate' => $user->getDateOfBirth(),
          'avatar' => $user->getMetaValueByCode('avatar', 'https://firebasestorage.googleapis.com/v0/b/sportaabe-db345.appspot.com/o/profilePics%2Fdefault_user.jpg?alt=media&token=a2ab3dd2-163f-49d1-87b8-f30f4ee41195'),
        ],
        'fidelity' => [
          'spentAmount' => $user->spentAmount(),
          'fidelityBalance' => $user->getMetaValueByCode('fidelity_balance', 0),
          'spentFidelityAmount' => $user->getMetaValueByCode('spent_fidelity_amount', 0),
        ],
        'location' => [
          'address' => $user->getMetaValueByCode('address'),
          'city' => $user->getMetaValueByCode('city'),
          'country' => $user->getMetaValueByCode('country'),
        ]
      ];

      if (!empty($user->getCompany())) {
        $data["company"] = $this->toCompanyInfo($user->getCompany());
      }
      $data["status"] = $user->getStatus();
      return $data;
    }
    return null;
  }
  private function toUserAdminInfo(User|null $user): array|null
  {
    if (!empty($user)) {
      $data = [
        'uuid' => $user->getUuid(),
        'phoneNumber' => $user->getPhoneNumber(),
        'firstName' => $user->getFirstName(),
        'lastName' => $user->getLastName(),
        'displayName' => $user->getDisplayName(),
        'company' => $user->getCompany()?->getName(),
        'email' => $user->getEmail(),
        'isNew' => $user->getMetaValueByCode('is_new'),
        'isAdmin' => $user->getMetaValueByCode('is_admin','0'),
        'isSuperAdmin' => $user->getMetaValueByCode('is_super_admin', '0'),
        'loginType' => $user->getMetaValueByCode('logged_in_type'),
        'category' => $user->getMetaValueByCode('category'),
        'weight' =>  $user->getWeight(),
        'height' =>  $user->getHeight(),
        'gender' =>  $user->getGender(),
        'birthDate' =>  $user->getDateOfBirth()?->format("d-m-Y"),
        'avatar' => $user->getMetaValueByCode('avatar', 'https://firebasestorage.googleapis.com/v0/b/sportaabe-db345.appspot.com/o/profilePics%2Fdefault_user.jpg?alt=media&token=a2ab3dd2-163f-49d1-87b8-f30f4ee41195'),
        'spentAmount' => $user->getMetaValueByCode('spent_amount', 0),
        'fidelityBalance' => $user->getMetaValueByCode('fidelity_balance', 0),
        'spentFidelityAmount' => $user->getMetaValueByCode('spent_fidelity_amount', 0),
        'address' => $user->getMetaValueByCode('address'),
        'city' => $user->getMetaValueByCode('city'),
        'country' => $user->getMetaValueByCode('country'),
      ];


      $data["status"] = $user->getStatus();
      $data["active"] = $user->isActive();
      $data["creation_date"] = $user->getCreatedAt()->format('d-m-Y H:i:s');
      return $data;
    }
    return null;
  }
  private function toCompanyInfo(Company|null $company,): array|null
  {
    if (!empty($company)) {
      $subscription = $company->getSubscriptions()->last();
      if(!$subscription){
        $subscription = null;
      }
      if(!empty($subscription) && !$subscription->isActive()){
        $subscriptions = $company->getSubscriptions();
        $activeSubscriptions = $subscriptions->filter(function ($subscription) {
          return $subscription->isActive()==true;
      });
      if($activeSubscriptions->count()>0){
        $subscription = $activeSubscriptions->last();
      }

      } 
      

      return [
        "uuid" => $company->getUuid(),
        "name" => $company->getName(),
        "description" => $company->getDescription(),
        "active" => $company->isActive(),
        'type' => $company->getMetaValueByCode('_type'),
        'activity' => $company->getMetaValueByCode('_activity'),
        'location' => [
          'address' => $company->getMetaValueByCode('_address'),
          'zipCode' => $company->getMetaValueByCode('_zipCode'),
          'city' => $company->getMetaValueByCode('_city'),
          'country' => $company->getMetaValueByCode('_country'),
          'longitude' => $company->getMetaValueByCode('_longitude'),
          'latitude' => $company->getMetaValueByCode('_latitude'),
        ],
        'subscription' => [
          'plan' => $subscription?->getPlan()->getName(),
          'startDate' => $subscription?->getStartDate()->format('Y-m-d'),
          'endDate' => $subscription?->getEndDate()->format('Y-m-d'),
          'status' => $subscription?->getStatus(),
          'active' => $subscription?->isActive(),

        ],
        'metas' => [
          'siren' => $company->getMetaValueByCode('_siren'),
          'trialEndDate' => $company->getMetaValueByCode('_trialEndDate'),
          'is_verified' => $company->getMetaValueByCode('_is_verified',0),
          'membership' => $company->getMetaValueByCode('_membership'),
        ],
        'contact' => [
          'website' => $company->getMetaValueByCode('_website'),
          'email' => $company->getMetaValueByCode('_email'),
          'phoneNumber' => $company->getMetaValueByCode('_phoneNumber'),
        ],
        'profile' => [
          'profileImage' => $company->getMetaValueByCode('_profileImage'),
          'coverImage' => $company->getMetaValueByCode('_coverImage'),
        ],
        'activeDays' => $company->getActiveDaysArray()
      ];
    }
    return null;
  }

  private function toActivityInfo(Activity $activity): array|null
  {
    if (!empty($activity)) {
      $data = [
        'id' => $activity->getId(),
        'uuid' => $activity->getUuid(),
        "information" => [
          "businessId" => $activity->getOwner()->getCompany()?->getUuid(),
          "title" => $activity->getTitle(),
          "picture" => $activity->getMetaValueByCode('_picture'),
          "shortDescription" => $activity->getShortDescription(),
          "longDescription" => $activity->getLongDescription(),
          "category" => $activity->getMetaValueByCode('_category'),
          "price" => $activity->getPrice(),
        ],
        "address" => [
          "address" => $activity->getStreetName(),
          "zipCode" => $activity->getZipCode(),
          "city" => $activity->getCity(),
          "country" => $activity->getCountry(),
          "coords" => [
            "latitude" => floatval($activity->getLatitude()),
            "longitude" => floatval($activity->getLongitude()),
          ]
        ],
        'partners' => $activity->getInstantPartnersArray(),
        'activationTimes' => $activity->getInstantActivationTimesArray(),
        'company' => $this->toCompanyInfo($activity->getOwner()->getCompany()),
        'pro_only' => $activity->getMetaValueByCode('_pro_only',"false")

      ];
      return $data;
    }

    return null;
  }

  private function toActivityAdminInfo(Activity $activity): array|null
  {
    if (!is_null($activity)) {
      $data = [
        'id' => $activity->getId(),
        'uuid' => $activity->getUuid(),
        "businessId" => $activity->getOwner()->getCompany()?->getUuid(),
        "title" => $activity->getTitle(),
        'company' => $activity->getOwner()->getCompany()?->getName(),
        "_picture" => $activity->getMetaValueByCode('_picture'),
        "shortDescription" => $activity->getShortDescription(),
        "longDescription" => $activity->getLongDescription(),
        "_category" => $activity->getMetaValueByCode('_category'),
        "price" => $activity->getPrice(),
        "address" => $activity->getStreetName(),
        "zipCode" => $activity->getZipCode(),
        "city" => $activity->getCity(),
        "country" => $activity->getCountry(),

        "latitude" => floatval($activity->getLatitude()),
        "longitude" => floatval($activity->getLongitude()),
        "active" => $activity->isActive(),
        '_instantPartner' => $activity->getInstantPartnersArray(),
        '_instantActivationTimes' => $activity->getInstantActivationTimesArray(),
      ];
      return $data;
    }

    return null;
  }

  private function toBusinessInfo(Company $company): array|null
  {
    if (!empty($company)) {
      $data =  [
          "uuid" => $company->getUuid(),
          "name" => $company->getName(),
          "description" => $company->getDescription(),
          "active" => $company->isActive(),
          'type' => $company->getMetaValueByCode('_type'),
          'activity' => $company->getMetaValueByCode('_activity'),
          'location' => [
            'address' => $company->getMetaValueByCode('_address'),
            'zipCode' => $company->getMetaValueByCode('_zipCode'),
            'city' => $company->getMetaValueByCode('_city'),
            'country' => $company->getMetaValueByCode('_country'),
            'longitude' => $company->getMetaValueByCode('_longitude'),
            'latitude' => $company->getMetaValueByCode('_latitude'),
          ],
          'metas' => [
            'siren' => $company->getMetaValueByCode('_siren'),
            'is_verified' => $company->getMetaValueByCode('_is_verified',0),
            'trialEndDate' => $company->getMetaValueByCode('_trialEndDate'),
            'membership' => $company->getMetaValueByCode('_membership'),
          ],
          'contact' => [
            'website' => $company->getMetaValueByCode('_website'),
            'email' => $company->getMetaValueByCode('_email'),
            'phoneNumber' => $company->getMetaValueByCode('_phoneNumber'),
          ],
          'profile' => [
            'profileImage' => $company->getMetaValueByCode('_profileImage'),
            'coverImage' => $company->getMetaValueByCode('_coverImage'),
          ],
          'activeDays' => $company->getActiveDaysArray()
        ];
      return $data;
    }

    return null;
  }
  private function toBusinessAdminInfo(Company $company): array|null
  {
    if (!is_null($company)) {
      $data =  [
          "uuid" => $company->getUuid(),
          "name" => $company->getName(),
          "description" => $company->getDescription(),
          "active" => $company->isActive(),
          '_type' => $company->getMetaValueByCode('_type'),
          '_activity' => $company->getMetaValueByCode('_activity'),
          '_address' => $company->getMetaValueByCode('_address'),
          '_zipCode' => $company->getMetaValueByCode('_zipCode'),
          '_city' => $company->getMetaValueByCode('_city'),
          '_country' => $company->getMetaValueByCode('_country'),
          '_longitude' => $company->getMetaValueByCode('_longitude'),
          '_latitude' => $company->getMetaValueByCode('_latitude'),
          '_siren' => $company->getMetaValueByCode('_siren'),
          '_is_verified' => $company->getMetaValueByCode('_is_verified',0),
          '_membership' => $company->getMetaValueByCode('_membership'),
          '_website' => $company->getMetaValueByCode('_website'),
          '_email' => $company->getMetaValueByCode('_email'),
          '_phoneNumber' => $company->getMetaValueByCode('_phoneNumber'),
          '_profileImage' => $company->getMetaValueByCode('_profileImage'),
          '_coverImage' => $company->getMetaValueByCode('_coverImage'),
          '_activeDays' => $company->getActiveDaysArray()
        ];
      return $data;
    }

    return null;
  }
    private function toSubscriptionPlanInfo(SubscriptionPlan $subscriptionPlan): array|null
  {
    if (!is_null($subscriptionPlan)) {
      $data = [
        "id" => $subscriptionPlan->getId(),
        "name" => $subscriptionPlan->getName(),
        "description" => $subscriptionPlan->getDescription(),
        "price" => $subscriptionPlan->getPrice(),
        "duration" => $subscriptionPlan->getDuration(),
        "active" => $subscriptionPlan->isActive(),
        "createdAt" => $subscriptionPlan->getCreatedAt()->format('d-m-Y H:i:s'),
        "updatedAt" => $subscriptionPlan->getUpdatedAt()->format('d-m-Y H:i:s'),
      ];
      return $data;
    }
    return null;
  }
  private function toSubscriptionPlanAdminInfo(SubscriptionPlan $subscriptionPlan): array|null
  {
    if (!is_null($subscriptionPlan)) {
      $data = [
        "id" => $subscriptionPlan->getId(),
        "name" => $subscriptionPlan->getName(),
        "description" => $subscriptionPlan->getDescription(),
        "price" => $subscriptionPlan->getPrice(),
        "duration" => $subscriptionPlan->getDuration(),
        "active" => $subscriptionPlan->isActive(),
        "subscriptionsCount" => $subscriptionPlan->getSubscriptions()->count(),
        "createdAt" => $subscriptionPlan->getCreatedAt()->format('d-m-Y H:i:s'),
        "updatedAt" => $subscriptionPlan->getUpdatedAt()->format('d-m-Y H:i:s'),
      ];
      return $data;
    }
    return null;
  }
  private function toSubscriptionInfo(Subscription $subscription): array|null
  {
      if (!empty($subscription)) {
          $data = [
              'id' => $subscription->getId(),
              'companyName' => $subscription->getCompany()->getName(),
              'plan' => $this->toSubscriptionPlanInfo($subscription->getPlan()),
              'startDate' => $subscription->getStartDate(),
              'endDate' => $subscription->getEndDate(),
              'payment' => $this->toPaymentInfo($subscription->getPayment()),
          ];
  
          return $data;
      }
      return null;
  }


  private function toLicenseInfo(License $license): array|null
  {
      if (!empty($license)) {
          $data = [
              'id' => $license->getId(),
              'lastname' => $license->getLastName(),
              'firstname' => $license->getFirstName(),
              'birthDate' => $license->getBirthDate(),
              'idcardRecto' => $license->getIdcardRecto(),
              'idcardVerso' => $license->getIdcardVerso(),
              'photo' => $license->getPhoto(),
              'category' => $license->getCategory(),
              'checked' => $license->isChecked(),
              'user' => $license->getUser()->getDisplayName(),
              'status' => $license->getStatus(),
              'active' => $license->isActive(),
              'plan' => $this->toLicensePlanInfo($license->getLicensePlan()),
              'endDate' => $license->getEndDate(),
              'payment' => $this->toPaymentInfo($license->getPayment()),
              'createdAt' => $license->getCreatedAt(),          ];
  
          return $data;
      }
      return null;
  }
  private function toLicenseAdminInfo(License $license): array|null
  {
      if (!empty($license)) {
          $data = [
              'id' => $license->getId(),
              'lastname' => $license->getLastName(),
              'firstname' => $license->getFirstName(),
              'birthDate' => $license->getBirthDate(),
              'idcardRecto' => $license->getIdcardRecto(),
              'idcardVerso' => $license->getIdcardVerso(),
              'photo' => $license->getPhoto(),
              'category' => $license->getCategory(),
              'user' => $license->getUser()->getDisplayName(),
              'status' => $license->getStatus(),
              'active' => $license->isActive(),
              'checked' => $license->isChecked(), 
              'plan' => $this->toLicensePlanInfo($license->getLicensePlan()),
              'endDate' => $license->getEndDate(),
              'payment' => $this->toPaymentInfo($license->getPayment()),
              'createdAt' => $license->getCreatedAt(),          ];
  
          return $data;
      }
      return null;
  }

  private function toLicensePlanInfo(LicensePlan $licensePlan): array|null
  {
    if (!is_null($licensePlan)) {
      $data = [
        "id" => $licensePlan->getId(),
        "title" => $licensePlan->getTitle(),
        "description" => $licensePlan->getDescription(),
        "price" => $licensePlan->getPrice(),
        "active" => $licensePlan->isActive(),
        "createdAt" => $licensePlan->getCreatedAt()->format('d-m-Y H:i:s'),
        "updatedAt" => $licensePlan->getUpdatedAt()?->format('d-m-Y H:i:s'),
      ];
      return $data;
    }
    return null;
  }
  private function toLicensePlanAdminInfo(LicensePlan $licensePlan): array|null
  {
    if (!is_null($licensePlan)) {
      $data = [
        "id" => $licensePlan->getId(),
        "title" => $licensePlan->getTitle(),
        "description" => $licensePlan->getDescription(),
        "price" => $licensePlan->getPrice(),
        "active" => $licensePlan->isActive(),
        "licensesCount" => $licensePlan->getLicenses()->count(),
        "createdAt" => $licensePlan->getCreatedAt()->format('d-m-Y H:i:s'),
        "updatedAt" => $licensePlan->getUpdatedAt()?->format('d-m-Y H:i:s'),
      ];
      return $data;
    }
    return null;
  }
  private function toSubscriptionAdminInfo(Subscription $subscription): array|null
  {
      if (!empty($subscription)) {
          $data = [
              'id' => $subscription->getId(),
              'companyName' => $subscription->getCompany()->getName(),
              'plan' => $subscription->getPlan()->getName(),
              'startDate' => $subscription->getStartDate()->format('d-m-Y H:i:s'),
              'endDate' => $subscription->getEndDate()->format('d-m-Y H:i:s'),
              'paymentStatus' => $subscription->getPayment()->getStatus(),
              'status' => $subscription->getStatus(),
              'active' => $subscription->isActive(),
              'amount' => $subscription->getPayment()->getAmount(),
              'createdAt' => $subscription->getCreatedAt()->format('d-m-Y H:i:s'),
              'updatedAt' => $subscription->getUpdatedAt()->format('d-m-Y H:i:s'),
          ];
  
          return $data;
      }
      return null;
  }
  private function toPaymentInfo(Payment $payment): array|null
  {
      if (!empty($payment)) {
          $data = [
              'id' => $payment->getId(),
              'payUrl' => $payment->getPayUrl(),
              'currencyCode' => $payment->getCurrencyCode(),
              'amount' => $payment->getAmount(),
              'clientName' => $payment->getClientName(),
              'phoneNumber' => $payment->getPhoneNumber(),
              'mailAddress' => $payment->getMailAddress(),
              'orderId' => $payment->getOrderId(),
              'status' => $payment->getStatus(),
              'financialTransactionId' => $payment->getFinancialTransactionId(),
              'transactionId' => $payment->getTransactionId(),
              'paymentMethod' => $payment->getPaymentMethod(),
              'orangePayUrl' => $payment->getOrangePayUrl(),
              'payToken' => $payment->getPayToken(),
              'notifToken' => $payment->getNotifToken(),
              'paymentType' => $payment->getPaymentType(),
              'description' => $payment->getDescription(),
              'activityDateTime' => $payment->getActivityDateTime(),
              'createdAt' => $payment->getCreatedAt(),
              'updatedAt' => $payment->getUpdatedAt(),
              'companyName' => $payment->getCompanyName(),
          ];
  
          return $data;
      }
      return null;
  }

  private function toPaymentByCompanyInfo(Payment $payment): array|null
  {
      if (!empty($payment)) {
          $data = [
              'id' => $payment->getId(),
              'currencyCode' => $payment->getCurrencyCode(),
              'amount' => $payment->getAmount(),
              'clientName' => $payment->getClientName(),
              'phoneNumber' => $payment->getPhoneNumber(),
              'clientCompany' => $payment->getUser()->getCompany()?->getName(),
              'mailAddress' => $payment->getMailAddress(),
              'orderId' => $payment->getOrderId(),
              'status' => $payment->getStatus(),
              'paymentMethod' => $payment->getPaymentMethod(),
              'activityUuid' => $payment->getIdActivity(),
              'activityName' => $payment->getActivityName(),
              'description' => $payment->getDescription(),
              'activityDateTime' => $payment->getActivityDateTime(),
              'offerTo' => $payment->getOfferTo(),
              'createdAt' => $payment->getCreatedAt(),
              'updatedAt' => $payment->getUpdatedAt(),
          ];
  
          return $data;
      }
      return null;
  }

  private function toSubscriptionPaymentInfo(Payment $payment, SubscriptionRepository $subscriptionRepo): array|null
  {
      if (!empty($payment)) {
          $subscription = $subscriptionRepo->findOneBy(['payment' => $payment]);
          if(!empty($subscription)){

          $data = [
              'id' => $subscription->getId(),
              'payUrl' => $payment->getPayUrl(),
              'currencyCode' => $payment->getCurrencyCode(),
              'amount' => $payment->getAmount(),
              'subscriptionPlan' => $subscription->getDescription(),
              'active' => $subscription->isActive(),
              'orderId' => $payment->getOrderId(),
              'status' => $subscription->getStatus(),
              'financialTransactionId' => $payment->getFinancialTransactionId(),
              'transactionId' => $payment->getTransactionId(),
              'paymentMethod' => $payment->getPaymentMethod(),
              'paymentStatus' => $payment->getStatus(),
              'description' => $payment->getDescription(),
              'startDate' => $subscription->getStartDate(),
              'endDate' => $subscription->getEndDate(),
              'companyName' => $payment->getCompanyName(),
          ];
  
          return $data;
        }
        return null;

      }
      return null;
  }

  private function toLicensePaymentInfo(Payment $payment, LicenseRepository $licenseRepo): array|null
  {
      if (!empty($payment)) {
          $license = $licenseRepo->findOneBy(['payment' => $payment]);
          if(!empty($license)){

          $data = [
              'id' => $license->getId(),
              'payUrl' => $payment->getPayUrl(),
              'currencyCode' => $payment->getCurrencyCode(),
              'amount' => $payment->getAmount(),
              'licensePlan' => $license->getLicensePlan()->getTitle(),
              'active' => $license->isActive(),
              'orderId' => $payment->getOrderId(),
              'status' => $license->getStatus(),
              'financialTransactionId' => $payment->getFinancialTransactionId(),
              'transactionId' => $payment->getTransactionId(),
              'paymentMethod' => $payment->getPaymentMethod(),
              'paymentStatus' => $payment->getStatus(),
              'description' => $payment->getDescription(),
              'endDate' => $license->getEndDate(),
          ];
  
          return $data;
        }
        return [];

      }
      return [];
  }
  private function toPlayerInfo(Player $player): array
  {
      if (!empty($player)) {
          $data = [
              'id' => $player->getId(),
              'uuid' => $player->getUuid(),
              'firstName' => $player->getFirstName(),
              'lastName' => $player->getLastName(),
              'category' => $player->getCategory(),
              'birthDate' => $player->getBirthDate() ? $player->getBirthDate()->format('d-m-Y') : null,
              'photo' => $player->getPhoto(),
              'idcardRecto' => $player->getIdcardRecto(),
              'idcardVerso' => $player->getIdcardVerso(),
              'position' => $player->getPosition(),
              'jerseyNumber' => $player->getJerseyNumber()
          ];

          // Add team info only if team exists
          if ($player->getTeam()) {
              $data['team'] = [
                  'id' => $player->getTeam()->getId(),
                  'name' => $player->getTeam()->getName(),
                  'uuid' => $player->getTeam()->getUuid()
              ];
          }

          // Add licenses only if they exist
          if ($player->getLicenses()) {
              $data['licenses'] = array_map(function($license) {
                  return [
                      'id' => $license->getId(),
                      'category' => $license->getCategory(),
                      'endDate' => $license->getEndDate() ? $license->getEndDate()->format('d-m-Y') : null,
                      'status' => $license->getStatus(),
                      'active' => $license->isActive(),
                      'checked' => $license->isChecked(),
                      'licensePlan' => $license->getLicensePlan() ? [
                          'id' => $license->getLicensePlan()->getId(),
                          'title' => $license->getLicensePlan()->getTitle(),
                          'price' => $license->getLicensePlan()->getPrice()
                      ] : null,
                      'payment' => $license->getPayment() ? [
                          'id' => $license->getPayment()->getId(),
                          'status' => $license->getPayment()->getStatus(),
                          'amount' => $license->getPayment()->getAmount()
                      ] : null
                  ];
              }, $player->getLicenses()->toArray());
          }

          // Add statistics
          $data['statistics'] = [
              'totalGoals' => $player->getTotalGoals(),
              'totalYellowCards' => $player->getTotalYellowCards(),
              'totalRedCards' => $player->getTotalRedCards(),
              'totalAssists' => $player->getTotalAssists(),
              'totalMatches' => $player->getTotalMatches()
          ];

          $data['active'] = $player->isActive();
          $data['createdAt'] = $player->getCreatedAt() ? $player->getCreatedAt()->format('d-m-Y H:i:s') : null;
          $data['updatedAt'] = $player->getUpdatedAt() ? $player->getUpdatedAt()->format('d-m-Y H:i:s') : null;

          return $data;
      }
      return [];
  }

  private function toStatisticInfo(PlayerStatistic $statistic): array
  {
      if (!empty($statistic)) {
          return [
              'id' => $statistic->getId(),
              'uuid' => $statistic->getUuid(),
              'player' => [
                  'id' => $statistic->getPlayer()->getId(),
                  'name' => $statistic->getPlayer()->getFirstName() . ' ' . $statistic->getPlayer()->getLastName()
              ],
              'matchId' => $statistic->getMatchId(),
              'matchName' => $statistic->getMatchName(),
              'matchDate' => $statistic->getMatchDate() ? $statistic->getMatchDate()->format('d-m-Y') : null,
              'goals' => $statistic->getGoals(),
              'yellowCards' => $statistic->getYellowCards(),
              'redCards' => $statistic->getRedCards(),
              'assists' => $statistic->getAssists(),
              'notes' => $statistic->getNotes(),
              'createdAt' => $statistic->getCreatedAt() ? $statistic->getCreatedAt()->format('d-m-Y H:i:s') : null
          ];
      }
      return [];
  }

}

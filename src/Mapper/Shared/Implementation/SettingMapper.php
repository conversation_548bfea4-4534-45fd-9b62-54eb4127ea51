<?php

namespace App\Mapper\Shared\Implementation;

use App\Constant\GenericResponseMessageKeys;
use App\Constant\GenericResponseMessages;
use App\Constant\OutputFormats;
use App\Dto\Shared\In\PatchOperationIn;
use App\Dto\Shared\In\Setting\SettingCreateIn;
use App\Dto\Shared\In\Setting\SettingReplaceOrUpdateIn;
use Symfony\Component\HttpFoundation\Request;
use App\Entity\Shared\Setting;
use App\Entity\Shared\SettingVersion;
use App\Exception\RequestValidationException;
use App\Form\Shared\SettingCreateType;
use App\Mapper\BaseMapperTrait;
use App\Mapper\Shared\Interface\ISettingMapper;
use App\Service\Security\Interface\IUserService;
use App\Repository\Shared\SettingRepository;
use App\Dto\Shared\Out\Setting\SettingFullOut;
use App\Dto\Shared\Out\Setting\SettingMinimalOut;
use App\Dto\Shared\Out\Setting\SettingStandardOut;
use App\Dto\Shared\Out\Setting\SettingTinyOut;
use App\Dto\Shared\Out\Setting\SettingVersionFullOut;
use App\Entity\Security\User;
use App\Exception\DeleteConflictException;
use App\Exception\EntityNotFoundException;
use App\Form\Shared\SettingReplaceOrUpdateType;
use App\Mapper\Security\Interface\IUserMapper;
use Doctrine\Common\Collections\ArrayCollection;

class SettingMapper implements ISettingMapper
{
  use BaseMapperTrait;

  private SettingRepository $repository;

  private IUserService $userService;

  private IUserMapper $userMapper;

  public function __construct(SettingRepository $repository, IUserService $userService, IUserMapper $userMapper)
  {
    $this->repository = $repository;
    $this->userService = $userService;
    $this->userMapper = $userMapper;
  }

  private function ensureIsCodeUnique(SettingCreateIn $settingCreateIn): void
  {
    if (!empty($this->repository->findOneByCode(["code" => $settingCreateIn->getCode()]))) {
      $this->throwRequestValidationError(GenericResponseMessages::SETTING_CODE_DUPLICATE, GenericResponseMessageKeys::SETTING_CODE_DUPLICATE);
    }
  }

  private function fromSettingCreateIn(SettingCreateIn $settingCreateIn): Setting
  {
    $this->ensureIsCodeUnique($settingCreateIn);

    $user = $this->userService->getCurrentUser();

    return $this->map($settingCreateIn, Setting::class)
      ->setOwner($user)
      ->addSettingVersion(
        (new SettingVersion())
          ->setOldValue(null)
          ->setEditor($user)
          ->setNewValue($settingCreateIn->getValue())
      );
  }

  private function fromSettingReplaceOrUpdateIn(string $uuid, SettingReplaceOrUpdateIn $settingReplaceOrUpdateIn): Setting
  {
    $user = $this->userService->getCurrentUser();
    $setting = $this->repository->findOneBy(["uuid" => $uuid]);

    if (empty($setting)) {
      throw new EntityNotFoundException($this->getErrorMessage("The item with the provided uuid doesn't exists", "setting_not_foung"));
    }

    $oldValue = $setting->getValue();
    $setting = $this->map($settingReplaceOrUpdateIn,  $setting);

    if ($oldValue !== $settingReplaceOrUpdateIn->getValue()) {
      $setting->addSettingVersion(
        (new SettingVersion())
          ->setEditor($user)
          ->setOldValue($oldValue)
          ->setNewValue($settingReplaceOrUpdateIn->getValue())
      );
    }

    return $setting;
  }

  private function toSettingVersionOut(SettingVersion $settingVersion): SettingVersionFullOut
  {
    return $this->map($settingVersion, SettingVersionFullOut::class)
      ->setEditor($this->userMapper->toTinyOut($settingVersion->getEditor()));
  }

  private function toSettingVersionOutArray(SettingVersion ...$settingVersions): array
  {
    return array_map(function ($version) {
      return $this->toSettingVersionOut(($version));
    }, $settingVersions);
  }

  public function fromSettingCreateRequest(Request $request): Setting
  {
    $settingCreateIn = new SettingCreateIn();
    $form = $this->formFactory->create(SettingCreateType::class, $settingCreateIn);
    $form->submit($request->request->all());

    if (false === $form->isValid()) {
      throw new RequestValidationException($form);
    }

    return $this->fromSettingCreateIn($settingCreateIn);
  }

  public function fromReplaceOrPatchUpdateRequest(string $uuid, Request|PatchOperationIn $request, bool $clearMissing = true): Setting
  {
    $settingCreateIn = new SettingReplaceOrUpdateIn();
    $form = $this->formFactory->create(SettingReplaceOrUpdateType::class, $settingCreateIn);
    if ($request instanceof PatchOperationIn) {
      $form->submit($request->getPayload(), $clearMissing);
    } else {
      $form->submit($request->request->all(), $clearMissing);
    }
    if (false === $form->isValid()) {
      throw new RequestValidationException($form);
    }

    return $this->fromSettingReplaceOrUpdateIn($uuid, $settingCreateIn);
  }

  public function toSettingTinyOut(Setting $setting): SettingTinyOut
  {
    return $this->map($setting, SettingTinyOut::class);
  }

  public function toSettingMinimalOut(Setting $setting): SettingMinimalOut
  {
    return $this->map($setting, SettingMinimalOut::class);
  }

  public function toSettingStandardOut(Setting $setting): SettingStandardOut
  {
    return $this->map($setting, SettingStandardOut::class);
     // ->setOwner($this->userMapper->toTinyOut($setting->getOwner()));
  }

  public function toSettingFullOut(Setting $setting): SettingFullOut
  {
    return $this->map($setting, SettingFullOut::class)
      ->setOwner($this->userMapper->toTinyOut($setting->getOwner()))
      ->setSettingVersions(new ArrayCollection($this->toSettingVersionOutArray(...$setting->getSettingVersions())));
  }

  public function toSettingOut(string $outputFormat, Setting $setting): SettingFullOut | SettingStandardOut | SettingMinimalOut | SettingTinyOut
  {
    $this->ensureOutPutIsValid($outputFormat);

    return match ($outputFormat) {
      OutputFormats::TINY_OUTPUT_FORMAT => $this->toSettingTinyOut($setting),
      OutputFormats::MINIMAL_OUTPUT_FORMAT => $this->toSettingMinimalOut($setting),
      OutputFormats::FULL_OUTPUT_FORMAT => $this->toSettingFullOut($setting),
      default => $this->toSettingStandardOut($setting),
    };
  }

  public function toSettingOutArray(string $outputFormat, Setting ...$settings): array
  {
    $this->ensureOutPutIsValid($outputFormat);

    return array_map(
      function ($setting) use ($outputFormat) {
        return $this->toSettingOut($outputFormat, $setting);
      },
      $settings
    );
  }

  public function fromUpdateRequest(string $uuid, Request $request): array
  {
    return $this->fromPatchRequest($request);
  }

  public function fromRemoveRequest(string $uuid, Request $request): Setting
  {
    $setting = $this->repository->getByUuid($uuid);
    if ($setting->isProgiciel()) {
      throw new DeleteConflictException($this->getErrorMessage("You cannot delete a system setting", "system_setting_unremovable"));
    }
    return $setting;
  }

  public function fromArray(array $settingArray, User $user): Setting
  {
    return $this->mapArray($settingArray, Setting::class)->setOwner($user);
  }
}

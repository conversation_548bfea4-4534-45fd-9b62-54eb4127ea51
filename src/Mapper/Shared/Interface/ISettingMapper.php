<?php

namespace App\Mapper\Shared\Interface;

use App\Dto\Shared\In\PatchOperationIn;
use App\Dto\Shared\Out\Setting\SettingFullOut;
use App\Dto\Shared\Out\Setting\SettingMinimalOut;
use App\Dto\Shared\Out\Setting\SettingStandardOut;
use App\Dto\Shared\Out\Setting\SettingTinyOut;
use App\Entity\Security\User;
use App\Entity\Shared\Setting;
use App\Mapper\IBaseMapper;
use Symfony\Component\HttpFoundation\Request;

interface ISettingMapper extends IBaseMapper
{

  /**
   * Does something interesting
   *
   * @param Request $request  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> <<EMAIL>>
   * @return Setting
   */
  public function fromSettingCreateRequest(Request $request): Setting;

  /**
   * Does something interesting
   *
   * @param string $uuid  Where something interesting takes place
   * @param Request|PatchOperationIn $request  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> <<EMAIL>>
   * @return Setting
   */
  public function fromReplaceOrPatchUpdateRequest(string $uuid, Request|PatchOperationIn $request, bool $clearMissing = true): Setting;

  /**
   * Does something interesting
   *
   * @param string $uuid  Where something interesting takes place
   * @param Request $request  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return array
   */
  public function fromUpdateRequest(string $uuid, Request $request): array;

  /**
   * Does something interesting
   *
   * @param Setting $setting Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return SettingTinyOut
   */
  public function toSettingTinyOut(Setting $setting): SettingTinyOut;

  /**
   * Does something interesting
   *
   * @param Setting $setting  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return SettingMinimalOut
   */
  public function toSettingMinimalOut(Setting $setting): SettingMinimalOut;

  /**
   * Does something interesting
   *
   * @param Setting $setting  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return SettingStandardOut
   */
  public function toSettingStandardOut(Setting $setting): SettingStandardOut;

  /**
   * Does something interesting
   *
   * @param Setting $setting  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return SettingFullOut
   */
  public function toSettingFullOut(Setting $setting): SettingFullOut;

  /**
   * Does something interesting
   *
   * @param string $outputFormat  Where something interesting takes place
   * @param Setting $setting  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return SettingFullOut | SettingStandardOut | SettingMinimalOut | SettingTinyOut
   */
  public function toSettingOut(string $outputFormat, Setting $setting): SettingFullOut | SettingStandardOut | SettingMinimalOut | SettingTinyOut;

  /**
   * Does something interesting
   *
   * @param string $outputFormat  Where something interesting takes place
   * @param Setting $settings  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return array
   */
  public function toSettingOutArray(string $outputFormat, Setting ...$settings): array;

  /**
   * Does something interesting
   *
   * @param string $uuid  Where something interesting takes place
   * @param Request $request  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return Setting
   */
  public function fromRemoveRequest(string $uuid, Request $request): Setting;


  /**
   * Does something interesting
   *
   * @param array $addressArray  Where something interesting takes place
   * @param User $user  Where something interesting takes place
   * 
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return Setting
   */
  public function fromArray(array $settingArray, User $user): Setting;
}

<?php

namespace App\Mapper;

use App\Dto\Shared\In\FindAllFilterIn;
use Symfony\Component\HttpFoundation\Request;

interface IBaseMapper
{

  /**
   * Does something interesting
   *
   * @param Request $request  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> <<EMAIL>>
   * @return FindAllFilterIn
   */
  public function fromFindAllRequest(Request $request): FindAllFilterIn;
}

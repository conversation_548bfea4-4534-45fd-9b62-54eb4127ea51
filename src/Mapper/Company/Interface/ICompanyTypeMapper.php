<?php

namespace App\Mapper\Company\Interface;

use App\Dto\Company\Out\CompanyType\CompanyTypeFullOut;
use App\Dto\Company\Out\CompanyType\CompanyTypeMinimalOut;
use App\Dto\Company\Out\CompanyType\CompanyTypeStandardOut;
use App\Dto\Company\Out\CompanyType\CompanyTypeTinyOut;
use App\Entity\Company\CompanyType;
use App\Mapper\IBaseMapper;

interface ICompanyTypeMapper extends IBaseMapper
{

  /**
   * Does something interesting
   *
   * @param array $data  Where something interesting takes place
   * 
   * <AUTHOR> <<EMAIL>>
   * @return CompanyType
   */
  public function fromArray(array $data): CompanyType;

  /**
   * Does something interesting
   *
   * @param array $data  Where something interesting takes place
   * 
   * <AUTHOR> <<EMAIL>>
   * @return CompanyType
   */
  public function fromArrayUpdate(array $data, CompanyType $companyType): CompanyType;

  /**
   * Does something interesting
   *
   * @param CompanyType $companyType Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> <<EMAIL>>
   * @return CompanyTypeTinyOut
   */
  public function toTinyOut(CompanyType $companyType): CompanyTypeTinyOut;

  /**
   * Does something interesting
   *
   * @param CompanyType $companyType  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return CompanyTypeMinimalOut
   */
  public function toMinimalOut(CompanyType $companyType): CompanyTypeMinimalOut;

  /**
   * Does something interesting
   *
   * @param CompanyType $companyType  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return CompanyTypeStandardOut
   */
  public function toStandardOut(CompanyType $companyType): CompanyTypeStandardOut;

  /**
   * Does something interesting
   *
   * @param CompanyType $companyType  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return CompanyTypeFullOut
   */
  public function toFullOut(CompanyType $companyType): CompanyTypeFullOut;

  /**
   * Does something interesting
   *
   * @param string $outputFormat  Where something interesting takes place
   * @param CompanyType $companyType  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return CompanyTypeFullOut | CompanyTypeStandardOut | CompanyTypeMinimalOut | CompanyTypeTinyOut
   */
  public function toOut(string $outputFormat, CompanyType $companyType): CompanyTypeFullOut | CompanyTypeStandardOut | CompanyTypeMinimalOut | CompanyTypeTinyOut;

  /**
   * Does something interesting
   *
   * @param string $outputFormat  Where something interesting takes place
   * @param CompanyType $companyCategories  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return array
   */
  public function toOutArray(string $outputFormat, CompanyType ...$companyCategories): array;
}

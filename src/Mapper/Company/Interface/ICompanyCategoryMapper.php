<?php

namespace App\Mapper\Company\Interface;

use App\Dto\Company\Out\CompanyCategory\CompanyCategoryFullOut;
use App\Dto\Company\Out\CompanyCategory\CompanyCategoryMinimalOut;
use App\Dto\Company\Out\CompanyCategory\CompanyCategoryStandardOut;
use App\Dto\Company\Out\CompanyCategory\CompanyCategoryTinyOut;
use App\Entity\Company\CompanyCategory;
use App\Mapper\IBaseMapper;

interface ICompanyCategoryMapper extends IBaseMapper
{

  /**
   * Does something interesting
   *
   * @param array $data  Where something interesting takes place
   * 
   * <AUTHOR> <<EMAIL>>
   * @return CompanyCategory
   */
  public function fromArray(array $data): CompanyCategory;

  /**
   * Does something interesting
   *
   * @param array $data  Where something interesting takes place
   * 
   * <AUTHOR> <<EMAIL>>
   * @return CompanyCategory
   */
  public function fromArrayUpdate(array $data, CompanyCategory $companyCategory): CompanyCategory;

  /**
   * Does something interesting
   *
   * @param CompanyCategory $companyCategory Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return CompanyCategoryTinyOut
   */
  public function toTinyOut(CompanyCategory $companyCategory): CompanyCategoryTinyOut;

  /**
   * Does something interesting
   *
   * @param CompanyCategory $companyCategory  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return CompanyCategoryMinimalOut
   */
  public function toMinimalOut(CompanyCategory $companyCategory): CompanyCategoryMinimalOut;

  /**
   * Does something interesting
   *
   * @param CompanyCategory $companyCategory  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return CompanyCategoryStandardOut
   */
  public function toStandardOut(CompanyCategory $companyCategory): CompanyCategoryStandardOut;

  /**
   * Does something interesting
   *
   * @param CompanyCategory $companyCategory  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return CompanyCategoryFullOut
   */
  public function toFullOut(CompanyCategory $companyCategory): CompanyCategoryFullOut;

  /**
   * Does something interesting
   *
   * @param string $outputFormat  Where something interesting takes place
   * @param CompanyCategory $companyCategory  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return CompanyCategoryFullOut | CompanyCategoryStandardOut | CompanyCategoryMinimalOut | CompanyCategoryTinyOut
   */
  public function toOut(string $outputFormat, CompanyCategory $companyCategory): CompanyCategoryFullOut | CompanyCategoryStandardOut | CompanyCategoryMinimalOut | CompanyCategoryTinyOut;

  /**
   * Does something interesting
   *
   * @param string $outputFormat  Where something interesting takes place
   * @param CompanyCategory $companyCategories  Where something interesting takes place
   * 
   * @throws RequestValidationExeption If something interesting cannot happen
   * <AUTHOR> KONGNUY <<EMAIL>>
   * @return array
   */
  public function toOutArray(string $outputFormat, CompanyCategory ...$companyCategories): array;
}

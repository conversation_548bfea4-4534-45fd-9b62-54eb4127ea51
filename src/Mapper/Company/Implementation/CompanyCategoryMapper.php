<?php

namespace App\Mapper\Company\Implementation;

use App\Constant\OutputFormats;
use App\Dto\Company\Out\CompanyCategory\CompanyCategoryFullOut;
use App\Dto\Company\Out\CompanyCategory\CompanyCategoryMinimalOut;
use App\Dto\Company\Out\CompanyCategory\CompanyCategoryStandardOut;
use App\Dto\Company\Out\CompanyCategory\CompanyCategoryTinyOut;
use App\Entity\Company\CompanyCategory;
use App\Mapper\Company\Interface\ICompanyCategoryMapper;
use App\Mapper\BaseMapperTrait;

class CompanyCategoryMapper implements ICompanyCategoryMapper
{
  use BaseMapperTrait;

  public function fromArray(array $data): CompanyCategory
  {
    return $this->mapArray($data, CompanyCategory::class);
  }

  public function fromArrayUpdate(array $data, CompanyCategory $companyCategory): CompanyCategory
  {
    return $this->mapArray($data, $companyCategory);
  }

  public function toTinyOut(CompanyCategory $companyCategory): CompanyCategoryTinyOut
  {
    return $this->map($companyCategory, CompanyCategoryTinyOut::class);
  }

  public function toMinimalOut(CompanyCategory $companyCategory): CompanyCategoryMinimalOut
  {
    return $this->map($companyCategory, CompanyCategoryMinimalOut::class);
  }

  public function toStandardOut(CompanyCategory $companyCategory): CompanyCategoryStandardOut
  {
    return $this->map($companyCategory, CompanyCategoryStandardOut::class);
  }

  public function toFullOut(CompanyCategory $companyCategory): CompanyCategoryFullOut
  {
    return $this->map($companyCategory, CompanyCategoryFullOut::class);
  }

  public function toOut(string $outputFormat, CompanyCategory $companyCategory): CompanyCategoryFullOut | CompanyCategoryStandardOut | CompanyCategoryMinimalOut | CompanyCategoryTinyOut
  {
    $this->ensureOutPutIsValid($outputFormat);

    return match ($outputFormat) {
      OutputFormats::TINY_OUTPUT_FORMAT => $this->toTinyOut($companyCategory),
      OutputFormats::MINIMAL_OUTPUT_FORMAT => $this->toMinimalOut($companyCategory),
      OutputFormats::FULL_OUTPUT_FORMAT => $this->toFullOut($companyCategory),
      default => $this->toStandardOut($companyCategory),
    };
  }

  public function toOutArray(string $outputFormat, CompanyCategory ...$companyCategories): array
  {
    $this->ensureOutPutIsValid($outputFormat);

    return array_map(
      function ($companyCategory) use ($outputFormat) {
        return $this->toOut($outputFormat, $companyCategory);
      },
      $companyCategories
    );
  }
}

<?php

namespace App\Mapper\Company\Implementation;

use App\Constant\OutputFormats;
use App\Dto\Company\Out\CompanyType\CompanyTypeFullOut;
use App\Dto\Company\Out\CompanyType\CompanyTypeMinimalOut;
use App\Dto\Company\Out\CompanyType\CompanyTypeStandardOut;
use App\Dto\Company\Out\CompanyType\CompanyTypeTinyOut;
use App\Entity\Company\CompanyType;
use App\Mapper\Company\Interface\ICompanyTypeMapper;
use App\Mapper\BaseMapperTrait;

class CompanyTypeMapper implements ICompanyTypeMapper
{
  use BaseMapperTrait;

  public function fromArray(array $data): CompanyType
  {
    return $this->mapArray($data, CompanyType::class);
  }

  public function fromArrayUpdate(array $data, CompanyType $companyType): CompanyType
  {
    return $this->mapArray($data, $companyType);
  }

  public function toTinyOut(CompanyType $companyType): CompanyTypeTinyOut
  {
    return $this->map($companyType, CompanyTypeTinyOut::class);
  }

  public function toMinimalOut(CompanyType $companyType): CompanyTypeMinimalOut
  {
    return $this->map($companyType, CompanyTypeMinimalOut::class);
  }

  public function toStandardOut(CompanyType $companyType): CompanyTypeStandardOut
  {
    return $this->map($companyType, CompanyTypeStandardOut::class);
  }

  public function toFullOut(CompanyType $companyType): CompanyTypeFullOut
  {
    return $this->map($companyType, CompanyTypeFullOut::class);
  }

  public function toOut(string $outputFormat, CompanyType $companyType): CompanyTypeFullOut | CompanyTypeStandardOut | CompanyTypeMinimalOut | CompanyTypeTinyOut
  {
    $this->ensureOutPutIsValid($outputFormat);

    return match ($outputFormat) {
      OutputFormats::TINY_OUTPUT_FORMAT => $this->toTinyOut($companyType),
      OutputFormats::MINIMAL_OUTPUT_FORMAT => $this->toMinimalOut($companyType),
      OutputFormats::FULL_OUTPUT_FORMAT => $this->toFullOut($companyType),
      default => $this->toStandardOut($companyType),
    };
  }

  public function toOutArray(string $outputFormat, CompanyType ...$companyCategories): array
  {
    $this->ensureOutPutIsValid($outputFormat);

    return array_map(
      function ($companyType) use ($outputFormat) {
        return $this->toOut($outputFormat, $companyType);
      },
      $companyCategories
    );
  }
}

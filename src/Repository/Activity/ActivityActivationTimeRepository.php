<?php

namespace App\Repository\Activity;

use App\Dto\Shared\In\FindAllFilterIn;
use App\Entity\Activity\ActivityActivationTime;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ActivityActivationTime>
 *
 * @method ActivityActivationTime|null find($id, $lockMode = null, $lockVersion = null)
 * @method ActivityActivationTime|null findOneBy(array $criteria, array $orderBy = null)
 * @method ActivityActivationTime[]    findAll()
 * @method ActivityActivationTime[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ActivityActivationTimeRepository extends ServiceEntityRepository
{
  public function __construct(ManagerRegistry $registry)
  {
    parent::__construct($registry, ActivityActivationTime::class);
  }

  public function save(ActivityActivationTime $entity, bool $flush = false): void
  {
    $this->getEntityManager()->persist($entity);

    if ($flush) {
      $this->getEntityManager()->flush();
    }
  }

  public function remove(ActivityActivationTime $entity, bool $flush = false): void
  {
    $this->getEntityManager()->remove($entity);

    if ($flush) {
      $this->getEntityManager()->flush();
    }
  }

  //    /**
  //     * @return ActivityActivationTime[] Returns an array of ActivityActivationTime objects
  //     */
  //    public function findByExampleField($value): array
  //    {
  //        return $this->createQueryBuilder('a')
  //            ->andWhere('a.exampleField = :val')
  //            ->setParameter('val', $value)
  //            ->orderBy('a.id', 'ASC')
  //            ->setMaxResults(10)
  //            ->getQuery()
  //            ->getResult()
  //        ;
  //    }

  //    public function findOneBySomeField($value): ?ActivityActivationTime
  //    {
  //        return $this->createQueryBuilder('a')
  //            ->andWhere('a.exampleField = :val')
  //            ->setParameter('val', $value)
  //            ->getQuery()
  //            ->getOneOrNullResult()
  //        ;
  //    }
}

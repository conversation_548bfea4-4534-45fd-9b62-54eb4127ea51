<?php

namespace App\Repository\Activity;

use App\Entity\Activity\ActivityActivationTimeType;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ActivityActivationTimeType>
 *
 * @method ActivityActivationTimeType|null find($id, $lockMode = null, $lockVersion = null)
 * @method ActivityActivationTimeType|null findOneBy(array $criteria, array $orderBy = null)
 * @method ActivityActivationTimeType[]    findAll()
 * @method ActivityActivationTimeType[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ActivityActivationTimeTypeRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ActivityActivationTimeType::class);
    }

    public function save(ActivityActivationTimeType $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(ActivityActivationTimeType $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

//    /**
//     * @return ActivityActivationTimeType[] Returns an array of ActivityActivationTimeType objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('a')
//            ->andWhere('a.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('a.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?ActivityActivationTimeType
//    {
//        return $this->createQueryBuilder('a')
//            ->andWhere('a.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
}

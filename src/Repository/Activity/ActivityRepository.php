<?php

namespace App\Repository\Activity;

use App\Dto\GenericResponseMessage;
use App\Entity\Activity\Activity;
use App\Exception\EntityNotFoundException;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Activity>
 *
 * @method Activity|null find($id, $lockMode = null, $lockVersion = null)
 * @method Activity|null findOneBy(array $criteria, array $orderBy = null)
 * @method Activity[]    findAll()
 * @method Activity[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ActivityRepository extends ServiceEntityRepository
{
  public function __construct(ManagerRegistry $registry)
  {
    parent::__construct($registry, Activity::class);
  }

  public function save(Activity $entity, bool $flush = false): void
  {
    $this->getEntityManager()->persist($entity);

    if ($flush) {
      $this->getEntityManager()->flush();
    }
  }

  public function remove(Activity $entity, bool $flush = false): void
  {
    $this->getEntityManager()->remove($entity);

    if ($flush) {
      $this->getEntityManager()->flush();
    }
  }

  public function getByUuid(string $uuid, bool $throwIfNotFound = true): Activity
  {
    $activity = $this->findOneBy(["uuid" => $uuid]);
    if (empty($activity) && $throwIfNotFound) {
      throw new EntityNotFoundException(new GenericResponseMessage("The item with the provided uuid doesn't exists", "activity_not_found"));
    }
    return $activity;
  }
}

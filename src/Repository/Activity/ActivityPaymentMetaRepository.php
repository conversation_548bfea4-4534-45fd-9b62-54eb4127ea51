<?php

namespace App\Repository\Activity;

use App\Entity\Activity\ActivityPaymentMeta;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ActivityPaymentMeta>
 *
 * @method ActivityPaymentMeta|null find($id, $lockMode = null, $lockVersion = null)
 * @method ActivityPaymentMeta|null findOneBy(array $criteria, array $orderBy = null)
 * @method ActivityPaymentMeta[]    findAll()
 * @method ActivityPaymentMeta[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ActivityPaymentMetaRepository extends ServiceEntityRepository
{
  public function __construct(ManagerRegistry $registry)
  {
    parent::__construct($registry, ActivityPaymentMeta::class);
  }

  public function save(ActivityPaymentMeta $entity, bool $flush = false): void
  {
    $this->getEntityManager()->persist($entity);

    if ($flush) {
      $this->getEntityManager()->flush();
    }
  }

  public function remove(ActivityPaymentMeta $entity, bool $flush = false): void
  {
    $this->getEntityManager()->remove($entity);

    if ($flush) {
      $this->getEntityManager()->flush();
    }
  }
}

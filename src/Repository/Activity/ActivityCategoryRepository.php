<?php

namespace App\Repository\Activity;

use App\Dto\GenericResponseMessage;
use App\Entity\Activity\ActivityCategory;
use App\Exception\EntityNotFoundException;
use App\Repository\Traits\BaseRepositoryTrait;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ActivityCategory>
 *
 * @method ActivityCategory|null find($id, $lockMode = null, $lockVersion = null)
 * @method ActivityCategory|null findOneBy(array $criteria, array $orderBy = null)
 * @method ActivityCategory[]    findAll()
 * @method ActivityCategory[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ActivityCategoryRepository extends ServiceEntityRepository
{
  use BaseRepositoryTrait;

  public function __construct(ManagerRegistry $registry)
  {
    parent::__construct($registry, ActivityCategory::class);
  }

  public function save(ActivityCategory $entity, bool $flush = false): void
  {
    $this->getEntityManager()->persist($entity);

    if ($flush) {
      $this->getEntityManager()->flush();
    }
  }

  public function remove(ActivityCategory $entity, bool $flush = false): void
  {
    $this->getEntityManager()->remove($entity);

    if ($flush) {
      $this->getEntityManager()->flush();
    }
  }

  public function getByUuid(string $uuid, bool $throwIfNotFound = true): ActivityCategory|null
  {
    $entity = $this->findOneBy(["uuid" => $uuid]);

    if (empty($entity) && $throwIfNotFound) {
      throw new EntityNotFoundException(new GenericResponseMessage("The item with the provided uuid doesn't exists", "activity_category_not_found"));
    }
    return $entity;
  }
}

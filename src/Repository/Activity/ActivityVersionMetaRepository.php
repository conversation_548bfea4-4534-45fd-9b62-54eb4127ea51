<?php

namespace App\Repository\Activity;

use App\Entity\Activity\ActivityVersionMeta;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ActivityVersionMeta>
 *
 * @method ActivityVersionMeta|null find($id, $lockMode = null, $lockVersion = null)
 * @method ActivityVersionMeta|null findOneBy(array $criteria, array $orderBy = null)
 * @method ActivityVersionMeta[]    findAll()
 * @method ActivityVersionMeta[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ActivityVersionMetaRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ActivityVersionMeta::class);
    }

    public function save(ActivityVersionMeta $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(ActivityVersionMeta $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

//    /**
//     * @return ActivityVersionMeta[] Returns an array of ActivityVersionMeta objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('a')
//            ->andWhere('a.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('a.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?ActivityVersionMeta
//    {
//        return $this->createQueryBuilder('a')
//            ->andWhere('a.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
}

<?php

namespace App\Repository\Activity;

use App\Entity\Activity\ActivityPayment;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ActivityPayment>
 *
 * @method ActivityPayment|null find($id, $lockMode = null, $lockVersion = null)
 * @method ActivityPayment|null findOneBy(array $criteria, array $orderBy = null)
 * @method ActivityPayment[]    findAll()
 * @method ActivityPayment[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ActivityPaymentRepository extends ServiceEntityRepository
{
  public function __construct(ManagerRegistry $registry)
  {
    parent::__construct($registry, ActivityPayment::class);
  }

  public function save(ActivityPayment $entity, bool $flush = false): void
  {
    $this->getEntityManager()->persist($entity);

    if ($flush) {
      $this->getEntityManager()->flush();
    }
  }

  public function remove(ActivityPayment $entity, bool $flush = false): void
  {
    $this->getEntityManager()->remove($entity);

    if ($flush) {
      $this->getEntityManager()->flush();
    }
  }
}

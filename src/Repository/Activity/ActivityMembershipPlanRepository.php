<?php

namespace App\Repository\Activity;

use App\Entity\Activity\ActivityMembershipPlan;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ActivityMembershipPlan>
 *
 * @method ActivityMembershipPlan|null find($id, $lockMode = null, $lockVersion = null)
 * @method ActivityMembershipPlan|null findOneBy(array $criteria, array $orderBy = null)
 * @method ActivityMembershipPlan[]    findAll()
 * @method ActivityMembershipPlan[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ActivityMembershipPlanRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ActivityMembershipPlan::class);
    }

    public function save(ActivityMembershipPlan $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(ActivityMembershipPlan $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

//    /**
//     * @return ActivityMembershipPlan[] Returns an array of ActivityMembershipPlan objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('a')
//            ->andWhere('a.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('a.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?ActivityMembershipPlan
//    {
//        return $this->createQueryBuilder('a')
//            ->andWhere('a.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
}

<?php

namespace App\Repository\Security;

use App\Entity\Security\AuthentificationCode;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<AuthentificationCode>
 *
 * @method AuthentificationCode|null find($id, $lockMode = null, $lockVersion = null)
 * @method AuthentificationCode|null findOneBy(array $criteria, array $orderBy = null)
 * @method AuthentificationCode[]    findAll()
 * @method AuthentificationCode[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AuthentificationCodeRepository extends ServiceEntityRepository
{
  public function __construct(ManagerRegistry $registry)
  {
    parent::__construct($registry, AuthentificationCode::class);
  }

  public function save(AuthentificationCode $entity, bool $flush = false): void
  {
    $this->getEntityManager()->persist($entity);

    if ($flush) {
      $this->getEntityManager()->flush();
    }
  }

  public function remove(AuthentificationCode $entity, bool $flush = false): void
  {
    $this->getEntityManager()->remove($entity);

    if ($flush) {
      $this->getEntityManager()->flush();
    }
  }
}

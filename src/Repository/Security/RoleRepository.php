<?php

namespace App\Repository\Security;

use App\Dto\GenericResponseMessage;
use App\Entity\Security\Role;
use App\Exception\EntityNotFoundException;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Role>
 *
 * @method Role|null find($id, $lockMode = null, $lockVersion = null)
 * @method Role|null findOneBy(array $criteria, array $orderBy = null)
 * @method Role[]    findAll()
 * @method Role[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class RoleRepository extends ServiceEntityRepository
{
  public function __construct(ManagerRegistry $registry)
  {
    parent::__construct($registry, Role::class);
  }

  public function save(Role $entity, bool $flush = false): void
  {
    $this->getEntityManager()->persist($entity);

    if ($flush) {
      $this->getEntityManager()->flush();
    }
  }

  public function remove(Role $entity, bool $flush = false): void
  {
    $this->getEntityManager()->remove($entity);

    if ($flush) {
      $this->getEntityManager()->flush();
    }
  }

  public function getByUuid(string $uuid, bool $throwIfNotFound = true): Role
  {
    $role = $this->findOneBy(["uuid" => $uuid]);

    if (empty($role) && $throwIfNotFound) {
      throw new EntityNotFoundException(new GenericResponseMessage("The item with the provided uuid doesn't exists", "role_not_found"));
    }
    return $role;
  }
}

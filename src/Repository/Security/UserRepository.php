<?php

namespace App\Repository\Security;

use App\Dto\GenericResponseMessage;
use App\Entity\Security\User;
use App\Exception\EntityNotFoundException;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Bridge\Doctrine\Security\User\UserLoaderInterface;

/**
 * @extends ServiceEntityRepository<User>
 *
 * @method User|null find($id, $lockMode = null, $lockVersion = null)
 * @method User|null findOneBy(array $criteria, array $orderBy = null)
 * @method User[]    findAll()
 * @method User[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class UserRepository extends ServiceEntityRepository implements UserLoaderInterface
{
  public function __construct(ManagerRegistry $registry)
  {
    parent::__construct($registry, User::class);
  }

  public function save(User $entity, bool $flush = false): void
  {
    $this->getEntityManager()->persist($entity);

    if ($flush) {
      $this->getEntityManager()->flush();
    }
  }

  public function remove(User $entity, bool $flush = false): void
  {
    $this->getEntityManager()->remove($entity);

    if ($flush) {
      $this->getEntityManager()->flush();
    }
  }

  public function loadUserByIdentifier(string $identifier): ?User
  {
    return $this->findByPhoneNumberOrEmailOrUsername($identifier, $identifier, $identifier);
  }

  public function findByPhoneNumberOrEmailOrUsername(?string $phoneNumber, ?string $email, ?string $username): ?User
  {
    return $this->createQueryBuilder('u')
      ->orWhere('u.phoneNumber = :phoneNumber')
      ->orWhere('u.email = :email')
      ->orWhere('u.username = :username')
      ->setParameter('phoneNumber', $phoneNumber)
      ->setParameter('email', $email)
      ->setParameter('username', $username)
      ->getQuery()
      ->getOneOrNullResult();
  }

  public function getByUuid(string $uuid, bool $throwIfNotFound = true): User
  {
    $user = $this->findOneBy(["uuid" => $uuid]);

    if (empty($user) && $throwIfNotFound) {
      throw new EntityNotFoundException(new GenericResponseMessage("The item with the provided uuid doesn't exists", "user_not_found"));
    }
    return $user;
  }
  
    public function findAdminUsers()
  {
    return $this->createQueryBuilder('u')
      ->innerJoin('u.metas', 'm')
      ->where('m.code = :isAdminCode')
      ->andWhere('m.value = :isAdminValue')
      ->setParameter('isAdminCode', 'is_admin')
      ->setParameter('isAdminValue', '1')
      ->getQuery()
      ->getResult();
  }
}

<?php

namespace App\Repository\Tournament;

use App\Entity\Tournament\Encounter;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Encounter>
 *
 * @method Encounter|null find($id, $lockMode = null, $lockVersion = null)
 * @method Encounter|null findOneBy(array $criteria, array $orderBy = null)
 * @method Encounter[]    findAll()
 * @method Encounter[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class EncounterRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Encounter::class);
    }

    public function save(Encounter $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Encounter $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * Find encounters by tournament
     */
    public function findByTournament(int $tournamentId): array
    {
        return $this->createQueryBuilder('e')
            ->andWhere('e.tournament = :tournamentId')
            ->setParameter('tournamentId', $tournamentId)
            ->orderBy('e.encounterDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find encounters by team
     */
    public function findByTeam(int $teamId): array
    {
        return $this->createQueryBuilder('e')
            ->andWhere('e.homeTeam = :teamId OR e.awayTeam = :teamId')
            ->setParameter('teamId', $teamId)
            ->orderBy('e.encounterDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find encounters by category
     */
    public function findByCategory(string $category): array
    {
        return $this->createQueryBuilder('e')
            ->andWhere('e.category = :category')
            ->setParameter('category', $category)
            ->orderBy('e.encounterDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find encounters by status
     */
    public function findByStatus(string $status): array
    {
        return $this->createQueryBuilder('e')
            ->andWhere('e.status = :status')
            ->setParameter('status', $status)
            ->orderBy('e.encounterDate', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find upcoming encounters
     */
    public function findUpcoming(\DateTime $fromDate = null): array
    {
        $qb = $this->createQueryBuilder('e')
            ->andWhere('e.status IN (:statuses)')
            ->setParameter('statuses', ['SCHEDULED', 'ONGOING']);

        if ($fromDate) {
            $qb->andWhere('e.encounterDate >= :fromDate')
               ->setParameter('fromDate', $fromDate);
        } else {
            $qb->andWhere('e.encounterDate >= :now')
               ->setParameter('now', new \DateTime());
        }

        return $qb->orderBy('e.encounterDate', 'ASC')
                  ->getQuery()
                  ->getResult();
    }

    /**
     * Find completed encounters
     */
    public function findCompleted(): array
    {
        return $this->createQueryBuilder('e')
            ->andWhere('e.status = :status')
            ->setParameter('status', 'COMPLETED')
            ->orderBy('e.encounterDate', 'DESC')
            ->getQuery()
            ->getResult();
    }
}

<?php

namespace App\Repository\Tournament;

use App\Entity\Tournament\EncounterEvent;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<EncounterEvent>
 *
 * @method EncounterEvent|null find($id, $lockMode = null, $lockVersion = null)
 * @method EncounterEvent|null findOneBy(array $criteria, array $orderBy = null)
 * @method EncounterEvent[]    findAll()
 * @method EncounterEvent[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class EncounterEventRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, EncounterEvent::class);
    }

    public function save(EncounterEvent $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(EncounterEvent $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * Find events by encounter
     */
    public function findByEncounter(int $encounterId): array
    {
        return $this->createQueryBuilder('ee')
            ->andWhere('ee.encounter = :encounterId')
            ->setParameter('encounterId', $encounterId)
            ->orderBy('ee.minute', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find events by player
     */
    public function findByPlayer(int $playerId): array
    {
        return $this->createQueryBuilder('ee')
            ->andWhere('ee.player = :playerId')
            ->setParameter('playerId', $playerId)
            ->orderBy('ee.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find events by type
     */
    public function findByType(string $type): array
    {
        return $this->createQueryBuilder('ee')
            ->andWhere('ee.type = :type')
            ->setParameter('type', $type)
            ->orderBy('ee.createdAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Get player statistics from events
     */
    public function getPlayerStatistics(int $playerId): array
    {
        $qb = $this->createQueryBuilder('ee')
            ->select('ee.type, COUNT(ee.id) as count')
            ->andWhere('ee.player = :playerId')
            ->setParameter('playerId', $playerId)
            ->groupBy('ee.type');

        return $qb->getQuery()->getResult();
    }

    /**
     * Get top scorers
     */
    public function getTopScorers(int $limit = 10): array
    {
        return $this->createQueryBuilder('ee')
            ->select('p.id, p.firstName, p.lastName, COUNT(ee.id) as goals')
            ->join('ee.player', 'p')
            ->andWhere('ee.type = :type')
            ->setParameter('type', 'GOAL')
            ->groupBy('p.id')
            ->orderBy('goals', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }
}

<?php

namespace App\Repository\Team;

use App\Entity\Team\TeamCategory;
use App\Repository\Traits\BaseRepositoryTrait;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<TeamCategory>
 *
 * @method TeamCategory|null find($id, $lockMode = null, $lockVersion = null)
 * @method TeamCategory|null findOneBy(array $criteria, array $orderBy = null)
 * @method TeamCategory[]    findAll()
 * @method TeamCategory[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class TeamCategoryRepository extends ServiceEntityRepository
{
    use BaseRepositoryTrait;

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, TeamCategory::class);
    }

    public function save(TeamCategory $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(TeamCategory $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * Find category by UUID
     */
    public function getByUuid(string $uuid): ?TeamCategory
    {
        return $this->findOneBy(['uuid' => $uuid, 'active' => true]);
    }

    /**
     * Find category by code
     */
    public function getByCode(string $code): ?TeamCategory
    {
        return $this->findOneBy(['code' => strtoupper($code), 'active' => true]);
    }

    /**
     * Get all active categories ordered by sort order
     */
    public function getActiveCategories(): array
    {
        return $this->findBy(
            ['active' => true],
            ['sortOrder' => 'ASC', 'name' => 'ASC']
        );
    }

    /**
     * Get categories by gender
     */
    public function getCategoriesByGender(string $gender): array
    {
        return $this->findBy(
            ['active' => true, 'gender' => $gender],
            ['sortOrder' => 'ASC', 'name' => 'ASC']
        );
    }

    /**
     * Get categories suitable for a specific age
     */
    public function getCategoriesForAge(int $age): array
    {
        $qb = $this->createQueryBuilder('tc')
            ->where('tc.active = :active')
            ->setParameter('active', true)
            ->andWhere(
                $qb->expr()->orX(
                    $qb->expr()->andX(
                        $qb->expr()->isNull('tc.minAge'),
                        $qb->expr()->isNull('tc.maxAge')
                    ),
                    $qb->expr()->andX(
                        $qb->expr()->lte('tc.minAge', ':age'),
                        $qb->expr()->gte('tc.maxAge', ':age')
                    ),
                    $qb->expr()->andX(
                        $qb->expr()->lte('tc.minAge', ':age'),
                        $qb->expr()->isNull('tc.maxAge')
                    ),
                    $qb->expr()->andX(
                        $qb->expr()->isNull('tc.minAge'),
                        $qb->expr()->gte('tc.maxAge', ':age')
                    )
                )
            )
            ->setParameter('age', $age)
            ->orderBy('tc.sortOrder', 'ASC')
            ->addOrderBy('tc.name', 'ASC');

        return $qb->getQuery()->getResult();
    }

    /**
     * Check if category code is unique
     */
    public function isCodeUnique(string $code, ?int $excludeId = null): bool
    {
        $qb = $this->createQueryBuilder('tc')
            ->select('COUNT(tc.id)')
            ->where('tc.code = :code')
            ->setParameter('code', strtoupper($code));

        if ($excludeId) {
            $qb->andWhere('tc.id != :excludeId')
               ->setParameter('excludeId', $excludeId);
        }

        return (int) $qb->getQuery()->getSingleScalarResult() === 0;
    }

    /**
     * Check if category name is unique
     */
    public function isNameUnique(string $name, ?int $excludeId = null): bool
    {
        $qb = $this->createQueryBuilder('tc')
            ->select('COUNT(tc.id)')
            ->where('tc.name = :name')
            ->setParameter('name', $name);

        if ($excludeId) {
            $qb->andWhere('tc.id != :excludeId')
               ->setParameter('excludeId', $excludeId);
        }

        return (int) $qb->getQuery()->getSingleScalarResult() === 0;
    }

    /**
     * Get categories with team counts
     */
    public function getCategoriesWithTeamCounts(): array
    {
        $qb = $this->createQueryBuilder('tc')
            ->select('tc', 'COUNT(t.id) as teamCount')
            ->leftJoin('tc.teams', 't', 'WITH', 't.active = true')
            ->where('tc.active = :active')
            ->setParameter('active', true)
            ->groupBy('tc.id')
            ->orderBy('tc.sortOrder', 'ASC')
            ->addOrderBy('tc.name', 'ASC');

        $results = $qb->getQuery()->getResult();
        
        $categories = [];
        foreach ($results as $result) {
            $category = $result[0];
            $category->teamCount = (int) $result['teamCount'];
            $categories[] = $category;
        }

        return $categories;
    }

    /**
     * Get next sort order value
     */
    public function getNextSortOrder(): int
    {
        $qb = $this->createQueryBuilder('tc')
            ->select('MAX(tc.sortOrder)')
            ->where('tc.active = :active')
            ->setParameter('active', true);

        $maxOrder = $qb->getQuery()->getSingleScalarResult();
        
        return ($maxOrder ?? 0) + 10;
    }
}

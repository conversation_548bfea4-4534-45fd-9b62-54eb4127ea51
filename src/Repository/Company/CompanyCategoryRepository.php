<?php

namespace App\Repository\Company;

use App\Constant\GenericResponseMessageKeys;
use App\Entity\Company\CompanyCategory;
use App\Repository\Traits\BaseRepositoryTrait;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<CompanyCategory>
 *
 * @method CompanyCategory|null find($id, $lockMode = null, $lockVersion = null)
 * @method CompanyCategory|null findOneBy(array $criteria, array $orderBy = null)
 * @method CompanyCategory[]    findAll()
 * @method CompanyCategory[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CompanyCategoryRepository extends ServiceEntityRepository
{
  use BaseRepositoryTrait;

  public function __construct(ManagerRegistry $registry)
  {
    parent::__construct($registry, CompanyCategory::class);
  }

  public function save(CompanyCategory $entity, bool $flush = false): void
  {
    $this->getEntityManager()->persist($entity);

    if ($flush) {
      $this->getEntityManager()->flush();
    }
  }

  public function remove(CompanyCategory $entity, bool $flush = false): void
  {
    $this->getEntityManager()->remove($entity);

    if ($flush) {
      $this->getEntityManager()->flush();
    }
  }

  public function getByUuid(string $uuid, bool $throwIfNotFound = true): CompanyCategory|null
  {
    $entity = $this->findOneBy(["uuid" => $uuid]);

    $this->ensureThrowIfNotFound($entity, GenericResponseMessageKeys::COMPANY_CATEGORY_NOT_FOUND, $throwIfNotFound);

    return $entity;
  }
}

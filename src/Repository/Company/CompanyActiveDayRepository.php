<?php

namespace App\Repository\Company;

use App\Entity\Company\CompanyActiveDay;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<CompanyActiveDay>
 *
 * @method CompanyActiveDay|null find($id, $lockMode = null, $lockVersion = null)
 * @method CompanyActiveDay|null findOneBy(array $criteria, array $orderBy = null)
 * @method CompanyActiveDay[]    findAll()
 * @method CompanyActiveDay[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CompanyActiveDayRepository extends ServiceEntityRepository
{
  public function __construct(ManagerRegistry $registry)
  {
    parent::__construct($registry, CompanyActiveDay::class);
  }

  public function save(CompanyActiveDay $entity, bool $flush = false): void
  {
    $this->getEntityManager()->persist($entity);

    if ($flush) {
      $this->getEntityManager()->flush();
    }
  }

  public function remove(CompanyActiveDay $entity, bool $flush = false): void
  {
    $this->getEntityManager()->remove($entity);

    if ($flush) {
      $this->getEntityManager()->flush();
    }
  }

  //    /**
  //     * @return CompanyActiveDay[] Returns an array of CompanyActiveDay objects
  //     */
  //    public function findByExampleField($value): array
  //    {
  //        return $this->createQueryBuilder('c')
  //            ->andWhere('c.exampleField = :val')
  //            ->setParameter('val', $value)
  //            ->orderBy('c.id', 'ASC')
  //            ->setMaxResults(10)
  //            ->getQuery()
  //            ->getResult()
  //        ;
  //    }

  //    public function findOneBySomeField($value): ?CompanyActiveDay
  //    {
  //        return $this->createQueryBuilder('c')
  //            ->andWhere('c.exampleField = :val')
  //            ->setParameter('val', $value)
  //            ->getQuery()
  //            ->getOneOrNullResult()
  //        ;
  //    }
}

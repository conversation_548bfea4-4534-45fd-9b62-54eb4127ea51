<?php

namespace App\Repository\Company;

use App\Constant\GenericResponseMessageKeys;
use App\Entity\Company\CompanyType;
use App\Repository\Traits\BaseRepositoryTrait;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<CompanyType>
 *
 * @method CompanyType|null find($id, $lockMode = null, $lockVersion = null)
 * @method CompanyType|null findOneBy(array $criteria, array $orderBy = null)
 * @method CompanyType[]    findAll()
 * @method CompanyType[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CompanyTypeRepository extends ServiceEntityRepository
{
  use BaseRepositoryTrait;

  public function __construct(ManagerRegistry $registry)
  {
    parent::__construct($registry, CompanyType::class);
  }

  public function save(CompanyType $entity, bool $flush = false): void
  {
    $this->getEntityManager()->persist($entity);

    if ($flush) {
      $this->getEntityManager()->flush();
    }
  }

  public function remove(CompanyType $entity, bool $flush = false): void
  {
    $this->getEntityManager()->remove($entity);

    if ($flush) {
      $this->getEntityManager()->flush();
    }
  }

  public function getByUuid(string $uuid, bool $throwIfNotFound = true): CompanyType|null
  {
    $entity = $this->findOneBy(["uuid" => $uuid]);

    $this->ensureThrowIfNotFound($entity, GenericResponseMessageKeys::COMPANY_TYPE_NOT_FOUND, $throwIfNotFound);

    return $entity;
  }
}

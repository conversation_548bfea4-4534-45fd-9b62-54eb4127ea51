<?php

namespace App\Repository\Shared;

use App\Entity\Shared\ActiveDayType;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ActiveDayType>
 *
 * @method ActiveDayType|null find($id, $lockMode = null, $lockVersion = null)
 * @method ActiveDayType|null findOneBy(array $criteria, array $orderBy = null)
 * @method ActiveDayType[]    findAll()
 * @method ActiveDayType[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ActiveDayTypeRepository extends ServiceEntityRepository
{
  public function __construct(ManagerRegistry $registry)
  {
    parent::__construct($registry, ActiveDayType::class);
  }

  public function save(ActiveDayType $entity, bool $flush = false): void
  {
    $this->getEntityManager()->persist($entity);

    if ($flush) {
      $this->getEntityManager()->flush();
    }
  }

  public function remove(ActiveDayType $entity, bool $flush = false): void
  {
    $this->getEntityManager()->remove($entity);

    if ($flush) {
      $this->getEntityManager()->flush();
    }
  }
}

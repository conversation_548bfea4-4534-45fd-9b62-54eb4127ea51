<?php

namespace App\Repository\Shared;

use App\Dto\GenericResponseMessage;
use App\Entity\Shared\Setting;
use App\Exception\EntityNotFoundException;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Setting>
 *
 * @method Setting|null find($id, $lockMode = null, $lockVersion = null)
 * @method Setting|null findOneBy(array $criteria, array $orderBy = null)
 * @method Setting[]    findAll()
 * @method Setting[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class SettingRepository extends ServiceEntityRepository
{
  public function __construct(ManagerRegistry $registry)
  {
    parent::__construct($registry, Setting::class);
  }

  public function save(Setting $entity, bool $flush = false): void
  {
    $this->getEntityManager()->persist($entity);

    if ($flush) {
      $this->getEntityManager()->flush();
    }
  }

  public function remove(Setting $entity, bool $flush = false): void
  {
    $this->getEntityManager()->remove($entity);

    if ($flush) {
      $this->getEntityManager()->flush();
    }
  }

  public function getByUuid(string $uuid, bool $throwIfNotFound = true): Setting
  {
    $setting = $this->findOneBy(["uuid" => $uuid]);

    if (empty($setting) && $throwIfNotFound) {
      throw new EntityNotFoundException(new GenericResponseMessage("The item with the provided uuid doesn't exists", "setting_not_found"));
    }
    return $setting;
  }

  public function getByCode(string $code, bool $throwIfNotFound = true): Setting
  {
    $settings = $this->findOneBy(["code" => $code]);

    if(empty($settings) && $throwIfNotFound){
      throw new EntityNotFoundException(new GenericResponseMessage("The item with the provided code doesn't exists", "setting_not_found"));
    }

    return $settings;
  }
}

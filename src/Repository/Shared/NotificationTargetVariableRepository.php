<?php

namespace App\Repository\Shared;

use App\Entity\Shared\NotificationTargetVariable;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<NotificationTargetVariable>
 *
 * @method NotificationTargetVariable|null find($id, $lockMode = null, $lockVersion = null)
 * @method NotificationTargetVariable|null findOneBy(array $criteria, array $orderBy = null)
 * @method NotificationTargetVariable[]    findAll()
 * @method NotificationTargetVariable[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class NotificationTargetVariableRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, NotificationTargetVariable::class);
    }

    public function save(NotificationTargetVariable $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(NotificationTargetVariable $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

//    /**
//     * @return NotificationTargetVariable[] Returns an array of NotificationTargetVariable objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('n')
//            ->andWhere('n.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('n.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?NotificationTargetVariable
//    {
//        return $this->createQueryBuilder('n')
//            ->andWhere('n.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }
}

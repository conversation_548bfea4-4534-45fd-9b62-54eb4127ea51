<?php

namespace App\Repository\Shared;

use App\Entity\Shared\SettingVersion;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<SettingVersion>
 *
 * @method SettingVersion|null find($id, $lockMode = null, $lockVersion = null)
 * @method SettingVersion|null findOneBy(array $criteria, array $orderBy = null)
 * @method SettingVersion[]    findAll()
 * @method SettingVersion[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class SettingVersionRepository extends ServiceEntityRepository
{
  public function __construct(ManagerRegistry $registry)
  {
    parent::__construct($registry, SettingVersion::class);
  }

  public function save(SettingVersion $entity, bool $flush = false): void
  {
    $this->getEntityManager()->persist($entity);

    if ($flush) {
      $this->getEntityManager()->flush();
    }
  }

  public function remove(SettingVersion $entity, bool $flush = false): void
  {
    $this->getEntityManager()->remove($entity);

    if ($flush) {
      $this->getEntityManager()->flush();
    }
  }
}

<?php

namespace App\Repository\Shared;

use App\Entity\Shared\MembershipPlan;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<MembershipPlan>
 *
 * @method MembershipPlan|null find($id, $lockMode = null, $lockVersion = null)
 * @method MembershipPlan|null findOneBy(array $criteria, array $orderBy = null)
 * @method MembershipPlan[]    findAll()
 * @method MembershipPlan[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class MembershipPlanRepository extends ServiceEntityRepository
{
  public function __construct(ManagerRegistry $registry)
  {
    parent::__construct($registry, MembershipPlan::class);
  }

  public function save(MembershipPlan $entity, bool $flush = false): void
  {
    $this->getEntityManager()->persist($entity);

    if ($flush) {
      $this->getEntityManager()->flush();
    }
  }

  public function remove(MembershipPlan $entity, bool $flush = false): void
  {
    $this->getEntityManager()->remove($entity);

    if ($flush) {
      $this->getEntityManager()->flush();
    }
  }
}

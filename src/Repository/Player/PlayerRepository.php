<?php

namespace App\Repository\Player;

use App\Entity\Player\Player;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Player>
 *
 * @method Player|null find($id, $lockMode = null, $lockVersion = null)
 * @method Player|null findOneBy(array $criteria, array $orderBy = null)
 * @method Player[]    findAll()
 * @method Player[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class PlayerRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Player::class);
    }

    public function save(Player $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Player $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * Find top scorers
     */
    public function findTopScorers(int $limit = 10): array
    {
        return $this->createQueryBuilder('p')
            ->where('p.active = :active')
            ->setParameter('active', true)
            ->orderBy('p.totalGoals', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Find players by team
     */
    public function findByTeam($team): array
    {
        return $this->createQueryBuilder('p')
            ->where('p.team = :team')
            ->andWhere('p.active = :active')
            ->setParameter('team', $team)
            ->setParameter('active', true)
            ->orderBy('p.jerseyNumber', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Find player by full name (for duplicate checking)
     */
    public function findByFullName(string $firstName, string $lastName, ?int $excludeId = null): ?Player
    {
        $qb = $this->createQueryBuilder('p')
            ->where('p.firstName = :firstName')
            ->andWhere('p.lastName = :lastName')
            ->andWhere('p.active = :active')
            ->setParameter('firstName', $firstName)
            ->setParameter('lastName', $lastName)
            ->setParameter('active', true);

        if ($excludeId) {
            $qb->andWhere('p.id != :excludeId')
               ->setParameter('excludeId', $excludeId);
        }

        return $qb->getQuery()->getOneOrNullResult();
    }
}
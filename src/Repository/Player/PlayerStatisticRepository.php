<?php

namespace App\Repository\Player;

use App\Entity\Player\PlayerStatistic;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<PlayerStatistic>
 *
 * @method PlayerStatistic|null find($id, $lockMode = null, $lockVersion = null)
 * @method PlayerStatistic|null findOneBy(array $criteria, array $orderBy = null)
 * @method PlayerStatistic[]    findAll()
 * @method PlayerStatistic[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class PlayerStatisticRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, PlayerStatistic::class);
    }

    public function save(PlayerStatistic $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(PlayerStatistic $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * Find statistics by encounter
     */
    public function findByEncounter(string $encounterId): array
    {
        return $this->createQueryBuilder('ps')
            ->where('ps.encounterId = :encounterId')
            ->setParameter('encounterId', $encounterId)
            ->getQuery()
            ->getResult();
    }
}
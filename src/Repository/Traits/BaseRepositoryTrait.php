<?php

namespace App\Repository\Traits;

use App\Dto\GenericResponseMessage;
use App\Dto\Shared\In\FindAllFilterIn;
use App\Exception\EntityNotFoundException;
use Doctrine\ORM\Tools\Pagination\Paginator;

trait BaseRepositoryTrait
{
  public function getItems(FindAllFilterIn $filter): array
  {
    $queryBuilder = $this->createQueryBuilder('a');
    if (!empty($filter->getSearch())) {
      $queryBuilder
        ->andWhere('a.rowIndex LIKE :rowIndex')
        ->setParameter('rowIndex', '%' . $filter->getSearch() . '%');
    }
    $filterParams = $filter->getFilters()->toArray();
    if (!empty($filterParams)) {
      foreach ($filterParams as $param) {
        $queryBuilder
          ->andWhere('a.' . $param->getField() . ' IN (:' . $param->getField() . ')')
          ->setParameter($param->getField(), $param->getValues());
      }
    }

    $result = [];
    if (is_numeric($filter->getLimit()) && is_numeric($filter->getOffset())) {
      $queryBuilder->orderBy('a.id', 'DESC');
      $queryBuilder->setMaxResults($filter->getLimit());
      $queryBuilder->setFirstResult($filter->getOffset());

      $paginator = new Paginator($queryBuilder);
      $result['items'] = $paginator->getIterator();
      $result['total'] = $paginator->count();
    } else {
      $results = $queryBuilder
        ->getQuery()
        ->getResult();

      $result['items'] = $results;
      $result['total'] = count($results);
    }
    return $result;
  }

  public function ensureThrowIfNotFound(mixed $entity, string $errorKey, bool $throwIfNotFound = true): void
  {
    if (empty($entity) && $throwIfNotFound) {
      throw new EntityNotFoundException(new GenericResponseMessage("The item with the provided uuid doesn't exists", $errorKey));
    }
  }
}

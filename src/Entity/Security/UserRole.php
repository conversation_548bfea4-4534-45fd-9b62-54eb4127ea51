<?php

namespace App\Entity\Security;

use App\Entity\Traits\DeActivatableTrait;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Repository\Security\RoleRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: "user_roles")]
#[ORM\Entity(repositoryClass: RoleRepository::class)]
class UserRole
{
  use IDableTrait, DeActivatableTrait, TimestampableTrait;

  #[ORM\ManyToOne(inversedBy: 'roles')]
  private ?User $user = null;

  #[ORM\ManyToOne]
  #[ORM\JoinColumn(nullable: false)]
  private ?Role $role = null;

  #[ORM\PrePersist]
  public function setDefaultValues(): void
  {
    if (empty($this->id)) {
      $this->active = $this->active ?? true;
    }
  }

  public function getRole(): ?Role
  {
    return $this->role;
  }

  public function setRole(?Role $role): static
  {
    $this->role = $role;

    return $this;
  }

  public function getUser(): ?User
  {
    return $this->user;
  }

  public function setUser(?User $user): static
  {
    $this->user = $user;

    return $this;
  }
}

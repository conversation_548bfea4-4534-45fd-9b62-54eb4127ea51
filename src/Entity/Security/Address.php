<?php

namespace App\Entity\Security;

use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\IndexableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Entity\Traits\UuidableTrait;
use App\Repository\Security\AddressRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: "addresses")]
#[ORM\Entity(repositoryClass: AddressRepository::class)]
class Address
{
  use IDableTrait, UuidableTrait, IndexableTrait, TimestampableTrait;

  #[ORM\Column(length: 255, nullable: true)]
  private ?string $website = null;

  #[ORM\Column(length: 255, nullable: true)]
  private ?string $contactEmail = null;

  #[ORM\Column(length: 255, nullable: true)]
  private ?string $contactPhoneNumber = null;

  #[ORM\Column(length: 255, nullable: true)]
  private ?string $streetAddress = null;

  #[ORM\Column(length: 255, nullable: true)]
  private ?string $city = null;

  #[ORM\Column(nullable: true)]
  private ?int $zipCode = null;

  #[ORM\Column(length: 3, nullable: true)]
  private ?string $country = null;

  #[ORM\Column(length: 255, nullable: true)]
  private ?string $longitude = null;

  #[ORM\Column(length: 255, nullable: true)]
  private ?string $latitude = null;

  #[ORM\Column(options: ["default" => 0])]
  private ?bool $contactEmailVerified = null;

  #[ORM\Column(options: ["default" => 0])]
  private ?bool $contactPhoneNumberVerified = null;

  #[ORM\Column(type: 'addressType')]
  private ?string $type = null;

  #[ORM\ManyToOne(inversedBy: 'addresses')]
  #[ORM\JoinColumn(nullable: false)]
  private ?User $user = null;

  #[ORM\PrePersist]
  public function setDefaultValues(): void
  {
    $this->rowIndex = $this->buildRowIndex();
  }

  public function getWebsite(): ?string
  {
    return $this->website;
  }

  public function setWebsite(?string $website): static
  {
    $this->website = $website;

    return $this;
  }

  public function getContactEmail(): ?string
  {
    return $this->contactEmail;
  }

  public function setContactEmail(?string $contactEmail): static
  {
    $this->contactEmail = $contactEmail;

    return $this;
  }

  public function getContactPhoneNumber(): ?string
  {
    return $this->contactPhoneNumber;
  }

  public function setContactPhoneNumber(?string $contactPhoneNumber): static
  {
    $this->contactPhoneNumber = $contactPhoneNumber;

    return $this;
  }

  public function getStreetAddress(): ?string
  {
    return $this->streetAddress;
  }

  public function setStreetAddress(?string $streetAddress): static
  {
    $this->streetAddress = $streetAddress;

    return $this;
  }

  public function getCity(): ?string
  {
    return $this->city;
  }

  public function setCity(?string $city): static
  {
    $this->city = $city;

    return $this;
  }

  public function getZipCode(): ?int
  {
    return $this->zipCode;
  }

  public function setZipCode(?int $zipCode): static
  {
    $this->zipCode = $zipCode;

    return $this;
  }

  public function getCountry(): ?string
  {
    return $this->country;
  }

  public function setCountry(?string $country): static
  {
    $this->country = $country;

    return $this;
  }

  public function getLongitude(): ?string
  {
    return $this->longitude;
  }

  public function setLongitude(?string $longitude): static
  {
    $this->longitude = $longitude;

    return $this;
  }

  public function getLatitude(): ?string
  {
    return $this->latitude;
  }

  public function setLatitude(?string $latitude): static
  {
    $this->latitude = $latitude;

    return $this;
  }


  public function isContactEmailVerified(): ?bool
  {
    return $this->contactEmailVerified;
  }

  public function setContactEmailVerified(bool $contactEmailVerified): static
  {
    $this->contactEmailVerified = $contactEmailVerified;

    return $this;
  }

  public function isPhoneNumberVerified(): ?bool
  {
    return $this->contactPhoneNumberVerified;
  }

  public function setPhoneNumberVerified(bool $contactPhoneNumberVerified): static
  {
    $this->contactPhoneNumberVerified = $contactPhoneNumberVerified;

    return $this;
  }

  public function getUser(): ?User
  {
    return $this->user;
  }

  public function setUser(?User $user): static
  {
    $this->user = $user;

    return $this;
  }

  public function getType()
  {
    return $this->type;
  }

  public function setType($type): static
  {
    $this->type = $type;

    return $this;
  }

  private function getIndexableFields(): array
  {
    return ["uuid", "contactPhoneNumber", "website", "streetAddress", "city", "contactEmail", "zipCode", "country"];
  }
}

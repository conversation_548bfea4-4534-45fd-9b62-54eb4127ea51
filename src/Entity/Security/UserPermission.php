<?php

namespace App\Entity\Security;

use App\Entity\Traits\DeActivatableTrait;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Repository\Security\PermissionRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: "user_permissions")]
#[ORM\Entity(repositoryClass: PermissionRepository::class)]
class UserPermission
{
  use IDableTrait, DeActivatableTrait, TimestampableTrait;

  #[ORM\ManyToOne(inversedBy: 'permissions')]
  private ?User $user = null;

  #[ORM\ManyToOne]
  #[ORM\JoinColumn(nullable: false)]
  private ?Permission $permission = null;

  #[ORM\PrePersist]
  public function setDefaultValues(): void
  {
    if (empty($this->id)) {
      $this->active = $this->active ?? true;
    }
  }

  public function getPermission(): ?Permission
  {
    return $this->permission;
  }

  public function setPermission(?Permission $permission): static
  {
    $this->permission = $permission;

    return $this;
  }

  public function getUser(): ?User
  {
    return $this->user;
  }

  public function setUser(?User $user): static
  {
    $this->user = $user;

    return $this;
  }
}

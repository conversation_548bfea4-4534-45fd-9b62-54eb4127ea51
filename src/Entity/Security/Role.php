<?php

namespace App\Entity\Security;

use App\Entity\Traits\CodeableTrait;
use App\Entity\Traits\DeActivatableTrait;
use App\Entity\Traits\DescribableTrait;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\IndexableTrait;
use App\Entity\Traits\ProgicielableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Entity\Traits\UuidableTrait;
use App\Repository\Security\RoleRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: "roles")]
#[ORM\Entity(repositoryClass: RoleRepository::class)]
class Role
{
  use IDableTrait, CodeableTrait, UuidableTrait, DescribableTrait, DeActivatableTrait, ProgicielableTrait, IndexableTrait, TimestampableTrait;

  #[ORM\OneToMany(mappedBy: 'role', targetEntity: RolePermission::class)]
  private Collection $permissions;

  public function __construct()
  {
    $this->permissions = new ArrayCollection();
  }

  #[ORM\PrePersist]
  public function setDefaultValues(): void
  {
    if (empty($this->id)) {
      $this->active = $this->active ?? true;
      $this->progiciel = $this->progiciel ?? false;
    }
  }


  public function getPermissions(): array
  {
    return [];
  }

  /**
   * @return Collection<int, RolePermission>
   */
  public function getRolePermissions(): Collection
  {
    return $this->permissions;
  }

  public function addPermission(RolePermission $rolePermission): static
  {
    if (!$this->permissions->contains($rolePermission)) {
      $this->permissions->add($rolePermission);
      $rolePermission->setRole($this);
    }

    return $this;
  }

  public function hasPermission(RolePermission $rolePermission): bool
  {
    return $this->permissions->exists(function ($key, $element) use ($rolePermission) {
      return (!empty($element->getRole()) &&
        !empty($element->getPermission()) &&
        !empty($rolePermission->getRole()) &&
        !empty($rolePermission->getPermission()) &&
        $element->getRole()->getUuid() == $rolePermission->getRole()->getUuid() &&
        $element->getPermission()->getUuid() == $rolePermission->getPermission()->getUuid());
    });
  }

  public function removePermission(RolePermission $rolePermission): static
  {
    if ($this->permissions->removeElement($rolePermission)) {
      // set the owning side to null (unless already changed)
      if ($rolePermission->getRole() === $this) {
        $rolePermission->setRole(null);
      }
    }

    return $this;
  }

  private function getIndexableFields(): array
  {
    return ["uuid", "code", "description"];
  }
}

<?php

namespace App\Entity\Security;

use App\Entity\Activity\ActivityPayment;
use App\Entity\Activity\ActivityReservation;
use App\Entity\Chat\Request;
use App\Entity\Company\Company;
use App\Entity\Payment\License;
use App\Entity\Payment\Payment;
use App\Entity\Shared\File;
use App\Repository\Security\UserRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Security\Core\User\UserInterface;
use App\Entity\Traits\TimestampableTrait;
use App\Entity\Traits\DeActivatableTrait;
use App\Entity\Traits\DescribableTrait;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\IndexableTrait;
use App\Entity\Traits\UuidableTrait;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;

#[ORM\Table(name: "users")]
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: UserRepository::class)]
class User implements UserInterface, PasswordAuthenticatedUserInterface
{
  use IDableTrait, TimestampableTrait, DeActivatableTrait, UuidableTrait, DescribableTrait, IndexableTrait;

  #[ORM\Column(length: 20, nullable: true, unique: true)]
  private ?string $phoneNumber = null;

  #[ORM\Column(length: 50, nullable: true)]
  private ?string $firstName = null;

  #[ORM\Column(length: 50, nullable: true)]
  private ?string $lastName = null;

  #[ORM\Column(type: "genderType", nullable: true)]
  private ?string $gender = null;

  #[ORM\Column(type: Types::DATE_MUTABLE, nullable: true)]
  private ?\DateTimeInterface $dateOfBirth = null;

  #[ORM\Column(nullable: true)]
  private ?int $weight = null;

  #[ORM\Column(nullable: true)]
  private ?int $height = null;

  #[ORM\Column(length: 100, nullable: true, unique: true)]
  private ?string $email = null;

  #[ORM\Column(options: ["default" => 0])]
  private ?bool $emailVerified = false;

  #[ORM\Column(options: ["default" => 0])]
  private ?bool $phoneNumberVerified = false;

  #[ORM\Column(length: 100, nullable: true, unique: true)]
  private ?string $username = null;

  #[ORM\Column(length: 255, nullable: true)]
  private ?string $displayName = null;

  #[ORM\Column(type: 'userType')]
  private ?string $type = null;

  #[ORM\Column(type: 'userStatusType')]
  private ?string $status = null;

  #[ORM\ManyToOne(inversedBy: 'users')]
  #[ORM\JoinColumn(nullable: true)]
  private ?Company $company = null;

  #[ORM\OneToMany(mappedBy: 'user', targetEntity: Address::class, cascade: ['persist', 'remove'])]
  private Collection $addresses;

  #[ORM\OneToOne(mappedBy: 'user', cascade: ['persist', 'remove'])]
  private ?AuthentificationCode $authentificationCode = null;

  #[ORM\OneToMany(mappedBy: 'user', targetEntity: UserMeta::class, orphanRemoval: true, cascade: ['persist', 'remove'])]
  private Collection $metas;

  #[ORM\OneToMany(mappedBy: 'user', targetEntity: UserRole::class)]
  private Collection $roles;

  #[ORM\OneToMany(mappedBy: 'user', targetEntity: UserRole::class)]
  private Collection $permissions;

  #[ORM\ManyToOne(targetEntity: self::class, inversedBy: 'logicalUsers')]
  private ?self $owner = null;

  #[ORM\OneToMany(mappedBy: 'owner', targetEntity: self::class)]
  private Collection $logicalUsers;

  #[ORM\OneToMany(mappedBy: 'owner', targetEntity: File::class)]
  private Collection $ownerFiles;

  #[ORM\OneToMany(mappedBy: 'editor', targetEntity: File::class)]
  private Collection $editorFiles;

  #[ORM\OneToMany(mappedBy: 'user', targetEntity: ActivityReservation::class)]
  private Collection $reservations;

  #[ORM\OneToMany(mappedBy: 'creator', targetEntity: Request::class, orphanRemoval: true)]
  private Collection $requests;

  #[ORM\OneToMany(mappedBy: 'user', targetEntity: Payment::class)]
  private Collection $payments;

  #[ORM\OneToMany(mappedBy: 'user', targetEntity: License::class, orphanRemoval: true)]
  private Collection $licenses;

  public function __construct()
  {
    $this->metas = new ArrayCollection();
    $this->roles = new ArrayCollection();
    $this->addresses = new ArrayCollection();
    $this->permissions = new ArrayCollection();
    $this->logicalUsers = new ArrayCollection();
    $this->ownerFiles = new ArrayCollection();
    $this->editorFiles = new ArrayCollection();
    $this->reservations = new ArrayCollection();
    $this->requests = new ArrayCollection();
    $this->payments = new ArrayCollection();
    $this->licenses = new ArrayCollection();
  }

  #[ORM\PrePersist]
  #[ORM\PreUpdate]
  public function setDefaultValues(): void
  {
    $this->displayName = $this->getDisplayName();
    $this->rowIndex = $this->buildRowIndex();
  }

  public function getPhoneNumber(): ?string
  {
    return $this->phoneNumber;
  }

  public function setPhoneNumber(?string $phoneNumber): static
  {
    $this->phoneNumber = $phoneNumber;

    return $this;
  }

  public function getFirstName(): ?string
  {
    return $this->firstName;
  }

  public function setFirstName(?string $firstName): static
  {
    $this->firstName = $firstName;

    return $this;
  }

  public function getLastName(): ?string
  {
    return $this->lastName;
  }

  public function setLastName(?string $lastName): static
  {
    $this->lastName = $lastName;

    return $this;
  }

  public function getGender(): ?string
  {
    return $this->gender;
  }

  public function setGender(?string $gender): static
  {
    $this->gender = $gender;

    return $this;
  }

  public function getDateOfBirth(): ?\DateTimeInterface
  {
    return $this->dateOfBirth;
  }

  public function setDateOfBirth(?\DateTimeInterface $dateOfBirth): static
  {
    $this->dateOfBirth = $dateOfBirth;

    return $this;
  }

  public function getWeight(): ?int
  {
    return $this->weight;
  }

  public function setWeight(?int $weight): static
  {
    $this->weight = $weight;

    return $this;
  }

  public function getHeight(): ?int
  {
    return $this->height;
  }

  public function setHeight(?int $height): static
  {
    $this->height = $height;

    return $this;
  }

  public function getEmail(): ?string
  {
    return $this->email;
  }

  public function setEmail(?string $email): static
  {
    $this->email = $email;

    return $this;
  }

  public function getAuthentificationCode(): ?AuthentificationCode
  {
    return $this->authentificationCode;
  }

  public function setAuthentificationCode(?AuthentificationCode $authentificationCode): static
  {
    // set the owning side of the relation if necessary
    if (!empty($authentificationCode) && $authentificationCode->getUser() !== $this) {
      $authentificationCode->setUser($this);
    }

    $this->authentificationCode = $authentificationCode;

    return $this;
  }

  /**
   * @return Collection<int, UserMeta>
   */
  public function getMetas(): Collection
  {
    return $this->metas;
  }

  public function addMeta(UserMeta $userMeta): static
  {
    $this->removeMetaIfExistsByCode($userMeta->getCode());
    if (!$this->metas->contains($userMeta)) {
      $this->metas->add($userMeta);
      $userMeta->setUser($this);
    }
    return $this;
  }

  public function removeMeta(UserMeta $userMeta): static
  {
    if ($this->metas->removeElement($userMeta)) {
      // set the owning side to null (unless already changed)
      if ($userMeta->getUser() === $this) {
        $userMeta->setUser(null);
      }
    }

    return $this;
  }

  public function getRoles(): array
  {
    return ['ROLE_USER'];
  }

  /**
   * @return Collection<int, UserRole>
   */
  public function getUserRoles(): Collection
  {
    return $this->roles;
  }

  public function addRole(UserRole $userRole): static
  {
    if (!$this->roles->contains($userRole)) {
      $this->roles->add($userRole);
      $userRole->setUser($this);
    }

    return $this;
  }

  public function removeRole(UserRole $userRole): static
  {
    if ($this->roles->removeElement($userRole)) {
      // set the owning side to null (unless already changed)
      if ($userRole->getUser() === $this) {
        $userRole->setUser(null);
      }
    }

    return $this;
  }

  public function eraseCredentials(): void
  {
  }


  public function getPermissions(): array
  {
    return [];
  }

  /**
   * @return Collection<int, UserPermission>
   */
  public function getUserPermissions(): Collection
  {
    return $this->permissions;
  }

  public function addPermission(UserPermission $userPermission): static
  {
    if (!$this->permissions->contains($userPermission)) {
      $this->permissions->add($userPermission);
      $userPermission->setUser($this);
    }

    return $this;
  }

  public function removePermission(UserPermission $userPermission): static
  {
    if ($this->permissions->removeElement($userPermission)) {
      // set the owning side to null (unless already changed)
      if ($userPermission->getUser() === $this) {
        $userPermission->setUser(null);
      }
    }

    return $this;
  }

  /**
   * @see UserInterface
   */
  public function getUserIdentifier(): string
  {
    if (!empty($this->phoneNumber)) {
      return $this->phoneNumber;
    }
    return $this->email;
  }

  /**
   * @see PasswordAuthenticatedUserInterface
   */
  public function getPassword(): ?string
  {
    if (!empty($this->getAuthentificationCode())) {
      return $this->getAuthentificationCode()->getCode();
    }
    return null;
  }

  public function getMetaCodes()
  {
    $mappedCollection = $this->getMetas()->map(function ($meta) {
      return $meta->getCode();
    });
    return $mappedCollection->toArray();
  }

  public function hasMeta(string $code)
  {
    return in_array($code, $this->getMetaCodes());
  }



  public function getMetaValueByCode(string $code, $defaultValue = '')
  {
    $first = $this->getMetas()->findFirst(function (int $key, UserMeta $value) use ($code) {
      return $value->getCode() === $code;
    });
    if (!empty($first)) {
      return $first->getValue();
    }
    return $defaultValue;
  }


  public function removeMetaIfExistsByCode(string $code)
  {
    $first = $this->getMetas()->findFirst(function (int $key, UserMeta $value) use ($code) {
      return $value->getCode() === $code;
    });
    if (!empty($first)) {
      $this->removeMeta($first);
    }
  }


  public function isEmailVerified(): ?bool
  {
    return $this->emailVerified;
  }

  public function setEmailVerified(bool $emailVerified): static
  {
    $this->emailVerified = $emailVerified;

    return $this;
  }

  public function isPhoneNumberVerified(): ?bool
  {
    return $this->phoneNumberVerified;
  }

  public function setPhoneNumberVerified(bool $phoneNumberVerified): static
  {
    $this->phoneNumberVerified = $phoneNumberVerified;

    return $this;
  }

  public function getUsername(): ?string
  {
    if (!empty($this->username)) {
      return $this->username;
    } else if (!empty($this->phoneNumber)) {
      return $this->phoneNumber;
    } else if (!empty($this->email)) {
      return $this->email;
    }
    return $this->username;
  }

  public function setUsername(?string $username): static
  {
    $this->username = $username;

    return $this;
  }

  public function getDisplayName(): ?string
  {
    if (!empty($this->firstName) && !empty($this->lastName)) {
      return $this->firstName . ' ' . $this->lastName;
    } else  if (!empty($this->displayName)) {
      return $this->displayName;
    } else if (!empty($this->lastName)) {
      return $this->lastName;
    } else if (!empty($this->firstName)) {
      return $this->firstName;
    } else if (!empty($this->email)) {
      return $this->email;
    } else if (!empty($this->phoneNumber)) {
      return $this->phoneNumber;
    } else if (!empty($this->username)) {
      return $this->username;
    }
    return $this->displayName;
  }

  public function setDisplayName(?string $displayName): static
  {
    $this->displayName = $displayName;

    return $this;
  }

  /**
   * @return Collection<int, Address>
   */
  public function getAddresses(): Collection
  {
    return $this->addresses;
  }

  public function addAddress(Address $address): static
  {
    if (!$this->addresses->contains($address)) {
      $this->addresses->add($address);
      $address->setUser($this);
    }

    return $this;
  }

  public function removeAddress(Address $address): static
  {
    if ($this->addresses->removeElement($address)) {
      // set the owning side to null (unless already changed)
      if ($address->getUser() === $this) {
        $address->setUser(null);
      }
    }

    return $this;
  }

  public function getType()
  {
    return $this->type;
  }

  public function setType($type): static
  {
    $this->type = $type;

    return $this;
  }

  public function getCompany(): ?Company
  {
    return $this->company;
  }

  public function setCompany(?Company $company): static
  {
    $this->company = $company;

    return $this;
  }

  public function getStatus()
  {
    return $this->status;
  }

  public function setStatus($status): static
  {
    $this->status = $status;

    return $this;
  }

  private function getIndexableFields(): array
  {
    return ["uuid", "phoneNumber", "firstName", "lastName", "gender", "email", "username", "displayName", "type", "status"];
  }

  public function getOwner(): ?self
  {
    return $this->owner;
  }

  public function setOwner(?self $owner): static
  {
    $this->owner = $owner;

    return $this;
  }

  /**
   * @return Collection<int, self>
   */
  public function getLogicalUsers(): Collection
  {
    return $this->logicalUsers;
  }

  public function addLogicalUser(self $logicalUser): static
  {
    if (!$this->logicalUsers->contains($logicalUser)) {
      $this->logicalUsers->add($logicalUser);
      $logicalUser->setOwner($this);
    }

    return $this;
  }

  public function removeLogicalUser(self $logicalUser): static
  {
    if ($this->logicalUsers->removeElement($logicalUser)) {
      // set the owning side to null (unless already changed)
      if ($logicalUser->getOwner() === $this) {
        $logicalUser->setOwner(null);
      }
    }

    return $this;
  }

  /**
   * @return Collection<int, File>
   */
  public function getOwnerFiles(): Collection
  {
    return $this->ownerFiles;
  }

  public function addOwnerFile(File $ownerFile): static
  {
    if (!$this->ownerFiles->contains($ownerFile)) {
      $this->ownerFiles->add($ownerFile);
      $ownerFile->setOwner($this);
    }

    return $this;
  }

  public function removeOwnerFile(File $ownerFile): static
  {
    if ($this->ownerFiles->removeElement($ownerFile)) {
      // set the owning side to null (unless already changed)
      if ($ownerFile->getOwner() === $this) {
        $ownerFile->setOwner(null);
      }
    }

    return $this;
  }

  /**
   * @return Collection<int, File>
   */
  public function getEditorFiles(): Collection
  {
    return $this->editorFiles;
  }

  public function addEditorFiles(File $editorFiles): static
  {
    if (!$this->editorFiles->contains($editorFiles)) {
      $this->editorFiles->add($editorFiles);
      $editorFiles->setEditor($this);
    }

    return $this;
  }

  public function removeEditorFiles(File $editorFiles): static
  {
    if ($this->editorFiles->removeElement($editorFiles)) {
      // set the owning side to null (unless already changed)
      if ($editorFiles->getEditor() === $this) {
        $editorFiles->setEditor(null);
      }
    }

    return $this;
  }

  /**
   * @return Collection<int, ActivityReservation>
   */
  public function getReservations(): Collection
  {
    return $this->reservations;
  }

  public function addReservation(ActivityReservation $reservation): static
  {
    if (!$this->reservations->contains($reservation)) {
      $this->reservations->add($reservation);
      $reservation->setUser($this);
    }

    return $this;
  }

  public function removeReservation(ActivityReservation $reservation): static
  {
    if ($this->reservations->removeElement($reservation)) {
      // set the owning side to null (unless already changed)
      if ($reservation->getUser() === $this) {
        $reservation->setUser(null);
      }
    }

    return $this;
  }

  /**
   * @return Collection<int, Request>
   */
  public function getRequests(): Collection
  {
    return $this->requests;
  }

  public function addRequest(Request $request): static
  {
    if (!$this->requests->contains($request)) {
      $this->requests->add($request);
      $request->setCreator($this);
    }

    return $this;
  }

  public function removeRequest(Request $request): static
  {
    if ($this->requests->removeElement($request)) {
      // set the owning side to null (unless already changed)
      if ($request->getCreator() === $this) {
        $request->setCreator(null);
      }
    }

    return $this;
  }

  /**
   * @return Collection<int, Payment>
   */
  public function getPayments(): Collection
  {
      return $this->payments;
  }

  public function spentAmount(): float
  {
      return $this->payments
          ->filter(fn(Payment $p) => $p->getStatus() === 'SUCCESSFUL')
          ->reduce(fn(float $total, Payment $p) => $total + $p->getAmount(), 0.0);
  }

  public function addPayment(Payment $payment): static
  {
      if (!$this->payments->contains($payment)) {
          $this->payments->add($payment);
          $payment->setUser($this);
      }

      return $this;
  }

  public function removePayment(Payment $payment): static
  {
      if ($this->payments->removeElement($payment)) {
          // set the owning side to null (unless already changed)
          if ($payment->getUser() === $this) {
              $payment->setUser(null);
          }
      }

      return $this;
  }

  /**
   * @return Collection<int, License>
   */
  public function getLicenses(): Collection
  {
      return $this->licenses;
  }

  public function addLicense(License $license): static
  {
      if (!$this->licenses->contains($license)) {
          $this->licenses->add($license);
          $license->setUser($this);
      }

      return $this;
  }

  public function removeLicense(License $license): static
  {
      if ($this->licenses->removeElement($license)) {
          // set the owning side to null (unless already changed)
          if ($license->getUser() === $this) {
              $license->setUser(null);
          }
      }

      return $this;
  }

 
}

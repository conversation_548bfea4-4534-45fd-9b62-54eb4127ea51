<?php

namespace App\Entity\Security;

use App\Entity\Traits\DeActivatableTrait;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Repository\Security\RoleRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: "role_permissions")]
#[ORM\Entity(repositoryClass: RoleRepository::class)]
class RolePermission
{
  use IDableTrait, DeActivatableTrait, TimestampableTrait;

  #[ORM\ManyToOne]
  private ?Permission $permission = null;

  #[ORM\ManyToOne(inversedBy: 'permissions')]
  #[ORM\JoinColumn(nullable: false)]
  private ?Role $role = null;

  #[ORM\PrePersist]
  public function setDefaultValues(): void
  {
    if (empty($this->id)) {
      $this->active = $this->active ?? true;
    }
  }

  public function getRole(): ?Role
  {
    return $this->role;
  }

  public function setRole(?Role $role): static
  {
    $this->role = $role;

    return $this;
  }

  public function getPermission(): ?Permission
  {
    return $this->permission;
  }

  public function setPermission(?Permission $permission): static
  {
    $this->permission = $permission;

    return $this;
  }
}

<?php

namespace App\Entity\Security;

use App\Entity\Traits\CodeableTrait;
use App\Entity\Traits\DeActivatableTrait;
use App\Entity\Traits\DescribableTrait;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\IndexableTrait;
use App\Entity\Traits\ProgicielableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Entity\Traits\UuidableTrait;
use App\Repository\Security\PermissionRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: "permissions")]
#[ORM\Entity(repositoryClass: PermissionRepository::class)]
class Permission
{
  use IDableTrait, CodeableTrait, UuidableTrait, DescribableTrait, DeActivatableTrait, ProgicielableTrait, IndexableTrait, TimestampableTrait;

  #[ORM\PrePersist]
  public function setDefaultValues(): void
  {
    if (empty($this->id)) {
      $this->active = $this->active ?? true;
      $this->progiciel = $this->progiciel ?? false;
    }
  }

  private function getIndexableFields(): array
  {
    return ["uuid", "code", "description"];
  }
}

<?php

namespace App\Entity\Security;

use App\Entity\Traits\IDableTrait;
use App\Repository\Security\AuthentificationCodeRepository;
use Doctrine\ORM\Mapping as ORM;
use App\Entity\Traits\TimestampableTrait;

#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: "authentification_codes")]
#[ORM\Entity(repositoryClass: AuthentificationCodeRepository::class)]
class AuthentificationCode
{
  use IDableTrait, TimestampableTrait;

  #[ORM\Column]
  private ?int $code = null;

  #[ORM\Column]
  private ?int $trialsCode = null;

  #[ORM\Column]
  private ?int $generatedCode = null;

  #[ORM\OneToOne(inversedBy: 'authentificationCode')]
  #[ORM\JoinColumn(nullable: false)]
  private ?User $user = null;

  #[ORM\Column(type: 'loginType')]
  private $type = null;

  public function getCode(): ?int
  {
    return $this->code;
  }

  public function setCode(int $code): static
  {
    $this->code = $code;

    return $this;
  }

  public function getTrialsCode(): ?int
  {
    return $this->trialsCode;
  }

  public function setTrialsCode(int $trialsCode): static
  {
    $this->trialsCode = $trialsCode;

    return $this;
  }

  public function getGeneratedCode(): ?int
  {
    return $this->generatedCode;
  }

  public function setGeneratedCode(int $generatedCode): static
  {
    $this->generatedCode = $generatedCode;

    return $this;
  }

  public function getUser(): ?User
  {
    return $this->user;
  }

  public function setUser(User $user): static
  {
    $this->user = $user;

    return $this;
  }

  public function getType()
  {
    return $this->type;
  }

  public function setType($type): static
  {
    $this->type = $type;

    return $this;
  }
}

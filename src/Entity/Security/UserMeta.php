<?php

namespace App\Entity\Security;

use App\Entity\Traits\DescribableTrait;
use App\Entity\Traits\IDableTrait;
use App\Repository\Security\UserMetaRepository;
use Doctrine\ORM\Mapping as ORM;
use App\Entity\Traits\TimestampableTrait;
use App\Entity\Traits\ValueableTrait;

#[ORM\Table(name: "user_metas")]
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: UserMetaRepository::class)]
class UserMeta
{
  use IDableTrait, TimestampableTrait, DescribableTrait, ValueableTrait;

  #[ORM\ManyToOne(inversedBy: 'metas')]
  #[ORM\JoinColumn(nullable: false)]
  private ?User $user = null;

  #[ORM\Column(length: 200)]
  private ?string $code = null;

  public function getCode(): ?string
  {
    return $this->code;
  }

  public function setCode(string $code): static
  {
    $this->code = $code;

    return $this;
  }

  public function getUser(): ?User
  {
    return $this->user;
  }

  public function setUser(?User $user): static
  {
    $this->user = $user;

    return $this;
  }
}

<?php

namespace App\Entity\Player;

use App\Entity\Team\Team;
use App\Entity\Security\User;
use Doctrine\DBAL\Types\Types;
use App\Entity\Company\Company;
use App\Entity\Payment\License;
use Doctrine\ORM\Mapping as ORM;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\UuidableTrait;
use App\Entity\Traits\DeActivatableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Repository\Player\PlayerRepository;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\ArrayCollection;

#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: "players")]
#[ORM\Entity(repositoryClass: PlayerRepository::class)]
class Player
{
    use IDableTrait;
    use UuidableTrait;
    use TimestampableTrait;
    use DeActivatableTrait;

    // Add player personal information fields
    #[ORM\Column(length: 255, nullable: true)]
    private ?string $firstName = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $lastName = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $category = null;

    #[ORM\Column(type: Types::DATE_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $birthDate = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $photo = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $idcardRecto = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $idcardVerso = null;

    #[ORM\Column(length: 255)]
    private ?string $position = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $jerseyNumber = null;

    #[ORM\ManyToOne(inversedBy: 'players')]
    #[ORM\JoinColumn(nullable: false)]
    private ?Team $team = null;

    // Remove the direct Company relationship
    // private ?Company $team = null;

    #[ORM\OneToMany(mappedBy: 'player', targetEntity: PlayerStatistic::class, orphanRemoval: true)]
    private Collection $statistics;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $totalGoals = 0;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $totalYellowCards = 0;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $totalRedCards = 0;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $totalAssists = 0;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $totalMatches = 0;

    #[ORM\Column(length: 255)]
    private string $status = 'IRREGULAR';

    #[ORM\OneToMany(mappedBy: 'player', targetEntity: License::class)]
    private Collection $licenses;


    public function __construct()
    {
        $this->statistics = new ArrayCollection();
        $this->licenses = new ArrayCollection();
    }

    public function getStatus(): string
    {
        return $this->status;
    }

    public function setStatus(string $status): static
    {
        $this->status = $status;
        return $this;
    }


    public function getActiveLicense(): ?License
    {
        foreach ($this->licenses as $license) {
            if ($license->isActive()) {
                return $license;
            }
        }
        return null;
    }

    public function getPosition(): ?string
    {
        return $this->position;
    }

    public function setPosition(string $position): static
    {
        $this->position = $position;

        return $this;
    }

    public function getJerseyNumber(): ?int
    {
        return $this->jerseyNumber;
    }

    public function setJerseyNumber(?int $jerseyNumber): static
    {
        $this->jerseyNumber = $jerseyNumber;

        return $this;
    }

    public function getTeam(): ?Team
    {
        return $this->team;
    }

    public function setTeam(?Team $team): static
    {
        $this->team = $team;

        return $this;
    }


    /**
     * @return Collection<int, PlayerStatistic>
     */
    public function getStatistics(): Collection
    {
        return $this->statistics;
    }

    public function addStatistic(PlayerStatistic $statistic): static
    {
        if (!$this->statistics->contains($statistic)) {
            $this->statistics->add($statistic);
            $statistic->setPlayer($this);
        }

        return $this;
    }

    public function removeStatistic(PlayerStatistic $statistic): static
    {
        if ($this->statistics->removeElement($statistic)) {
            // set the owning side to null (unless already changed)
            if ($statistic->getPlayer() === $this) {
                $statistic->setPlayer(null);
            }
        }

        return $this;
    }

    public function getTotalGoals(): ?int
    {
        return $this->totalGoals;
    }

    public function setTotalGoals(?int $totalGoals): static
    {
        $this->totalGoals = $totalGoals;

        return $this;
    }

    public function getTotalYellowCards(): ?int
    {
        return $this->totalYellowCards;
    }

    public function setTotalYellowCards(?int $totalYellowCards): static
    {
        $this->totalYellowCards = $totalYellowCards;

        return $this;
    }

    public function getTotalRedCards(): ?int
    {
        return $this->totalRedCards;
    }

    public function setTotalRedCards(?int $totalRedCards): static
    {
        $this->totalRedCards = $totalRedCards;

        return $this;
    }

    public function getTotalAssists(): ?int
    {
        return $this->totalAssists;
    }

    public function setTotalAssists(?int $totalAssists): static
    {
        $this->totalAssists = $totalAssists;

        return $this;
    }

    public function getTotalMatches(): ?int
    {
        return $this->totalMatches;
    }

    public function setTotalMatches(?int $totalMatches): static
    {
        $this->totalMatches = $totalMatches;

        return $this;
    }

    public function incrementGoals(int $count = 1): static
    {
        $this->totalGoals = ($this->totalGoals ?? 0) + $count;
        return $this;
    }

    public function incrementYellowCards(int $count = 1): static
    {
        $this->totalYellowCards = ($this->totalYellowCards ?? 0) + $count;
        return $this;
    }

    public function incrementRedCards(int $count = 1): static
    {
        $this->totalRedCards = ($this->totalRedCards ?? 0) + $count;
        return $this;
    }

    public function incrementAssists(int $count = 1): static
    {
        $this->totalAssists = ($this->totalAssists ?? 0) + $count;
        return $this;
    }

    public function incrementMatches(int $count = 1): static
    {
        $this->totalMatches = ($this->totalMatches ?? 0) + $count;
        return $this;
    }

        // Add getters and setters for the new fields
        public function getFirstName(): ?string
        {
            return $this->firstName;
        }
    
        public function setFirstName(?string $firstName): static
        {
            $this->firstName = $firstName;
            return $this;
        }
    
        public function getLastName(): ?string
        {
            return $this->lastName;
        }
    
        public function setLastName(?string $lastName): static
        {
            $this->lastName = $lastName;
            return $this;
        }
    
        public function getCategory(): ?string
        {
            return $this->category;
        }
    
        public function setCategory(?string $category): static
        {
            $this->category = $category;
            return $this;
        }
    
        public function getBirthDate(): ?\DateTimeInterface
        {
            return $this->birthDate;
        }
    
        public function setBirthDate(?\DateTimeInterface $birthDate): static
        {
            $this->birthDate = $birthDate;
            return $this;
        }
    
        public function getPhoto(): ?string
        {
            return $this->photo;
        }
    
        public function setPhoto(?string $photo): static
        {
            $this->photo = $photo;
            return $this;
        }
    
        public function getIdcardRecto(): ?string
        {
            return $this->idcardRecto;
        }
    
        public function setIdcardRecto(?string $idcardRecto): static
        {
            $this->idcardRecto = $idcardRecto;
            return $this;
        }
    
        public function getIdcardVerso(): ?string
        {
            return $this->idcardVerso;
        }
    
        public function setIdcardVerso(?string $idcardVerso): static
        {
            $this->idcardVerso = $idcardVerso;
            return $this;
        }

        /**
         * @return Collection<int, License>
         */
        public function getLicenses(): Collection
        {
            return $this->licenses;
        }

        public function addLicense(License $license): static
        {
            if (!$this->licenses->contains($license)) {
                $this->licenses->add($license);
                $license->setPlayer($this);
            }

            return $this;
        }

        public function removeLicense(License $license): static
        {
            if ($this->licenses->removeElement($license)) {
                // set the owning side to null (unless already changed)
                if ($license->getPlayer() === $this) {
                    $license->setPlayer(null);
                }
            }

            return $this;
        }
}
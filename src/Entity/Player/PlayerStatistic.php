<?php

namespace App\Entity\Player;

use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Entity\Traits\UuidableTrait;
use App\Repository\Player\PlayerStatisticRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: "player_statistics")]
#[ORM\Entity(repositoryClass: PlayerStatisticRepository::class)]
class PlayerStatistic
{
    use IDableTrait;
    use UuidableTrait;
    use TimestampableTrait;

    #[ORM\ManyToOne(inversedBy: 'statistics')]
    #[ORM\JoinColumn(nullable: false)]
    private ?Player $player = null;

    #[ORM\Column(length: 255)]
    private ?string $matchId = null;

    #[ORM\Column(length: 255)]
    private ?string $matchName = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $matchDate = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $goals = 0;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $yellowCards = 0;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $redCards = 0;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $assists = 0;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $notes = null;

    public function getPlayer(): ?Player
    {
        return $this->player;
    }

    public function setPlayer(?Player $player): static
    {
        $this->player = $player;

        return $this;
    }

    public function getMatchId(): ?string
    {
        return $this->matchId;
    }

    public function setMatchId(string $matchId): static
    {
        $this->matchId = $matchId;

        return $this;
    }

    public function getMatchName(): ?string
    {
        return $this->matchName;
    }

    public function setMatchName(string $matchName): static
    {
        $this->matchName = $matchName;

        return $this;
    }

    public function getMatchDate(): ?\DateTimeInterface
    {
        return $this->matchDate;
    }

    public function setMatchDate(\DateTimeInterface $matchDate): static
    {
        $this->matchDate = $matchDate;

        return $this;
    }

    public function getGoals(): ?int
    {
        return $this->goals;
    }

    public function setGoals(?int $goals): static
    {
        $this->goals = $goals;

        return $this;
    }

    public function getYellowCards(): ?int
    {
        return $this->yellowCards;
    }

    public function setYellowCards(?int $yellowCards): static
    {
        $this->yellowCards = $yellowCards;

        return $this;
    }

    public function getRedCards(): ?int
    {
        return $this->redCards;
    }

    public function setRedCards(?int $redCards): static
    {
        $this->redCards = $redCards;

        return $this;
    }

    public function getAssists(): ?int
    {
        return $this->assists;
    }

    public function setAssists(?int $assists): static
    {
        $this->assists = $assists;

        return $this;
    }

    public function getNotes(): ?string
    {
        return $this->notes;
    }

    public function setNotes(?string $notes): static
    {
        $this->notes = $notes;

        return $this;
    }
}
<?php

namespace App\Entity\Payment;

use App\Entity\Security\User;
use Doctrine\ORM\Mapping as ORM;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\DescribableTrait;
use App\Entity\Traits\IndexableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Repository\Payment\PaymentRepository;

#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: "payments")]
#[ORM\Entity(repositoryClass: PaymentRepository::class)]
class Payment
{
  use IDableTrait, DescribableTrait, TimestampableTrait, IndexableTrait;
    
    #[ORM\Column(length: 255, nullable: true)]
    private ?string $payUrl = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $currencyCode = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $idActivity = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $activityName = null;

    #[ORM\Column(length: 255)]
    private ?string $amount = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $spentFidelityAmount = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $offerTo = null;

    #[ORM\ManyToOne(inversedBy: 'payments')]
    private ?User $user = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $idCompany = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $companyName = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $phoneNumber = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $clientName = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $mailAddress = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $orderId = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $activityDateTime = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $status = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $financialTransactionId = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $transactionId = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $paymentMethod = null;

    #[ORM\Column(length: 254, nullable: true)]
    private ?string $orangePayUrl = null;

    #[ORM\Column(length: 254, nullable: true)]
    private ?string $payToken = null;

    #[ORM\Column(length: 254, nullable: true)]
    private ?string $notifToken = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $paymentType = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getPayUrl(): ?string
    {
        return $this->payUrl;
    }

    public function setPayUrl(?string $payUrl): static
    {
        $this->payUrl = $payUrl;

        return $this;
    }

    public function getCurrencyCode(): ?string
    {
        return $this->currencyCode;
    }

    public function setCurrencyCode(?string $currencyCode): static
    {
        $this->currencyCode = $currencyCode;

        return $this;
    }

    public function getIdActivity(): ?string
    {
        return $this->idActivity;
    }

    public function setIdActivity(?string $idActivity): static
    {
        $this->idActivity = $idActivity;

        return $this;
    }

    public function getActivityName(): ?string
    {
        return $this->activityName;
    }

    public function setActivityName(?string $activityName): static
    {
        $this->activityName = $activityName;

        return $this;
    }

    public function getAmount(): ?string
    {
        return $this->amount;
    }

    public function setAmount(string $amount): static
    {
        $this->amount = $amount;

        return $this;
    }

    public function getSpentFidelityAmount(): ?string
    {
        return $this->spentFidelityAmount;
    }

    public function setSpentFidelityAmount(?string $spentFidelityAmount): static
    {
        $this->spentFidelityAmount = $spentFidelityAmount;

        return $this;
    }

    public function getOfferTo(): ?string
    {
        return $this->offerTo;
    }

    public function setOfferTo(?string $offerTo): static
    {
        $this->offerTo = $offerTo;

        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): static
    {
        $this->user = $user;

        return $this;
    }

    public function getIdCompany(): ?string
    {
        return $this->idCompany;
    }

    public function setIdCompany(?string $idCompany): static
    {
        $this->idCompany = $idCompany;

        return $this;
    }

    public function getCompanyName(): ?string
    {
        return $this->companyName;
    }

    public function setCompanyName(?string $companyName): static
    {
        $this->companyName = $companyName;

        return $this;
    }

    public function getPhoneNumber(): ?string
    {
        return $this->phoneNumber;
    }

    public function setPhoneNumber(?string $phoneNumber): static
    {
        $this->phoneNumber = $phoneNumber;

        return $this;
    }

    public function getClientName(): ?string
    {
        return $this->clientName;
    }

    public function setClientName(?string $clientName): static
    {
        $this->clientName = $clientName;

        return $this;
    }

    public function getMailAddress(): ?string
    {
        return $this->mailAddress;
    }

    public function setMailAddress(?string $mailAddress): static
    {
        $this->mailAddress = $mailAddress;

        return $this;
    }

    public function getOrderId(): ?string
    {
        return $this->orderId;
    }

    public function setOrderId(?string $orderId): static
    {
        $this->orderId = $orderId;

        return $this;
    }

    public function getActivityDateTime(): ?string
    {
        return $this->activityDateTime;
    }

    public function setActivityDateTime(?string $activityDateTime): static
    {
        $this->activityDateTime = $activityDateTime;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(?string $status): static
    {
        $this->status = $status;

        return $this;
    }

    public function getFinancialTransactionId(): ?string
    {
        return $this->financialTransactionId;
    }

    public function setFinancialTransactionId(?string $financialTransactionId): static
    {
        $this->financialTransactionId = $financialTransactionId;

        return $this;
    }

    public function getTransactionId(): ?string
    {
        return $this->transactionId;
    }

    public function setTransactionId(?string $transactionId): static
    {
        $this->transactionId = $transactionId;

        return $this;
    }

    public function getPaymentMethod(): ?string
    {
        return $this->paymentMethod;
    }

    public function setPaymentMethod(?string $paymentMethod): static
    {
        $this->paymentMethod = $paymentMethod;

        return $this;
    }

    public function getOrangePayUrl(): ?string
    {
        return $this->orangePayUrl;
    }

    public function setOrangePayUrl(?string $orangePayUrl): static
    {
        $this->orangePayUrl = $orangePayUrl;

        return $this;
    }

    public function getPayToken(): ?string
    {
        return $this->payToken;
    }

    public function setPayToken(?string $payToken): static
    {
        $this->payToken = $payToken;

        return $this;
    }

    public function getNotifToken(): ?string
    {
        return $this->notifToken;
    }

    public function setNotifToken(?string $notifToken): static
    {
        $this->notifToken = $notifToken;

        return $this;
    }

    public function getPaymentType(): ?string
    {
        return $this->paymentType;
    }

    public function setPaymentType(string $paymentType): static
    {
        $this->paymentType = $paymentType;

        return $this;
    }
}

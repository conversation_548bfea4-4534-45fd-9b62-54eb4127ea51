<?php

namespace App\Entity\Shared;

use App\Entity\Security\User;
use App\Entity\Traits\CodeableTrait;
use App\Entity\Traits\DescribableTrait;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\IndexableTrait;
use App\Entity\Traits\ProgicielableTrait;
use App\Repository\Shared\SettingRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use App\Entity\Traits\TimestampableTrait;
use App\Entity\Traits\UuidableTrait;

#[ORM\Table(name: "settings")]
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: SettingRepository::class)]
class Setting
{
  use IDableTrait, CodeableTrait, TimestampableTrait, UuidableTrait, ProgicielableTrait, IndexableTrait, DescribableTrait;

  #[ORM\Column(type: Types::TEXT, nullable: true)]
  private ?string $value = null;

  #[ORM\OneToMany(mappedBy: 'setting', targetEntity: SettingVersion::class, orphanRemoval: true, cascade: ['persist', 'remove'])]
  private Collection $settingVersions;

  #[ORM\ManyToOne]
  #[ORM\JoinColumn(nullable: true)]
  private ?User $owner = null;

  public function __construct()
  {
    $this->settingVersions = new ArrayCollection();
  }

  #[ORM\PrePersist]
  public function setDefaultValues(): void
  {
    $this->rowIndex = $this->buildRowIndex();
  }

  public function getValue(): ?string
  {
    return $this->value;
  }

  public function setValue(?string $value): static
  {
    $this->value = $value;

    return $this;
  }

  /**
   * @return Collection<int, SettingVersion>
   */
  public function getSettingVersions(): Collection
  {
    return $this->settingVersions;
  }

  public function addSettingVersion(SettingVersion $settingVersion): static
  {
    if (!$this->settingVersions->contains($settingVersion)) {
      $this->settingVersions->add($settingVersion);
      $settingVersion->setSetting($this);
    }

    return $this;
  }

  public function removeSettingVersion(SettingVersion $settingVersion): static
  {
    if ($this->settingVersions->removeElement($settingVersion)) {
      // set the owning side to null (unless already changed)
      if ($settingVersion->getSetting() === $this) {
        $settingVersion->setSetting(null);
      }
    }

    return $this;
  }

  public function getOwner(): ?User
  {
    return $this->owner;
  }

  public function setOwner(?User $owner): static
  {
    $this->owner = $owner;

    return $this;
  }

  private function getIndexableFields(): array
  {
    return ["uuid", "code", "description", "value"];
  }
}

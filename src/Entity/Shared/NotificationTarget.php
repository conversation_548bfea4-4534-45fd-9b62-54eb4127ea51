<?php

namespace App\Entity\Shared;

use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Repository\Shared\NotificationTargetRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: "notification_targets")]
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: NotificationTargetRepository::class)]
class NotificationTarget
{
  use IDableTrait, TimestampableTrait;

  #[ORM\Column(length: 255)]
  private ?string $identifier = null;

  #[ORM\Column(length: 255)]
  private ?string $status = null;

  #[ORM\ManyToOne(inversedBy: 'targets')]
  #[ORM\JoinColumn(nullable: false)]
  private ?Notification $notification = null;

  #[ORM\OneToMany(mappedBy: 'target', targetEntity: NotificationTargetVariable::class, orphanRemoval: true)]
  private Collection $variables;

  public function __construct()
  {
    $this->variables = new ArrayCollection();
  }

  public function getIdentifier(): ?string
  {
    return $this->identifier;
  }

  public function setIdentifier(string $identifier): static
  {
    $this->identifier = $identifier;

    return $this;
  }

  public function getStatus(): ?string
  {
    return $this->status;
  }

  public function setStatus(string $status): static
  {
    $this->status = $status;

    return $this;
  }

  public function getNotification(): ?Notification
  {
    return $this->notification;
  }

  public function setNotification(?Notification $notification): static
  {
    $this->notification = $notification;

    return $this;
  }

  /**
   * @return Collection<int, NotificationTargetVariable>
   */
  public function getVariables(): Collection
  {
    return $this->variables;
  }

  public function addVariable(NotificationTargetVariable $variable): static
  {
    if (!$this->variables->contains($variable)) {
      $this->variables->add($variable);
      $variable->setTarget($this);
    }

    return $this;
  }

  public function removeVariable(NotificationTargetVariable $variable): static
  {
    if ($this->variables->removeElement($variable)) {
      // set the owning side to null (unless already changed)
      if ($variable->getTarget() === $this) {
        $variable->setTarget(null);
      }
    }

    return $this;
  }
}

<?php

namespace App\Entity\Shared;

use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Repository\Shared\NotificationTargetVariableRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: "notification_variables")]
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: NotificationTargetVariableRepository::class)]
class NotificationTargetVariable
{
  use IDableTrait, TimestampableTrait;

  #[ORM\Column(length: 255)]
  private ?string $name = null;

  #[ORM\Column(type: Types::TEXT, nullable: true)]
  private ?string $value = null;

  #[ORM\ManyToOne(inversedBy: 'variables')]
  #[ORM\JoinColumn(nullable: false)]
  private ?NotificationTarget $target = null;

  public function getName(): ?string
  {
    return $this->name;
  }

  public function setName(string $name): static
  {
    $this->name = $name;

    return $this;
  }

  public function getValue(): ?string
  {
    return $this->value;
  }

  public function setValue(?string $value): static
  {
    $this->value = $value;

    return $this;
  }

  public function getTarget(): ?NotificationTarget
  {
    return $this->target;
  }

  public function setTarget(?NotificationTarget $target): static
  {
    $this->target = $target;

    return $this;
  }
}

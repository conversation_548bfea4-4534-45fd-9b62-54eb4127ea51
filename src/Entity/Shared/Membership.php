<?php

namespace App\Entity\Shared;


use App\Entity\Traits\CodeableTrait;
use App\Entity\Traits\DescribableTrait;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Entity\Traits\UuidableTrait;
use App\Repository\Shared\MembershipRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: "memberships")]
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: MembershipRepository::class)]
class Membership
{
  use IDableTrait, UuidableTrait, CodeableTrait, DescribableTrait, TimestampableTrait;

  #[ORM\OneToMany(mappedBy: 'membership', targetEntity: MembershipPlan::class, orphanRemoval: true)]
  private Collection $plans;

  public function __construct()
  {
      $this->plans = new ArrayCollection();
  }

  /**
   * @return Collection<int, MembershipPlan>
   */
  public function getPlans(): Collection
  {
      return $this->plans;
  }

  public function addPlan(MembershipPlan $plan): static
  {
      if (!$this->plans->contains($plan)) {
          $this->plans->add($plan);
          $plan->setMembership($this);
      }

      return $this;
  }

  public function removePlan(MembershipPlan $plan): static
  {
      if ($this->plans->removeElement($plan)) {
          // set the owning side to null (unless already changed)
          if ($plan->getMembership() === $this) {
              $plan->setMembership(null);
          }
      }

      return $this;
  }
}

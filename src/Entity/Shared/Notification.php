<?php

namespace App\Entity\Shared;

use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Entity\Traits\UuidableTrait;
use App\Repository\Shared\NotificationRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: "notifications")]
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: NotificationRepository::class)]
class Notification
{
  use IDableTrait, UuidableTrait, TimestampableTrait;

  #[ORM\Column(type: Types::TEXT, nullable: true)]
  private ?string $content = null;

  #[ORM\Column(length: 255, nullable: true)]
  private ?string $templateId = null;

  #[ORM\Column(length: 255)]
  private ?string $status = null;

  #[ORM\ManyToOne(inversedBy: 'notifications')]
  private ?EventSubscription $subscription = null;

  #[ORM\ManyToOne]
  #[ORM\JoinColumn(nullable: false)]
  private ?NotificationChannel $channel = null;

  #[ORM\OneToMany(mappedBy: 'notification', targetEntity: NotificationTarget::class, orphanRemoval: true)]
  private Collection $targets;

  public function __construct()
  {
    $this->targets = new ArrayCollection();
  }

  public function getContent(): ?string
  {
    return $this->content;
  }

  public function setContent(string $content): static
  {
    $this->content = $content;

    return $this;
  }

  public function getTemplateId(): ?string
  {
    return $this->templateId;
  }

  public function setTemplateId(?string $templateId): static
  {
    $this->templateId = $templateId;

    return $this;
  }

  public function getStatus(): ?string
  {
    return $this->status;
  }

  public function setStatus(string $status): static
  {
    $this->status = $status;

    return $this;
  }

  public function getSubscription(): ?EventSubscription
  {
    return $this->subscription;
  }

  public function setSubscription(?EventSubscription $subscription): static
  {
    $this->subscription = $subscription;

    return $this;
  }

  public function getChannel(): ?NotificationChannel
  {
    return $this->channel;
  }

  public function setChannel(?NotificationChannel $channel): static
  {
    $this->channel = $channel;

    return $this;
  }


  /**
   * @return Collection<int, NotificationTarget>
   */
  public function getNotifications(): Collection
  {
    return $this->targets;
  }

  public function addNotification(NotificationTarget $target): static
  {
    if (!$this->targets->contains($target)) {
      $this->targets->add($target);
      $target->setNotification($this);
    }

    return $this;
  }

  public function removeNotification(NotificationTarget $target): static
  {
    if ($this->targets->removeElement($target)) {
      // set the owning side to null (unless already changed)
      if ($target->getNotification() === $this) {
        $target->setNotification(null);
      }
    }

    return $this;
  }
}

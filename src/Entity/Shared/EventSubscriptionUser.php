<?php

namespace App\Entity\Shared;

use App\Entity\Security\User;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Repository\Shared\EventSubscriptionUserRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: "event_subscription_users")]
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: EventSubscriptionUserRepository::class)]
class EventSubscriptionUser
{
  use IDableTrait, TimestampableTrait;

  #[ORM\Column(length: 255)]
  private ?string $status = null;

  #[ORM\ManyToOne]
  #[ORM\JoinColumn(nullable: false)]
  private ?User $user = null;

  #[ORM\ManyToOne(inversedBy: 'users')]
  #[ORM\JoinColumn(nullable: false)]
  private ?EventSubscription $subscription = null;

  public function getStatus(): ?string
  {
    return $this->status;
  }

  public function setStatus(string $status): static
  {
    $this->status = $status;

    return $this;
  }

  public function getUser(): ?User
  {
    return $this->user;
  }

  public function setUser(?User $user): static
  {
    $this->user = $user;

    return $this;
  }

  public function getSubscription(): ?EventSubscription
  {
    return $this->subscription;
  }

  public function setSubscription(?EventSubscription $subscription): static
  {
    $this->subscription = $subscription;

    return $this;
  }
}

<?php

namespace App\Entity\Shared;

use App\Entity\Security\User;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\IndexableTrait;
use App\Entity\Traits\ProgicielableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Entity\Traits\UuidableTrait;
use App\Repository\Shared\FileRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: "files")]
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: FileRepository::class)]
class File
{
  use IDableTrait, UuidableTrait, ProgicielableTrait, TimestampableTrait, IndexableTrait;

  #[ORM\Column(length: 255)]
  private ?string $fileName = null;

  #[ORM\Column(length: 255)]
  private ?string $uploadDirectory = null;

  #[ORM\Column(length: 255)]
  private ?string $type = null;

  #[ORM\Column]
  private ?int $size = null;

  #[ORM\Column(length: 255)]
  private ?string $visibility = null;

  #[ORM\ManyToOne(inversedBy: 'ownerFiles')]
  private ?User $owner = null;

  #[ORM\ManyToOne(inversedBy: 'editorFiles')]
  private ?User $editor = null;

  public function getFileName(): ?string
  {
    return $this->fileName;
  }

  public function setFileName(string $fileName): static
  {
    $this->fileName = $fileName;

    return $this;
  }

  public function getUploadDirectory(): ?string
  {
    return $this->uploadDirectory;
  }

  public function setUploadDirectory(string $uploadDirectory): static
  {
    $this->uploadDirectory = $uploadDirectory;

    return $this;
  }

  public function getType(): ?string
  {
    return $this->type;
  }

  public function setType(string $type): static
  {
    $this->type = $type;

    return $this;
  }

  public function getSize(): ?int
  {
    return $this->size;
  }

  public function setSize(int $size): static
  {
    $this->size = $size;

    return $this;
  }

  public function getVisibility(): ?string
  {
    return $this->visibility;
  }

  public function setVisibility(string $visibility): static
  {
    $this->visibility = $visibility;

    return $this;
  }

  public function getOwner(): ?User
  {
    return $this->owner;
  }

  public function setOwner(?User $owner): static
  {
    $this->owner = $owner;

    return $this;
  }

  public function getEditor(): ?User
  {
    return $this->editor;
  }

  public function setEditor(?User $editor): static
  {
    $this->editor = $editor;

    return $this;
  }
}

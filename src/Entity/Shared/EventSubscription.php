<?php

namespace App\Entity\Shared;

use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Entity\Traits\UuidableTrait;
use App\Repository\Shared\EventSubscriptionRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: "event_subscriptions")]
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: EventSubscriptionRepository::class)]
class EventSubscription
{
  use IDableTrait, UuidableTrait, TimestampableTrait;

  #[ORM\OneToMany(mappedBy: 'subscription', targetEntity: EventSubscriptionUser::class, orphanRemoval: true)]
  private Collection $subscribers;

  #[ORM\OneToMany(mappedBy: 'subscription', targetEntity: EventSubscriptionChannel::class, orphanRemoval: true)]
  private Collection $channels;

  #[ORM\OneToMany(mappedBy: 'subscription', targetEntity: Notification::class)]
  private Collection $notifications;

  public function __construct()
  {
    $this->subscribers = new ArrayCollection();
    $this->channels = new ArrayCollection();
    $this->notifications = new ArrayCollection();
  }

  /**
   * @return Collection<int, EventSubscriptionUser>
   */
  public function getSubscribers(): Collection
  {
    return $this->subscribers;
  }

  public function addSubscriber(EventSubscriptionUser $user): static
  {
    if (!$this->subscribers->contains($user)) {
      $this->subscribers->add($user);
      $user->setSubscription($this);
    }

    return $this;
  }

  public function removeSubscriber(EventSubscriptionUser $user): static
  {
    if ($this->subscribers->removeElement($user)) {
      // set the owning side to null (unless already changed)
      if ($user->getSubscription() === $this) {
        $user->setSubscription(null);
      }
    }

    return $this;
  }

  /**
   * @return Collection<int, EventSubscriptionChannel>
   */
  public function getChannels(): Collection
  {
    return $this->channels;
  }

  public function addChannel(EventSubscriptionChannel $channel): static
  {
    if (!$this->channels->contains($channel)) {
      $this->channels->add($channel);
      $channel->setSubscription($this);
    }

    return $this;
  }

  public function removeChannel(EventSubscriptionChannel $channel): static
  {
    if ($this->channels->removeElement($channel)) {
      // set the owning side to null (unless already changed)
      if ($channel->getSubscription() === $this) {
        $channel->setSubscription(null);
      }
    }

    return $this;
  }

  /**
   * @return Collection<int, Notification>
   */
  public function getNotifications(): Collection
  {
    return $this->notifications;
  }

  public function addNotification(Notification $notification): static
  {
    if (!$this->notifications->contains($notification)) {
      $this->notifications->add($notification);
      $notification->setSubscription($this);
    }

    return $this;
  }

  public function removeNotification(Notification $notification): static
  {
    if ($this->notifications->removeElement($notification)) {
      // set the owning side to null (unless already changed)
      if ($notification->getSubscription() === $this) {
        $notification->setSubscription(null);
      }
    }

    return $this;
  }
}

<?php

namespace App\Entity\Shared;

use App\Entity\Traits\DescribableTrait;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Entity\Traits\UuidableTrait;
use App\Repository\Shared\MembershipPlanRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: "membership_plans")]
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: MembershipPlanRepository::class)]
class MembershipPlan
{
  use IDableTrait, UuidableTrait, DescribableTrait, TimestampableTrait;

  #[ORM\ManyToOne(inversedBy: 'plans')]
  #[ORM\JoinColumn(nullable: false)]
  private ?Membership $membership = null;

  #[ORM\Column(length: 255)]
  private ?string $label = null;

  #[ORM\Column]
  private ?int $duration = null;

  #[ORM\Column(length: 255)]
  private ?string $durationType = null;

  public function getMembership(): ?Membership
  {
    return $this->membership;
  }

  public function setMembership(?Membership $membership): static
  {
    $this->membership = $membership;

    return $this;
  }

  public function getLabel(): ?string
  {
    return $this->label;
  }

  public function setLabel(string $label): static
  {
    $this->label = $label;

    return $this;
  }

  public function getDuration(): ?int
  {
    return $this->duration;
  }

  public function setDuration(int $duration): static
  {
    $this->duration = $duration;

    return $this;
  }

  public function getDurationType(): ?string
  {
    return $this->durationType;
  }

  public function setDurationType(string $durationType): static
  {
    $this->durationType = $durationType;

    return $this;
  }
}

<?php

namespace App\Entity\Shared;

use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Repository\Shared\EventSubscriptionChannelRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: "event_subscription_channels")]
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: EventSubscriptionChannelRepository::class)]
class EventSubscriptionChannel
{
  use IDableTrait, TimestampableTrait;

  #[ORM\Column(type: Types::TEXT, nullable: true)]
  private ?string $messageTemplate = null;

  #[ORM\Column(length: 255, nullable: true)]
  private ?string $messageTemplateId = null;

  #[ORM\ManyToOne]
  #[ORM\JoinColumn(nullable: false)]
  private ?NotificationChannel $channel = null;

  #[ORM\ManyToOne(inversedBy: 'channels')]
  #[ORM\JoinColumn(nullable: false)]
  private ?EventSubscription $subscription = null;

  public function getMessageTemplate(): ?string
  {
    return $this->messageTemplate;
  }

  public function setMessageTemplate(?string $messageTemplate): static
  {
    $this->messageTemplate = $messageTemplate;

    return $this;
  }

  public function getMessageTemplateId(): ?string
  {
    return $this->messageTemplateId;
  }

  public function setMessageTemplateId(?string $messageTemplateId): static
  {
    $this->messageTemplateId = $messageTemplateId;

    return $this;
  }

  public function getChannel(): ?NotificationChannel
  {
      return $this->channel;
  }

  public function setChannel(?NotificationChannel $channel): static
  {
      $this->channel = $channel;

      return $this;
  }

  public function getSubscription(): ?EventSubscription
  {
      return $this->subscription;
  }

  public function setSubscription(?EventSubscription $subscription): static
  {
      $this->subscription = $subscription;

      return $this;
  }
}

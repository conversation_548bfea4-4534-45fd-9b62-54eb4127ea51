<?php

namespace App\Entity\Shared;

use App\Entity\Security\User;
use App\Entity\Traits\IDableTrait;
use App\Repository\Shared\SettingVersionRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use App\Entity\Traits\TimestampableTrait;

#[ORM\Table(name: "setting_versions")]
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: SettingVersionRepository::class)]
class SettingVersion
{
  use IDableTrait, TimestampableTrait;

  #[ORM\Column(type: Types::TEXT, nullable: true)]
  private ?string $oldValue = null;

  #[ORM\Column(type: Types::TEXT, nullable: true)]
  private ?string $newValue = null;

  #[ORM\ManyToOne(inversedBy: 'settingVersions')]
  #[ORM\JoinColumn(nullable: false)]
  private ?Setting $setting = null;

  #[ORM\ManyToOne]
  #[ORM\JoinColumn(nullable: false)]
  private ?User $editor = null;

  public function getOldValue(): ?string
  {
    return $this->oldValue;
  }

  public function setOldValue(?string $oldValue): static
  {
    $this->oldValue = $oldValue;

    return $this;
  }

  public function getNewValue(): ?string
  {
    return $this->newValue;
  }

  public function setNewValue(?string $newValue): static
  {
    $this->newValue = $newValue;

    return $this;
  }

  public function getSetting(): ?Setting
  {
    return $this->setting;
  }

  public function setSetting(?Setting $setting): static
  {
    $this->setting = $setting;

    return $this;
  }

  public function getEditor(): ?User
  {
    return $this->editor;
  }

  public function setEditor(?User $editor): static
  {
    $this->editor = $editor;

    return $this;
  }
}

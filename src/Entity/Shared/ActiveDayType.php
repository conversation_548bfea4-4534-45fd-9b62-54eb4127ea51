<?php

namespace App\Entity\Shared;

use App\Entity\Traits\CodeableTrait;
use App\Entity\Traits\DescribableTrait;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Entity\Traits\UuidableTrait;
use App\Repository\Shared\ActiveDayTypeRepository;
use Doctrine\ORM\Mapping as ORM;


#[ORM\Table(name: "active_day_types")]
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: ActiveDayTypeRepository::class)]
class ActiveDayType
{
  use IDableTrait, UuidableTrait, CodeableTrait, DescribableTrait, TimestampableTrait;
}

<?php

namespace App\Entity\Shared;

use App\Entity\Traits\CodeableTrait;
use App\Entity\Traits\DescribableTrait;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Entity\Traits\UuidableTrait;
use App\Repository\Shared\EventTypeRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: "event_types")]
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: EventTypeRepository::class)]
class EventType
{
  use IDableTrait, UuidableTrait, CodeableTrait, DescribableTrait, TimestampableTrait;
}

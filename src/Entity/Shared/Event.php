<?php

namespace App\Entity\Shared;

use App\Entity\Traits\DescribableTrait;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Entity\Traits\UuidableTrait;
use App\Repository\Shared\EventRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: "events")]
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: EventRepository::class)]
class Event
{
  use IDableTrait, UuidableTrait, DescribableTrait, TimestampableTrait;

  #[ORM\Column(length: 255)]
  private ?string $message = null;

  #[ORM\ManyToOne]
  #[ORM\JoinColumn(nullable: false)]
  private ?EventType $eventType = null;


  public function getMessage(): ?string
  {
    return $this->message;
  }

  public function setMessage(string $message): static
  {
    $this->message = $message;

    return $this;
  }

  public function getEventType(): ?EventType
  {
    return $this->eventType;
  }

  public function setEventType(?EventType $eventType): static
  {
    $this->eventType = $eventType;

    return $this;
  }
}

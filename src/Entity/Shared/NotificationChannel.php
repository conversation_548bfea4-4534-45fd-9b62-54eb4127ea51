<?php

namespace App\Entity\Shared;

use App\Entity\Traits\CodeableTrait;
use App\Entity\Traits\DescribableTrait;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Entity\Traits\UuidableTrait;
use App\Repository\Shared\NotificationChannelRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: "notification_channels")]
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: NotificationChannelRepository::class)]
class NotificationChannel
{
  use IDableTrait, UuidableTrait, CodeableTrait, DescribableTrait, TimestampableTrait;
}

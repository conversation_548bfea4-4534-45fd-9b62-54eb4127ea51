<?php

namespace App\Entity\Shared;

use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Entity\Traits\UuidableTrait;
use App\Repository\Shared\FileSizeRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: "file_sizes")]
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: FileSizeRepository::class)]
class FileSize
{
  use IDableTrait, UuidableTrait, TimestampableTrait;

  #[ORM\Column(length: 255)]
  private ?string $fileName = null;

  #[ORM\Column(length: 255)]
  private ?string $uploadDirectory = null;

  #[ORM\Column(length: 255)]
  private ?string $dimension = null;

  #[ORM\Column]
  private ?int $size = null;

  #[ORM\Column(length: 255)]
  private ?string $fileUsage = null;

  public function getFileName(): ?string
  {
    return $this->fileName;
  }

  public function setFileName(string $fileName): static
  {
    $this->fileName = $fileName;

    return $this;
  }

  public function getUploadDirectory(): ?string
  {
    return $this->uploadDirectory;
  }

  public function setUploadDirectory(string $uploadDirectory): static
  {
    $this->uploadDirectory = $uploadDirectory;

    return $this;
  }

  public function getDimension(): ?string
  {
    return $this->dimension;
  }

  public function setDimension(string $dimension): static
  {
    $this->dimension = $dimension;

    return $this;
  }

  public function getSize(): ?int
  {
    return $this->size;
  }

  public function setSize(int $size): static
  {
    $this->size = $size;

    return $this;
  }

  public function getFileUsage(): ?string
  {
    return $this->fileUsage;
  }

  public function setFileUsage(string $fileUsage): static
  {
    $this->fileUsage = $fileUsage;

    return $this;
  }
}

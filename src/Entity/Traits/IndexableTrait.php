<?php

namespace App\Entity\Traits;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

trait IndexableTrait
{

  #[ORM\Column(type: Types::TEXT, nullable: true)]
  private ?string $rowIndex = null;

  public function getRowIndex(): ?string
  {
    return $this->rowIndex;
  }

  public function setRowIndex(?string $rowIndex): static
  {
    $this->rowIndex = $rowIndex;

    return $this;
  }

  public function buildRowIndex()
  {
    $index = '';
    if (!\method_exists($this, "getIndexableFields")) return $index;

    $indexableFields = $this->getIndexableFields();
    if (empty($indexableFields)) return $index;

    $reflectedClass = new \ReflectionClass($this);
    foreach ($indexableFields as $field) {
      if (!$reflectedClass->hasProperty($field)) continue;
      $prop = $reflectedClass->getProperty($field);
      $value = $prop->getValue($this);
      if (empty($value)) continue;
      $index .= '|' . strtolower($value);
    }
    if (method_exists($this, "buildIndexCallBack")) {
      return $this->buildIndexCallBack($index);
    }
    return $index;
  }
}

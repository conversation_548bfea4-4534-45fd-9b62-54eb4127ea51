<?php

namespace App\Entity\Company;

use App\Entity\Traits\DescribableTrait;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Entity\Traits\ValueableTrait;
use App\Repository\Company\CompanyMetaRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: "company_metas")]
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: CompanyMetaRepository::class)]
class CompanyMeta
{
  use IDableTrait, ValueableTrait, DescribableTrait, TimestampableTrait;

  #[ORM\ManyToOne(inversedBy: 'metas')]
  #[ORM\JoinColumn(nullable: false)]
  private ?Company $company = null;

  #[ORM\Column(length: 200)]
  private ?string $code = null;

  public function getCode(): ?string
  {
    return $this->code;
  }

  public function setCode(string $code): static
  {
    $this->code = $code;

    return $this;
  }

  public function getCompany(): ?Company
  {
    return $this->company;
  }

  public function setCompany(?Company $company): static
  {
    $this->company = $company;

    return $this;
  }
}

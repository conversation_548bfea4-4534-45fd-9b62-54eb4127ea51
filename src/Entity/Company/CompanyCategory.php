<?php

namespace App\Entity\Company;

use App\Entity\Traits\CodeableTrait;
use App\Entity\Traits\DescribableTrait;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\IndexableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Entity\Traits\UuidableTrait;
use App\Repository\Company\CompanyCategoryRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: "company_categories")]
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: CompanyCategoryRepository::class)]
class CompanyCategory
{
  use IDableTrait, UuidableTrait, CodeableTrait, DescribableTrait, TimestampableTrait, IndexableTrait;

  #[ORM\PrePersist]
  #[ORM\PreUpdate]
  public function setDefaultValues(): void
  {
    $this->rowIndex = $this->buildRowIndex();
  }

  private function getIndexableFields(): array
  {
    return ["uuid", "code", "description"];
  }
}

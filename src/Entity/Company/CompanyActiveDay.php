<?php

namespace App\Entity\Company;

use App\Entity\Shared\ActiveDayType;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Repository\Company\CompanyActiveDayRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: "company_active_days")]
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: CompanyActiveDayRepository::class)]
class CompanyActiveDay
{

  use IDableTrait, TimestampableTrait;

  #[ORM\Column(type: Types::TIME_MUTABLE)]
  private ?\DateTimeInterface $startTime = null;

  #[ORM\Column(type: Types::TIME_MUTABLE)]
  private ?\DateTimeInterface $endTime = null;

  #[ORM\Column]
  private ?bool $active = null;

  #[ORM\ManyToOne]
  #[ORM\JoinColumn(nullable: false)]
  private ?ActiveDayType $activeDayType = null;

  #[ORM\ManyToOne(inversedBy: 'activeDays')]
  #[ORM\JoinColumn(nullable: false)]
  private ?Company $company = null;

  public function getStartTime(): ?\DateTimeInterface
  {
    return $this->startTime;
  }

  public function setStartTime(\DateTimeInterface $startTime): static
  {
    $this->startTime = $startTime;

    return $this;
  }

  public function getEndTime(): ?\DateTimeInterface
  {
    return $this->endTime;
  }

  public function setEndTime(\DateTimeInterface $endTime): static
  {
    $this->endTime = $endTime;

    return $this;
  }

  public function isActive(): ?bool
  {
    return $this->active;
  }

  public function setActive(bool $active): static
  {
    $this->active = $active;

    return $this;
  }

  public function getActiveDayType(): ?ActiveDayType
  {
    return $this->activeDayType;
  }

  public function setActiveDayType(?ActiveDayType $activeDayType): static
  {
    $this->activeDayType = $activeDayType;

    return $this;
  }

  public function getCompany(): ?Company
  {
    return $this->company;
  }

  public function setCompany(?Company $company): static
  {
    $this->company = $company;

    return $this;
  }
}

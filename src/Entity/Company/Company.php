<?php

namespace App\Entity\Company;

use App\Entity\Security\User;
use App\Entity\Traits\DeActivatableTrait;
use App\Entity\Traits\DescribableTrait;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\IndexableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Entity\Traits\UuidableTrait;
use App\Repository\Company\CompanyRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: "companies")]
#[ORM\Entity(repositoryClass: CompanyRepository::class)]
class Company
{
  use IDableTrait, UuidableTrait, DescribableTrait, DeActivatableTrait, TimestampableTrait, IndexableTrait;

  #[ORM\Column(length: 200, unique: true)]
  private ?string $name = null;

  #[ORM\OneToMany(mappedBy: 'company', targetEntity: User::class)]
  private Collection $users;

  #[ORM\OneToMany(mappedBy: 'company', targetEntity: CompanyMeta::class, orphanRemoval: true, cascade: ['persist', 'remove'])]
  private Collection $metas;

  #[ORM\ManyToOne]
  #[ORM\JoinColumn(nullable: true)]
  private ?CompanyType $type = null;

  #[ORM\ManyToOne]
  #[ORM\JoinColumn(nullable: true)]
  private ?CompanyCategory $category = null;

  #[ORM\OneToMany(mappedBy: 'company', targetEntity: CompanyMembership::class, orphanRemoval: true)]
  private Collection $memberships;

  #[ORM\OneToMany(mappedBy: 'company', targetEntity: CompanyActiveDay::class, orphanRemoval: true)]
  private Collection $activeDays;

  #[ORM\OneToMany(mappedBy: 'company', targetEntity: Subscription::class)]
  private Collection $subscriptions;

  #[ORM\OneToMany(mappedBy: 'team', targetEntity: \App\Entity\Player\Player::class)]
  private Collection $players;

  // Make sure to initialize the players collection in the constructor
  public function __construct()
  {
      $this->users = new ArrayCollection();
      $this->metas = new ArrayCollection();
      $this->memberships = new ArrayCollection();
      $this->activeDays = new ArrayCollection();
      $this->subscriptions = new ArrayCollection();
      $this->players = new ArrayCollection();
  }

  #[ORM\PrePersist]
  #[ORM\PreUpdate]
  public function setDefaultValues(): void
  {
    $this->rowIndex = $this->buildRowIndex();
  }

  private function getIndexableFields(): array
  {
    return ["uuid", "name", "description"];
  }

  private function buildIndexCallBack(string $index): string
  {
    if (!empty($this->type)) {
      $index .= "|" . $this->type->getCode();
    }
    if (!empty($this->category)) {
      $index .= "|" . $this->category->getCode();
    }
    return $index;
  }

  public function getName(): ?string
  {
    return $this->name;
  }

  public function setName(string $name): static
  {
    $this->name = $name;

    return $this;
  }

  /**
   * @return Collection<int, User>
   */
  public function getUsers(): Collection
  {
    return $this->users;
  }

  public function addUser(User $user): static
  {
    if (!$this->users->contains($user)) {
      $this->users->add($user);
      $user->setCompany($this);
    }

    return $this;
  }

  public function removeUser(User $user): static
  {
    if ($this->users->removeElement($user)) {
      // set the owning side to null (unless already changed)
      if ($user->getCompany() === $this) {
        $user->setCompany(null);
      }
    }

    return $this;
  }

  /**
   * @return Collection<int, CompanyMeta>
   */
  public function getMetas(): Collection
  {
    return $this->metas;
  }

  public function addMeta(CompanyMeta $meta): static
  {
    $this->removeMetaIfExistsByCode($meta->getCode());
    if (!$this->metas->contains($meta)) {
      $this->metas->add($meta);
      $meta->setCompany($this);
    }

    return $this;
  }

  public function removeMeta(CompanyMeta $meta): static
  {
    if ($this->metas->removeElement($meta)) {
      // set the owning side to null (unless already changed)
      if ($meta->getCompany() === $this) {
        $meta->setCompany(null);
      }
    }

    return $this;
  }

  public function getType(): ?CompanyType
  {
    return $this->type;
  }

  public function setType(?CompanyType $type): static
  {
    $this->type = $type;

    return $this;
  }

  public function getCategory(): ?CompanyCategory
  {
    return $this->category;
  }

  public function setCategory(?CompanyCategory $category): static
  {
    $this->category = $category;

    return $this;
  }

  /**
   * @return Collection<int, CompanyMembership>
   */
  public function getMemberships(): Collection
  {
    return $this->memberships;
  }

  public function addMembership(CompanyMembership $membership): static
  {
    if (!$this->memberships->contains($membership)) {
      $this->memberships->add($membership);
      $membership->setCompany($this);
    }

    return $this;
  }

  public function removeMembership(CompanyMembership $membership): static
  {
    if ($this->memberships->removeElement($membership)) {
      // set the owning side to null (unless already changed)
      if ($membership->getCompany() === $this) {
        $membership->setCompany(null);
      }
    }

    return $this;
  }

  public function getMetaValueByCode(string $code, $defaultValue = '')
  {
    $first = $this->getMetas()->findFirst(function (int $key, CompanyMeta $value) use ($code) {
      return $value->getCode() === $code;
    });
    if (!empty($first)) {
      return $first->getValue();
    }
    return $defaultValue;
  }

  public function removeMetaIfExistsByCode(string $code)
  {
    $first = $this->getMetas()->findFirst(function (int $key, CompanyMeta $value) use ($code) {
      return $value->getCode() === $code;
    });
    if (!empty($first)) {
      $this->removeMeta($first);
    }
  }

  public function getMetaCodes()
  {
    $mappedCollection = $this->getMetas()->map(function ($meta) {
      return $meta->getCode();
    });
    return $mappedCollection->toArray();
  }

  public function hasMeta(string $code)
  {
    return in_array($code, $this->getMetaCodes());
  }

  /**
   * @return Collection<int, CompanyActiveDay>
   */
  public function getActiveDays(): Collection
  {
    return $this->activeDays;
  }

  public function addActiveDay(CompanyActiveDay $activeDay): static
  {
    if (!$this->activeDays->contains($activeDay)) {
      $this->activeDays->add($activeDay);
      $activeDay->setCompany($this);
    }

    return $this;
  }

  public function removeActiveDay(CompanyActiveDay $activeDay): static
  {
    if ($this->activeDays->removeElement($activeDay)) {
      // set the owning side to null (unless already changed)
      if ($activeDay->getCompany() === $this) {
        $activeDay->setCompany(null);
      }
    }

    return $this;
  }

  public function getActiveDaysArray()
  {
    $data = [];
    $days = $this->getMetaValueByCode('_openingHours');
    if (!empty($days)) {
      $parts = explode('|', $days);

      for ($i = 0; $i < count($parts); $i++) {

        $part = $parts[$i];
        if (empty($part)) {
          continue;
        }
        $parts2 = explode(',', $part);
        if (count($parts2) == 4) {
          $data[] = [
            'id' => $i + 1,
            'isPaused' => $parts2[3] == "1" || $parts2[3] == "true" ? true : false,
            'openingDays' => [
              'key' => $parts2[2],
              'code' => $parts2[2],
              'startTime' => $parts2[0],
              'endTime' => $parts2[1]
            ]
          ];
        }
      }
    }
    return $data;
    /*return $this->getActiveDays()->map(function ($day) {
      return [
        'id' => $day->getId(),
        'isPaused' => !$day->isActive(),
        'openingDays' => [
          'key' => $day->getActiveDayType()->getCode(),
          'code' => $day->getActiveDayType()->getCode(),
          'startTime' => $day->getStartTime()->format('H:i:sZ'),
          'endTime' => $day->getEndTime()->format('H:i:sZ')
        ]
      ];
    })->toArray();*/
  }

  /**
   * @return Collection<int, Subscription>
   */
  public function getSubscriptions(): Collection
  {
      return $this->subscriptions;
  }

  public function addSubscription(Subscription $subscription): static
  {
      if (!$this->subscriptions->contains($subscription)) {
          $this->subscriptions->add($subscription);
          $subscription->setCompany($this);
      }

      return $this;
  }

  public function removeSubscription(Subscription $subscription): static
  {
      if ($this->subscriptions->removeElement($subscription)) {
          // set the owning side to null (unless already changed)
          if ($subscription->getCompany() === $this) {
              $subscription->setCompany(null);
          }
      }

      return $this;
  }

  /**
   * @return Collection<int, \App\Entity\Player\Player>
   */
  public function getPlayers(): Collection
  {
      return $this->players;
  }

  public function addPlayer(\App\Entity\Player\Player $player): static
  {
      if (!$this->players->contains($player)) {
          $this->players->add($player);
          $player->setTeam($this);
      }

      return $this;
  }

  public function removePlayer(\App\Entity\Player\Player $player): static
  {
      if ($this->players->removeElement($player)) {
          // set the owning side to null (unless already changed)
          if ($player->getTeam() === $this) {
              $player->setTeam(null);
          }
      }

      return $this;
  }
}

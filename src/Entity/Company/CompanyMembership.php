<?php

namespace App\Entity\Company;

use App\Entity\Payment\Payment;
use App\Entity\Shared\Membership;
use App\Entity\Shared\MembershipPlan;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Repository\Company\CompanyMembershipRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Table(name: "company_memberships")]
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: CompanyMembershipRepository::class)]
class CompanyMembership
{
  use IDableTrait, TimestampableTrait;

  #[ORM\Column(length: 255)]
  private ?string $status = null;

  #[ORM\ManyToOne]
  #[ORM\JoinColumn(nullable: false)]
  private ?MembershipPlan $plan = null;

  #[ORM\ManyToOne]
  #[ORM\JoinColumn(nullable: false)]
  private ?Membership $membership = null;

  #[ORM\ManyToOne(inversedBy: 'memberships')]
  #[ORM\JoinColumn(nullable: false)]
  private ?Company $company = null;

  #[ORM\ManyToOne]
  private ?Payment $payment = null;

  #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
  private ?\DateTimeInterface $startValidationDate = null;

  #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
  private ?\DateTimeInterface $endValidationDate = null;

  public function getStatus(): ?string
  {
    return $this->status;
  }

  public function setStatus(string $status): static
  {
    $this->status = $status;

    return $this;
  }

  public function getPlan(): ?MembershipPlan
  {
    return $this->plan;
  }

  public function setPlan(?MembershipPlan $plan): static
  {
    $this->plan = $plan;

    return $this;
  }

  public function getMembership(): ?Membership
  {
    return $this->membership;
  }

  public function setMembership(?Membership $membership): static
  {
    $this->membership = $membership;

    return $this;
  }

  public function getCompany(): ?Company
  {
    return $this->company;
  }

  public function setCompany(?Company $company): static
  {
    $this->company = $company;

    return $this;
  }

  public function getPayment(): ?Payment
  {
      return $this->payment;
  }

  public function setPayment(?Payment $payment): static
  {
      $this->payment = $payment;

      return $this;
  }

  public function getStartValidationDate(): ?\DateTimeInterface
  {
      return $this->startValidationDate;
  }

  public function setStartValidationDate(?\DateTimeInterface $startValidationDate): static
  {
      $this->startValidationDate = $startValidationDate;

      return $this;
  }

  public function getEndValidationDate(): ?\DateTimeInterface
  {
      return $this->endValidationDate;
  }

  public function setEndValidationDate(?\DateTimeInterface $endValidationDate): static
  {
      $this->endValidationDate = $endValidationDate;

      return $this;
  }
}

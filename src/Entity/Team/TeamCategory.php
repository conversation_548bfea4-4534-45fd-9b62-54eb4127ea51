<?php

namespace App\Entity\Team;

use App\Entity\Traits\DeActivatableTrait;
use App\Entity\Traits\DescribableTrait;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Entity\Traits\UuidableTrait;
use App\Repository\Team\TeamCategoryRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: "team_categories")]
#[ORM\Entity(repositoryClass: TeamCategoryRepository::class)]
class TeamCategory
{
    use IDableTrait;
    use UuidableTrait;
    use TimestampableTrait;
    use DeActivatableTrait;
    use DescribableTrait;

    #[ORM\Column(length: 255, unique: true)]
    private ?string $name = null;

    #[ORM\Column(length: 100, unique: true)]
    private ?string $code = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $minAge = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $maxAge = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $maxPlayersPerTeam = null;

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $minPlayersPerTeam = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $gender = null; // 'MALE', 'FEMALE', 'MIXED'

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $rules = null;

    #[ORM\Column(type: Types::INTEGER)]
    private int $sortOrder = 0;

    #[ORM\OneToMany(mappedBy: 'category', targetEntity: Team::class)]
    private Collection $teams;

    public function __construct()
    {
        $this->teams = new ArrayCollection();
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;
        return $this;
    }

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function setCode(string $code): static
    {
        $this->code = strtoupper($code);
        return $this;
    }

    public function getMinAge(): ?int
    {
        return $this->minAge;
    }

    public function setMinAge(?int $minAge): static
    {
        $this->minAge = $minAge;
        return $this;
    }

    public function getMaxAge(): ?int
    {
        return $this->maxAge;
    }

    public function setMaxAge(?int $maxAge): static
    {
        $this->maxAge = $maxAge;
        return $this;
    }

    public function getMaxPlayersPerTeam(): ?int
    {
        return $this->maxPlayersPerTeam;
    }

    public function setMaxPlayersPerTeam(?int $maxPlayersPerTeam): static
    {
        $this->maxPlayersPerTeam = $maxPlayersPerTeam;
        return $this;
    }

    public function getMinPlayersPerTeam(): ?int
    {
        return $this->minPlayersPerTeam;
    }

    public function setMinPlayersPerTeam(?int $minPlayersPerTeam): static
    {
        $this->minPlayersPerTeam = $minPlayersPerTeam;
        return $this;
    }

    public function getGender(): ?string
    {
        return $this->gender;
    }

    public function setGender(?string $gender): static
    {
        $this->gender = $gender;
        return $this;
    }

    public function getRules(): ?string
    {
        return $this->rules;
    }

    public function setRules(?string $rules): static
    {
        $this->rules = $rules;
        return $this;
    }

    public function getSortOrder(): int
    {
        return $this->sortOrder;
    }

    public function setSortOrder(int $sortOrder): static
    {
        $this->sortOrder = $sortOrder;
        return $this;
    }

    /**
     * @return Collection<int, Team>
     */
    public function getTeams(): Collection
    {
        return $this->teams;
    }

    public function addTeam(Team $team): static
    {
        if (!$this->teams->contains($team)) {
            $this->teams->add($team);
            $team->setCategory($this);
        }
        return $this;
    }

    public function removeTeam(Team $team): static
    {
        if ($this->teams->removeElement($team)) {
            if ($team->getCategory() === $this) {
                $team->setCategory(null);
            }
        }
        return $this;
    }

    public function getAgeRange(): ?string
    {
        if ($this->minAge && $this->maxAge) {
            return $this->minAge . '-' . $this->maxAge . ' years';
        } elseif ($this->minAge) {
            return $this->minAge . '+ years';
        } elseif ($this->maxAge) {
            return 'Under ' . $this->maxAge . ' years';
        }
        return null;
    }

    public function getPlayerRange(): ?string
    {
        if ($this->minPlayersPerTeam && $this->maxPlayersPerTeam) {
            return $this->minPlayersPerTeam . '-' . $this->maxPlayersPerTeam . ' players';
        } elseif ($this->minPlayersPerTeam) {
            return 'Min ' . $this->minPlayersPerTeam . ' players';
        } elseif ($this->maxPlayersPerTeam) {
            return 'Max ' . $this->maxPlayersPerTeam . ' players';
        }
        return null;
    }

    public function __toString(): string
    {
        return $this->name ?? '';
    }
}

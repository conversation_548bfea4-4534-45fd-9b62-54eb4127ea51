<?php

namespace App\Entity\Activity;

use App\Entity\Shared\File;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Repository\Activity\ActivityFileRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: "activity_files")]
#[ORM\Entity(repositoryClass: ActivityFileRepository::class)]
class ActivityFile
{
  use IDableTrait, TimestampableTrait;

  #[ORM\ManyToOne]
  #[ORM\JoinColumn(nullable: false)]
  private ?File $file = null;

  #[ORM\ManyToOne]
  #[ORM\JoinColumn(nullable: false)]
  private ?Activity $activity = null;

  public function getFile(): ?File
  {
    return $this->file;
  }

  public function setFile(?File $file): static
  {
    $this->file = $file;

    return $this;
  }

  public function getActivity(): ?Activity
  {
    return $this->activity;
  }

  public function setActivity(?Activity $activity): static
  {
    $this->activity = $activity;

    return $this;
  }
}

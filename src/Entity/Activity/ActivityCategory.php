<?php

namespace App\Entity\Activity;

use App\Entity\Traits\CodeableTrait;
use App\Entity\Traits\DescribableTrait;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\IndexableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Entity\Traits\UuidableTrait;
use App\Repository\Activity\ActivityCategoryRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: "activity_categories")]
#[ORM\Entity(repositoryClass: ActivityCategoryRepository::class)]
class ActivityCategory
{
  use IDableTrait, UuidableTrait, CodeableTrait, DescribableTrait, TimestampableTrait, IndexableTrait;

  #[ORM\PrePersist]
  #[ORM\PreUpdate]
  public function setDefaultValues(): void
  {
    $this->rowIndex = $this->buildRowIndex();
  }

  private function getIndexableFields(): array
  {
    return ["uuid", "code", "description"];
  }
}

<?php

namespace App\Entity\Activity;

use App\Entity\Security\User;
use App\Entity\Traits\DeActivatableTrait;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Repository\Activity\ActivityPartnerRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: "activity_partners")]
#[ORM\Entity(repositoryClass: ActivityPartnerRepository::class)]
class ActivityPartner
{
  use IDableTrait, DeActivatableTrait, TimestampableTrait;

  #[ORM\ManyToOne]
  #[ORM\JoinColumn(nullable: false)]
  private ?User $company = null;

  #[ORM\ManyToOne(inversedBy: 'partners')]
  #[ORM\JoinColumn(nullable: false)]
  private ?Activity $activity = null;


  public function getCompany(): ?User
  {
    return $this->company;
  }

  public function setCompany(?User $company): static
  {
    $this->company = $company;

    return $this;
  }

  public function getActivity(): ?Activity
  {
      return $this->activity;
  }

  public function setActivity(?Activity $activity): static
  {
      $this->activity = $activity;

      return $this;
  }
}

<?php

namespace App\Entity\Activity;

use App\Entity\Traits\CodeableTrait;
use App\Entity\Traits\DescribableTrait;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\IndexableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Entity\Traits\UuidableTrait;
use App\Repository\Activity\ActivityActivationTimeTypeRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: "activity_activation_time_types")]
#[ORM\Entity(repositoryClass: ActivityActivationTimeTypeRepository::class)]
class ActivityActivationTimeType
{
  use IDableTrait, UuidableTrait, CodeableTrait, DescribableTrait, TimestampableTrait, IndexableTrait;
}

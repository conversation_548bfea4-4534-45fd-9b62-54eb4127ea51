<?php

namespace App\Entity\Activity;

use App\Entity\Traits\CodeableTrait;
use App\Entity\Traits\DeActivatableTrait;
use App\Entity\Traits\DescribableTrait;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Entity\Traits\UuidableTrait;
use App\Repository\Activity\ActivityMembershipPlanRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: "activity_membership_plans")]
#[ORM\Entity(repositoryClass: ActivityMembershipPlanRepository::class)]
class ActivityMembershipPlan
{
  use IDableTrait, UuidableTrait, CodeableTrait, DescribableTrait, DeActivatableTrait, TimestampableTrait;

  #[ORM\ManyToOne(inversedBy: 'plans')]
  #[ORM\JoinColumn(nullable: false)]
  private ?ActivityMembership $membership = null;

  #[ORM\Column]
  private ?int $duration = null;

  #[ORM\Column(length: 255)]
  private ?string $durationType = null;


  public function getMembership(): ?ActivityMembership
  {
    return $this->membership;
  }

  public function setMembership(?ActivityMembership $membership): static
  {
    $this->membership = $membership;

    return $this;
  }

  public function getDuration(): ?int
  {
    return $this->duration;
  }

  public function setDuration(int $duration): static
  {
    $this->duration = $duration;

    return $this;
  }

  public function getDurationType(): ?string
  {
    return $this->durationType;
  }

  public function setDurationType(string $durationType): static
  {
    $this->durationType = $durationType;

    return $this;
  }
}

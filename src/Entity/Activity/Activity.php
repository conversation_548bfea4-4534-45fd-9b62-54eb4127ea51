<?php

namespace App\Entity\Activity;

use App\Entity\Activity\ActivityPayment;
use App\Entity\Security\User;
use App\Entity\Traits\DeActivatableTrait;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\IndexableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Entity\Traits\UuidableTrait;
use App\Repository\Activity\ActivityRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: "activities")]
#[ORM\Entity(repositoryClass: ActivityRepository::class)]
class Activity
{
  use IDableTrait, DeActivatableTrait, UuidableTrait, TimestampableTrait, IndexableTrait;

  #[ORM\Column(length: 255)]
  private ?string $title = null;

  #[ORM\Column(length: 255, nullable: true)]
  private ?string $shortDescription = null;

  #[ORM\Column(type: Types::TEXT, nullable: true)]
  private ?string $longDescription = null;

  #[ORM\Column(length: 255, nullable: true)]
  private ?string $placeName = null;

  #[ORM\Column(length: 255, nullable: true)]
  private ?string $streetName = null;

  #[ORM\Column(length: 255, nullable: true)]
  private ?string $city = null;

  #[ORM\Column(nullable: true)]
  private ?int $zipCode = null;

  #[ORM\Column(type: Types::DECIMAL, precision: 10, scale: 7, nullable: true)]
  private ?string $longitude = null;

  #[ORM\Column(type: Types::DECIMAL, precision: 10, scale: 7, nullable: true)]
  private ?string $latitude = null;

  #[ORM\Column(length: 255, nullable: true)]
  private ?string $country = null;

  #[ORM\Column(type: Types::DECIMAL, precision: 10, scale: 2, nullable: true)]
  private ?string $price = null;

  #[ORM\ManyToOne]
  #[ORM\JoinColumn(nullable: false)]
  private ?User $owner = null;

  #[ORM\ManyToOne]
  #[ORM\JoinColumn(nullable: false)]
  private ?User $creator = null;

  #[ORM\Column(length: 255)]
  private ?string $status = null;

  #[ORM\OneToMany(mappedBy: 'activity', targetEntity: ActivityPartner::class, orphanRemoval: true)]
  private Collection $partners;

  #[ORM\OneToMany(mappedBy: 'activity', targetEntity: ActivityFile::class, orphanRemoval: true)]
  private Collection $files;

  #[ORM\OneToMany(mappedBy: 'activity', targetEntity: ActivityActivationTime::class, orphanRemoval: true)]
  private Collection $activationTimes;

  #[ORM\ManyToOne]
  #[ORM\JoinColumn(nullable: true)]
  private ?ActivityCategory $category = null;

  #[ORM\OneToMany(mappedBy: 'activity', targetEntity: ActivityMembership::class)]
  private Collection $memberships;

  #[ORM\OneToMany(mappedBy: 'activity', targetEntity: ActivityReservation::class)]
  private Collection $reservations;

  #[ORM\OneToMany(mappedBy: 'activity', targetEntity: ActivityMeta::class, orphanRemoval: true, cascade: ['persist', 'remove'])]
  private Collection $metas;

  #[ORM\OneToMany(mappedBy: 'activity', targetEntity: ActivityPayment::class)]
  private Collection $payments;

  public function __construct()
  {
    $this->files = new ArrayCollection();
    $this->partners = new ArrayCollection();
    $this->activationTimes = new ArrayCollection();
    $this->memberships = new ArrayCollection();
    $this->reservations = new ArrayCollection();
    $this->metas = new ArrayCollection();
    $this->payments = new ArrayCollection();
  }

  #[ORM\PrePersist]
  #[ORM\PreUpdate]
  public function setDefaultValues(): void
  {
    $this->rowIndex = $this->buildRowIndex();
  }

  private function getIndexableFields(): array
  {
    return ["uuid", "title", "shortDescription"];
  }

  public function getTitle(): ?string
  {
    return $this->title;
  }

  public function setTitle(string $title): static
  {
    $this->title = $title;

    return $this;
  }

  public function getShortDescription(): ?string
  {
    return $this->shortDescription;
  }

  public function setShortDescription(?string $shortDescription): static
  {
    $this->shortDescription = $shortDescription;

    return $this;
  }

  public function getLongDescription(): ?string
  {
    return $this->longDescription;
  }

  public function setLongDescription(?string $longDescription): static
  {
    $this->longDescription = $longDescription;

    return $this;
  }

  public function getPlaceName(): ?string
  {
    return $this->placeName;
  }

  public function setPlaceName(?string $placeName): static
  {
    $this->placeName = $placeName;

    return $this;
  }

  public function getStreetName(): ?string
  {
    return $this->streetName;
  }

  public function setStreetName(?string $streetName): static
  {
    $this->streetName = $streetName;

    return $this;
  }

  public function getCity(): ?string
  {
    return $this->city;
  }

  public function setCity(?string $city): static
  {
    $this->city = $city;

    return $this;
  }

  public function getZipCode(): ?int
  {
    return $this->zipCode;
  }

  public function setZipCode(?int $zipCode): static
  {
    $this->zipCode = $zipCode;

    return $this;
  }

  public function getLongitude(): ?string
  {
    return $this->longitude;
  }

  public function setLongitude(?string $longitude): static
  {
    $this->longitude = $longitude;

    return $this;
  }

  public function getLatitude(): ?string
  {
    return $this->latitude;
  }

  public function setLatitude(?string $latitude): static
  {
    $this->latitude = $latitude;

    return $this;
  }

  public function getCountry(): ?string
  {
    return $this->country;
  }

  public function setCountry(?string $country): static
  {
    $this->country = $country;

    return $this;
  }

  public function getPrice(): ?string
  {
    return $this->price;
  }

  public function setPrice(?string $price): static
  {
    $this->price = $price;

    return $this;
  }

  public function getOwner(): ?User
  {
    return $this->owner;
  }

  public function setOwner(?User $owner): static
  {
    $this->owner = $owner;

    return $this;
  }

  public function getCreator(): ?User
  {
    return $this->creator;
  }

  public function setCreator(?User $creator): static
  {
    $this->creator = $creator;

    return $this;
  }

  public function getStatus(): ?string
  {
    return $this->status;
  }

  public function setStatus(string $status): static
  {
    $this->status = $status;

    return $this;
  }


  /**
   * @return Collection<int, ActivityFile>
   */
  public function getFiles(): Collection
  {
    return $this->files;
  }

  public function addFile(ActivityFile $file): static
  {
    if (!$this->files->contains($file)) {
      $this->files->add($file);
      $file->setActivity($this);
    }

    return $this;
  }

  public function removeFile(ActivityFile $file): static
  {
    if ($this->files->removeElement($file)) {
      // set the owning side to null (unless already changed)
      if ($file->getActivity() === $this) {
        $file->setActivity(null);
      }
    }

    return $this;
  }

  /**
   * @return Collection<int, ActivityPartner>
   */
  public function getPartners(): Collection
  {
    return $this->partners;
  }

  public function addPartner(ActivityPartner $partner): static
  {
    if (!$this->partners->contains($partner)) {
      $this->partners->add($partner);
      $partner->setActivity($this);
    }

    return $this;
  }

  public function removePartner(ActivityPartner $partner): static
  {
    if ($this->partners->removeElement($partner)) {
      // set the owning side to null (unless already changed)
      if ($partner->getActivity() === $this) {
        $partner->setActivity(null);
      }
    }

    return $this;
  }

  /**
   * @return Collection<int, ActivityActivationTime>
   */
  public function getActivationTimes(): Collection
  {
    return $this->activationTimes;
  }

  public function addActivationTime(ActivityActivationTime $activationTime): static
  {
    if (!$this->activationTimes->contains($activationTime)) {
      $this->activationTimes->add($activationTime);
      $activationTime->setActivity($this);
    }

    return $this;
  }

  public function removeActivationTime(ActivityActivationTime $activationTime): static
  {
    if ($this->activationTimes->removeElement($activationTime)) {
      // set the owning side to null (unless already changed)
      if ($activationTime->getActivity() === $this) {
        $activationTime->setActivity(null);
      }
    }

    return $this;
  }

  public function getCategory(): ?ActivityCategory
  {
    return $this->category;
  }

  public function setCategory(?ActivityCategory $category): static
  {
    $this->category = $category;

    return $this;
  }

  /**
   * @return Collection<int, ActivityMembership>
   */
  public function getMemberships(): Collection
  {
    return $this->memberships;
  }

  public function addMembership(ActivityMembership $membership): static
  {
    if (!$this->memberships->contains($membership)) {
      $this->memberships->add($membership);
      $membership->setActivity($this);
    }

    return $this;
  }

  public function removeMembership(ActivityMembership $membership): static
  {
    if ($this->memberships->removeElement($membership)) {
      // set the owning side to null (unless already changed)
      if ($membership->getActivity() === $this) {
        $membership->setActivity(null);
      }
    }

    return $this;
  }

  /**
   * @return Collection<int, ActivityReservation>
   */
  public function getReservations(): Collection
  {
    return $this->reservations;
  }

  public function addReservation(ActivityReservation $reservation): static
  {
    if (!$this->reservations->contains($reservation)) {
      $this->reservations->add($reservation);
      $reservation->setActivity($this);
    }

    return $this;
  }

  public function removeReservation(ActivityReservation $reservation): static
  {
    if ($this->reservations->removeElement($reservation)) {
      // set the owning side to null (unless already changed)
      if ($reservation->getActivity() === $this) {
        $reservation->setActivity(null);
      }
    }

    return $this;
  }

  /**
   * @return Collection<int, ActivityMeta>
   */
  public function getMetas(): Collection
  {
    return $this->metas;
  }

  public function addMeta(ActivityMeta $meta): static
  {
    $this->removeMetaIfExistsByCode($meta->getCode());
    if (!$this->metas->contains($meta)) {
      $this->metas->add($meta);
      $meta->setActivity($this);
    }
    return $this;
  }

  public function removeMeta(ActivityMeta $meta): static
  {
    if ($this->metas->removeElement($meta)) {
      // set the owning side to null (unless already changed)
      if ($meta->getActivity() === $this) {
        $meta->setActivity(null);
      }
    }

    return $this;
  }


  public function removeMetaIfExistsByCode(string $code)
  {
    $first = $this->getMetas()->findFirst(function (int $key, ActivityMeta $value) use ($code) {
      return $value->getCode() === $code;
    });
    if (!empty($first)) {
      $this->removeMeta($first);
    }
  }

  public function getMetaValueByCode(string $code, $defaultValue = '')
  {
    $first = $this->getMetas()->findFirst(function (int $key, ActivityMeta $value) use ($code) {
      return $value->getCode() === $code;
    });
    if (!empty($first)) {
      return $first->getValue();
    }
    return $defaultValue;
  }

  public function getInstantActivationTimesArray($filter = false, $filteredDays = [])
  {
    $data = [];
    $days = $this->getMetaValueByCode('_instantActivationTimes');
    if (!empty($days)) {
      $parts = explode('|', $days);
      for ($i = 0; $i < count($parts); $i++) {
        $part = $parts[$i];
        if (empty($part)) {
          continue;
        }
        $parts2 = explode(',', $part);

        if (count($parts2) !== 5) continue;

        $activeDayType = $parts2[4];
        $isEnabled = $parts2[3] == "1" || $parts2[3] == "true" ? true : false;

        if (!$filter) {
          $data[] = [
            "id" => $i,
            "instantId" => $this->getUuid(),
            "activationTimeType" => $parts2[0],
            "startDateTime" => $parts2[1],
            "endDateTime" => $parts2[2],
            "activationStartDateTime" => $parts2[1],
            "activationEndDateTime" => $parts2[2],
            "isEnabled" => $isEnabled,
            "activeDayType" => $activeDayType
          ];
          continue;
        }

        if (!$isEnabled) continue;

        $filteredDayTypes = [];
        $filteredFixedDays = [];
        if (is_array($filteredDays) && count($filteredDays) > 0) {
          for ($t = 0; $t < count($filteredDays); $t++) {
            $daysParts = explode('|', $filteredDays[$t]);
            $dateParts = explode('-', $daysParts[1]);

            $filteredDayTypes[] = $daysParts[0];
            $filteredFixedDays[] = date("Y-m-d", strtotime(trim($dateParts[0])));
          }

          if (count($filteredDayTypes) > 0 && !in_array('fixed', $filteredDayTypes) && $activeDayType  !== 'all_days' && !in_array($activeDayType, $filteredDayTypes)) continue;
        }

        $j = -1;

        while ($j < 13) {
          $j++;
          $time = strtotime("+$j day");
          $date = date("Y-m-d", $time);
          $week = intval(date('w', $time));

          $plusOneYear = strval(intval(date('Y', $time)) + 1) . date('-m-d', $time);

          $start = $activeDayType !== 'fixed' && !str_contains($parts2[1], "-") ? $date . 'T' . $parts2[1] : $parts2[1];
          $end = $activeDayType !== 'fixed' && !str_contains($parts2[2], "-") ? $plusOneYear . 'T' . $parts2[2] : $parts2[2];

          $startTime = strtotime($start);
          $endTime = strtotime($end);

          if ($activeDayType === 'fixed' && ($startTime > $time || $time > $endTime)) continue;
          if ($week === 0 && !in_array($activeDayType, ["fixed", "all_sundays", "all_days", "saturdays_and_sundays"])) continue;
          if ($week === 1 && !in_array($activeDayType, ["fixed", "all_mondays", "all_days", "mondays_to_fridays"])) continue;
          if ($week === 2 && !in_array($activeDayType, ["fixed", "all_tuesdays", "all_days", "mondays_to_fridays"])) continue;
          if ($week === 3 && !in_array($activeDayType, ["fixed", "all_wednesdays", "all_days", "mondays_to_fridays"])) continue;
          if ($week === 4 && !in_array($activeDayType, ["fixed", "all_thursdays", "all_days", "mondays_to_fridays"])) continue;
          if ($week === 5 && !in_array($activeDayType, ["fixed", "all_fridays", "all_days", "mondays_to_fridays"])) continue;
          if ($week === 6 && !in_array($activeDayType, ["fixed", "all_saturdays", "all_days", "saturdays_and_sundays"])) continue;

          if (count($filteredDayTypes) > 0) {
            if ($week === 0 && !in_array("fixed", $filteredDayTypes) && !in_array("all_sundays", $filteredDayTypes) && !in_array("saturdays_and_sundays", $filteredDayTypes)) continue;
            if ($week === 1 && !in_array("fixed", $filteredDayTypes) && !in_array("all_mondays", $filteredDayTypes) && !in_array("mondays_to_fridays", $filteredDayTypes)) continue;
            if ($week === 2 && !in_array("fixed", $filteredDayTypes) && !in_array("all_tuesdays", $filteredDayTypes) && !in_array("mondays_to_fridays", $filteredDayTypes)) continue;
            if ($week === 3 && !in_array("fixed", $filteredDayTypes) && !in_array("all_wednesdays", $filteredDayTypes) && !in_array("mondays_to_fridays", $filteredDayTypes)) continue;
            if ($week === 4 && !in_array("fixed", $filteredDayTypes) && !in_array("all_thursdays", $filteredDayTypes) && !in_array("mondays_to_fridays", $filteredDayTypes)) continue;
            if ($week === 5 && !in_array("fixed", $filteredDayTypes) && !in_array("all_fridays", $filteredDayTypes) && !in_array("mondays_to_fridays", $filteredDayTypes)) continue;
            if ($week === 6 && !in_array("fixed", $filteredDayTypes) && !in_array("all_saturdays", $filteredDayTypes) && !in_array("saturdays_and_sundays", $filteredDayTypes)) continue;
          }

          if ($activeDayType !== 'fixed') {
            $start = $date . 'T' . date("H:i:sP", $startTime);
            $end = $date . 'T' . date("H:i:sP", $endTime);
          }

          $dateActivity = date("Y-m-d", strtotime($start));

          if (!empty($filteredFixedDays) && in_array('fixed', $filteredDayTypes) && !in_array($dateActivity, $filteredFixedDays)) continue;

          if (time() > strtotime($end)) continue;



          $data[] = [
            'id' => $this->getUuid(),
            'companyId' => $this->getOwner()?->getCompany()?->getUuid(),
            'title' => $this->getTitle(),
            'pictureUrl' => $this->getMetaValueByCode('_picture'),
            'shortDescription' => $this->getShortDescription(),
            'categoryId' => $this->getMetaValueByCode('_category'),
            'price' => floatval($this->getPrice()),
            'latitude' => floatval($this->getLatitude()),
            'longitude' => floatval($this->getLongitude()),
            "activationTimeType" => $parts2[0],
            "startDateTime" => $start,
            "endDateTime" => $end,
            "activationStartDateTime" => $start,
            "address" => $this->getStreetName(),
          ];
        }
      }
    }
    return $data;
  }

  public function getInstantPartnersArray()
  {
    $data = [];
    $days = $this->getMetaValueByCode('_instantPartner');
    if (!empty($days)) {
      $parts = explode('|', $days);
      for ($i = 0; $i < count($parts); $i++) {
        $part = $parts[$i];
        if (empty($part)) {
          continue;
        }
        $parts2 = explode(',', $part);
        if (count($parts2) == 3) {
          $data[] = [
            "id" => $parts2[0],
            "name" => $parts2[1],
            "image" => $parts2[2]
          ];
        }
      }
    }
    return $data;
  }

  public function getActivitiesPartnersArray()
  {
    $data = [];
    $partners = $this->getMetaValueByCode('_instantPartner');
    if (!empty($partners)) {
      $parts = explode('|', $partners);
      for ($i = 0; $i < count($parts); $i++) {
        $part = $parts[$i];
        if (empty($part)) {
          continue;
        }
        $parts2 = explode(',', $part);
        //return $parts2;
        //dd($parts);
        if (count($parts2) == 3) {
          $data[] = [
            "id" => $parts2[0],
            "name" => $parts2[1],
            "image" => $parts2[2]
          ];
        }
      }
    }
    return $data;
  }

  public function distanceInMetersFrom($lat, $long): float
  {
    $earthRadius = 6371000;
    $latFrom = deg2rad($this->getLatitude());
    $lonFrom = deg2rad($this->getLongitude());
    $latTo = deg2rad($lat);
    $lonTo = deg2rad($long);

    $lonDelta = $lonTo - $lonFrom;
    $a = pow(cos($latTo) * sin($lonDelta), 2) +
      pow(cos($latFrom) * sin($latTo) - sin($latFrom) * cos($latTo) * cos($lonDelta), 2);
    $b = sin($latFrom) * sin($latTo) + cos($latFrom) * cos($latTo) * cos($lonDelta);

    $angle = atan2(sqrt($a), $b);
    return round($angle * $earthRadius);
  }

  /**
   * @return Collection<int, ActivityPayment>
   */
  public function getPayments(): Collection
  {
    return $this->payments;
  }

  public function addPayment(ActivityPayment $payment): static
  {
    if (!$this->payments->contains($payment)) {
      $this->payments->add($payment);
      $payment->setActivity($this);
    }

    return $this;
  }

  public function removePayment(ActivityPayment $payment): static
  {
    if ($this->payments->removeElement($payment)) {
      // set the owning side to null (unless already changed)
      if ($payment->getActivity() === $this) {
        $payment->setActivity(null);
      }
    }

    return $this;
  }
}

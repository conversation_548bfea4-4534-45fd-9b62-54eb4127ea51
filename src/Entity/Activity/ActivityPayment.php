<?php

namespace App\Entity\Activity;

use App\Entity\Activity\Activity;
use App\Entity\Security\User;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Repository\Activity\ActivityPaymentRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: "activity_payments")]
#[ORM\Entity(repositoryClass: ActivityPaymentRepository::class)]
class ActivityPayment
{
  use IDableTrait, TimestampableTrait;

  #[ORM\ManyToOne(inversedBy: 'payments')]
  #[ORM\JoinColumn(nullable: false)]
  private ?Activity $activity = null;

  #[ORM\Column(length: 255)]
  private ?string $status = null;

  #[ORM\ManyToOne(inversedBy: 'payments')]
  #[ORM\JoinColumn(nullable: false)]
  private ?User $user = null;

  #[ORM\Column(type: Types::DECIMAL, precision: 10, scale: 2)]
  private ?string $amount = null;

  #[ORM\Column]
  private ?int $quantity = null;

  #[ORM\Column(type: Types::DATETIME_MUTABLE)]
  private ?\DateTimeInterface $startValidDate = null;

  #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
  private ?\DateTimeInterface $endValidDate = null;

  public function getActivity(): ?Activity
  {
    return $this->activity;
  }

  public function setActivity(?Activity $activity): static
  {
    $this->activity = $activity;

    return $this;
  }

  public function getStatus(): ?string
  {
    return $this->status;
  }

  public function setStatus(string $status): static
  {
    $this->status = $status;

    return $this;
  }

  public function getUser(): ?User
  {
    return $this->user;
  }

  public function setUser(?User $user): static
  {
    $this->user = $user;

    return $this;
  }

  public function getAmount(): ?string
  {
    return $this->amount;
  }

  public function setAmount(string $amount): static
  {
    $this->amount = $amount;

    return $this;
  }

  public function getQuantity(): ?int
  {
    return $this->quantity;
  }

  public function setQuantity(int $quantity): static
  {
    $this->quantity = $quantity;

    return $this;
  }

  public function getStartValidDate(): ?\DateTimeInterface
  {
    return $this->startValidDate;
  }

  public function setStartValidDate(\DateTimeInterface $startValidDate): static
  {
    $this->startValidDate = $startValidDate;

    return $this;
  }

  public function getEndValidDate(): ?\DateTimeInterface
  {
    return $this->endValidDate;
  }

  public function setEndValidDate(?\DateTimeInterface $endValidDate): static
  {
    $this->endValidDate = $endValidDate;

    return $this;
  }
}

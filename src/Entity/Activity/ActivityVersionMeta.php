<?php

namespace App\Entity\Activity;

use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Repository\Activity\ActivityVersionMetaRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: "activity_version_metas")]
#[ORM\Entity(repositoryClass: ActivityVersionMetaRepository::class)]
class ActivityVersionMeta
{
  use IDableTrait, TimestampableTrait;

  #[ORM\Column(type: Types::TEXT, nullable: true)]
  private ?string $oldValue = null;

  #[ORM\Column(type: Types::TEXT, nullable: true)]
  private ?string $newValue = null;

  #[ORM\ManyToOne(inversedBy: 'metas')]
  #[ORM\JoinColumn(nullable: false)]
  private ?ActivityVersion $version = null;

  public function getOldValue(): ?string
  {
    return $this->oldValue;
  }

  public function setOldValue(?string $oldValue): static
  {
    $this->oldValue = $oldValue;

    return $this;
  }

  public function getNewValue(): ?string
  {
    return $this->newValue;
  }

  public function setNewValue(?string $newValue): static
  {
    $this->newValue = $newValue;

    return $this;
  }

  public function getVersion(): ?ActivityVersion
  {
    return $this->version;
  }

  public function setVersion(?ActivityVersion $version): static
  {
    $this->version = $version;

    return $this;
  }
}

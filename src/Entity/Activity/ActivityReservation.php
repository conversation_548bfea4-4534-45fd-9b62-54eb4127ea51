<?php

namespace App\Entity\Activity;

use App\Entity\Payment\Payment;
use App\Entity\Security\User;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Entity\Traits\UuidableTrait;
use App\Repository\Activity\ActivityReservationRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: "activity_reservations")]
#[ORM\Entity(repositoryClass: ActivityReservationRepository::class)]
class ActivityReservation
{
  use IDableTrait, UuidableTrait, TimestampableTrait;

  #[ORM\ManyToOne(inversedBy: 'reservations')]
  #[ORM\JoinColumn(nullable: false)]
  private ?Activity $activity = null;

  #[ORM\ManyToOne(inversedBy: 'reservations')]
  #[ORM\JoinColumn(nullable: false)]
  private ?User $user = null;

  #[ORM\Column(length: 255)]
  private ?string $status = null;

  #[ORM\Column(type: Types::DATETIME_MUTABLE)]
  private ?\DateTimeInterface $startActivationDate = null;

  #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
  private ?\DateTimeInterface $endActivationDate = null;

  #[ORM\Column]
  private ?bool $autoRenew = null;

  #[ORM\ManyToOne]
  private ?Payment $payment = null;

  #[ORM\ManyToOne]
  #[ORM\JoinColumn(nullable: false)]
  private ?ActivityMembership $membership = null;

  #[ORM\ManyToOne]
  #[ORM\JoinColumn(nullable: false)]
  private ?ActivityMembershipPlan $plan = null;

  public function getId(): ?int
  {
    return $this->id;
  }

  public function getActivity(): ?Activity
  {
    return $this->activity;
  }

  public function setActivity(?Activity $activity): static
  {
    $this->activity = $activity;

    return $this;
  }

  public function getUser(): ?User
  {
    return $this->user;
  }

  public function setUser(?User $user): static
  {
    $this->user = $user;

    return $this;
  }

  public function getStatus(): ?string
  {
    return $this->status;
  }

  public function setStatus(string $status): static
  {
    $this->status = $status;

    return $this;
  }

  public function getStartActivationDate(): ?\DateTimeInterface
  {
    return $this->startActivationDate;
  }

  public function setStartActivationDate(\DateTimeInterface $startActivationDate): static
  {
    $this->startActivationDate = $startActivationDate;

    return $this;
  }

  public function getEndActivationDate(): ?\DateTimeInterface
  {
    return $this->endActivationDate;
  }

  public function setEndActivationDate(?\DateTimeInterface $endActivationDate): static
  {
    $this->endActivationDate = $endActivationDate;

    return $this;
  }

  public function isAutoRenew(): ?bool
  {
    return $this->autoRenew;
  }

  public function setAutoRenew(bool $autoRenew): static
  {
    $this->autoRenew = $autoRenew;

    return $this;
  }

  public function getPayment(): ?Payment
  {
    return $this->payment;
  }

  public function setPayment(?Payment $payment): static
  {
    $this->payment = $payment;

    return $this;
  }

  public function getMembership(): ?ActivityMembership
  {
      return $this->membership;
  }

  public function setMembership(?ActivityMembership $membership): static
  {
      $this->membership = $membership;

      return $this;
  }

  public function getPlan(): ?ActivityMembershipPlan
  {
      return $this->plan;
  }

  public function setPlan(?ActivityMembershipPlan $plan): static
  {
      $this->plan = $plan;

      return $this;
  }
}

<?php

namespace App\Entity\Activity;

use App\Entity\Traits\CodeableTrait;
use App\Entity\Traits\DeActivatableTrait;
use App\Entity\Traits\DescribableTrait;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Entity\Traits\UuidableTrait;
use App\Repository\Activity\ActivityMembershipRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: "activity_memberships")]
#[ORM\Entity(repositoryClass: ActivityMembershipRepository::class)]
class ActivityMembership
{
  use IDableTrait, UuidableTrait, CodeableTrait, DescribableTrait, DeActivatableTrait, TimestampableTrait;

  #[ORM\ManyToOne(inversedBy: 'memberships')]
  #[ORM\JoinColumn(nullable: false)]
  private ?Activity $activity = null;

  #[ORM\OneToMany(mappedBy: 'membership', targetEntity: ActivityMembershipPlan::class, orphanRemoval: true)]
  private Collection $plans;

  public function __construct()
  {
    $this->plans = new ArrayCollection();
  }

  public function getActivity(): ?Activity
  {
    return $this->activity;
  }

  public function setActivity(?Activity $activity): static
  {
    $this->activity = $activity;

    return $this;
  }

  /**
   * @return Collection<int, ActivityMembershipPlan>
   */
  public function getPlans(): Collection
  {
    return $this->plans;
  }

  public function addPlan(ActivityMembershipPlan $plan): static
  {
    if (!$this->plans->contains($plan)) {
      $this->plans->add($plan);
      $plan->setMembership($this);
    }

    return $this;
  }

  public function removePlan(ActivityMembershipPlan $plan): static
  {
    if ($this->plans->removeElement($plan)) {
      // set the owning side to null (unless already changed)
      if ($plan->getMembership() === $this) {
        $plan->setMembership(null);
      }
    }

    return $this;
  }
}

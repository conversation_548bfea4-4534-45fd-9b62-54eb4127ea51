<?php

namespace App\Entity\Activity;

use App\Entity\Security\User;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Repository\Activity\ActivityVersionRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: "activity_versions")]
#[ORM\Entity(repositoryClass: ActivityVersionRepository::class)]
class ActivityVersion
{
  use IDableTrait, TimestampableTrait;

  #[ORM\ManyToOne]
  #[ORM\JoinColumn(nullable: false)]
  private ?User $editor = null;

  #[ORM\OneToMany(mappedBy: 'version', targetEntity: ActivityVersionMeta::class, orphanRemoval: true)]
  private Collection $metas;

  public function __construct()
  {
    $this->metas = new ArrayCollection();
  }

  public function getEditor(): ?User
  {
    return $this->editor;
  }

  public function setEditor(?User $editor): static
  {
    $this->editor = $editor;

    return $this;
  }

  /**
   * @return Collection<int, ActivityVersionMeta>
   */
  public function getMetas(): Collection
  {
    return $this->metas;
  }

  public function addMeta(ActivityVersionMeta $meta): static
  {
    if (!$this->metas->contains($meta)) {
      $this->metas->add($meta);
      $meta->setVersion($this);
    }

    return $this;
  }

  public function removeMeta(ActivityVersionMeta $meta): static
  {
    if ($this->metas->removeElement($meta)) {
      // set the owning side to null (unless already changed)
      if ($meta->getVersion() === $this) {
        $meta->setVersion(null);
      }
    }

    return $this;
  }
}

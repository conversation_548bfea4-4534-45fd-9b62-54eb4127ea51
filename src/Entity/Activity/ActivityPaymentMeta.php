<?php

namespace App\Entity\Activity;

use App\Entity\Traits\DescribableTrait;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Entity\Traits\ValueableTrait;
use App\Repository\Activity\ActivityPaymentMetaRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: "activity_payment_metas")]
#[ORM\Entity(repositoryClass: ActivityPaymentMetaRepository::class)]
class ActivityPaymentMeta
{
  use IDableTrait, TimestampableTrait, DescribableTrait, ValueableTrait;

  #[ORM\Column(length: 200)]
  private ?string $code = null;

  public function getCode(): ?string
  {
    return $this->code;
  }

  public function setCode(string $code): static
  {
    $this->code = $code;

    return $this;
  }
}

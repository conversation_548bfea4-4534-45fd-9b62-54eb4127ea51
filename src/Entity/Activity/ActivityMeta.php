<?php

namespace App\Entity\Activity;

use App\Entity\Traits\DescribableTrait;
use App\Entity\Traits\IDableTrait;
use App\Repository\Activity\ActivityMetaRepository;
use Doctrine\ORM\Mapping as ORM;
use App\Entity\Traits\TimestampableTrait;
use App\Entity\Traits\ValueableTrait;

#[ORM\Table(name: "activity_metas")]
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: ActivityMetaRepository::class)]
class ActivityMeta
{
  use IDableTrait, TimestampableTrait, DescribableTrait, ValueableTrait;

  #[ORM\ManyToOne(inversedBy: 'metas')]
  #[ORM\JoinColumn(nullable: false)]
  private ?Activity $activity = null;

  #[ORM\Column(length: 200)]
  private ?string $code = null;

  public function getCode(): ?string
  {
    return $this->code;
  }

  public function setCode(string $code): static
  {
    $this->code = $code;

    return $this;
  }

  public function getActivity(): ?Activity
  {
    return $this->activity;
  }

  public function setActivity(?Activity $activity): static
  {
    $this->activity = $activity;

    return $this;
  }
}

<?php

namespace App\Entity\Activity;

use App\Entity\Shared\ActiveDayType;
use App\Entity\Traits\DeActivatableTrait;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Entity\Traits\UuidableTrait;
use App\Repository\Activity\ActivityActivationTimeRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: "activity_activation_times")]
#[ORM\Entity(repositoryClass: ActivityActivationTimeRepository::class)]
class ActivityActivationTime
{
  use IDableTrait, UuidableTrait, DeActivatableTrait, TimestampableTrait;

  #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
  private ?\DateTimeInterface $activationStartDateTime = null;

  #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
  private ?\DateTimeInterface $activationEndDateTime = null;

  #[ORM\Column(type: Types::DATETIME_MUTABLE)]
  private ?\DateTimeInterface $startDateTime = null;

  #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
  private ?\DateTimeInterface $endDateTime = null;

  #[ORM\ManyToOne]
  #[ORM\JoinColumn(nullable: false)]
  private ?ActiveDayType $activeDayType = null;

  #[ORM\ManyToOne(inversedBy: 'activationTimes')]
  #[ORM\JoinColumn(nullable: false)]
  private ?Activity $activity = null;

  #[ORM\ManyToOne]
  #[ORM\JoinColumn(nullable: false)]
  private ?ActivityActivationTimeType $activationTimeType = null;


  public function getActivationStartDateTime(): ?\DateTimeInterface
  {
    return $this->activationStartDateTime;
  }

  public function setActivationStartDateTime(?\DateTimeInterface $activationStartDateTime): static
  {
    $this->activationStartDateTime = $activationStartDateTime;

    return $this;
  }

  public function getActivationEndDateTime(): ?\DateTimeInterface
  {
    return $this->activationEndDateTime;
  }

  public function setActivationEndDateTime(?\DateTimeInterface $activationEndDateTime): static
  {
    $this->activationEndDateTime = $activationEndDateTime;

    return $this;
  }

  public function getStartDateTime(): ?\DateTimeInterface
  {
    return $this->startDateTime;
  }

  public function setStartDateTime(\DateTimeInterface $startDateTime): static
  {
    $this->startDateTime = $startDateTime;

    return $this;
  }

  public function getEndDateTime(): ?\DateTimeInterface
  {
    return $this->endDateTime;
  }

  public function setEndDateTime(?\DateTimeInterface $endDateTime): static
  {
    $this->endDateTime = $endDateTime;

    return $this;
  }

  public function getActiveDayType(): ?ActiveDayType
  {
    return $this->activeDayType;
  }

  public function setActiveDayType(?ActiveDayType $activeDayType): static
  {
    $this->activeDayType = $activeDayType;

    return $this;
  }

  public function getActivity(): ?Activity
  {
    return $this->activity;
  }

  public function setActivity(?Activity $activity): static
  {
    $this->activity = $activity;

    return $this;
  }

  public function getActivationTimeType(): ?ActivityActivationTimeType
  {
    return $this->activationTimeType;
  }

  public function setActivationTimeType(?ActivityActivationTimeType $activationTimeType): static
  {
    $this->activationTimeType = $activationTimeType;

    return $this;
  }
}

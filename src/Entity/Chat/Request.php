<?php

namespace App\Entity\Chat;

use App\Entity\Security\User;
use App\Entity\Traits\DescribableTrait;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Repository\Chat\RequestRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: "requests")]
#[ORM\Entity(repositoryClass: RequestRepository::class)]
class Request
{
  use IDableTrait, DescribableTrait, TimestampableTrait;

  #[ORM\Column(length: 255)]
  private ?string $titre = null;

  #[ORM\Column]
  private ?bool $closed = null;

  #[ORM\ManyToOne(inversedBy: 'requests')]
  #[ORM\JoinColumn(nullable: false)]
  private ?User $creator = null;

  #[ORM\OneToMany(mappedBy: 'request', targetEntity: Response::class, orphanRemoval: true)]
  private Collection $responses;

  #[ORM\OneToMany(mappedBy: 'request', targetEntity: Attachment::class)]
  private Collection $attachments;

  #[ORM\Column(length: 255, nullable: true)]
  private ?string $user = null;

  #[ORM\Column(length: 255, nullable: true)]
  private ?string $closedBy = null;

  public function __construct()
  {
    $this->responses = new ArrayCollection();
    $this->attachments = new ArrayCollection();
  }

  public function getTitre(): ?string
  {
    return $this->titre;
  }

  public function setTitre(string $titre): static
  {
    $this->titre = $titre;

    return $this;
  }

  public function isClosed(): ?bool
  {
    return $this->closed;
  }

  public function setClosed(bool $closed): static
  {
    $this->closed = $closed;

    return $this;
  }

  public function getCreator(): ?User
  {
    return $this->creator;
  }

  public function setCreator(?User $creator): static
  {
    $this->creator = $creator;

    return $this;
  }

  /**
   * @return Collection<int, Response>
   */
  public function getResponses(): Collection
  {
    return $this->responses;
  }

  public function addResponse(Response $response): static
  {
    if (!$this->responses->contains($response)) {
      $this->responses->add($response);
      $response->setRequest($this);
    }

    return $this;
  }

  public function removeResponse(Response $response): static
  {
    if ($this->responses->removeElement($response)) {
      // set the owning side to null (unless already changed)
      if ($response->getRequest() === $this) {
        $response->setRequest(null);
      }
    }

    return $this;
  }

  /**
   * @return Collection<int, Attachment>
   */
  public function getAttachments(): Collection
  {
    return $this->attachments;
  }

  public function addAttachment(Attachment $attachment): static
  {
    if (!$this->attachments->contains($attachment)) {
      $this->attachments->add($attachment);
      $attachment->setRequest($this);
    }

    return $this;
  }

  public function removeAttachment(Attachment $attachment): static
  {
    if ($this->attachments->removeElement($attachment)) {
      // set the owning side to null (unless already changed)
      if ($attachment->getRequest() === $this) {
        $attachment->setRequest(null);
      }
    }

    return $this;
  }

  public function getUser(): ?string
  {
      return $this->user;
  }

  public function setUser(?string $user): static
  {
      $this->user = $user;

      return $this;
  }
  public function getClosedBy(): ?string
  {
      return $this->closedBy;
  }

  public function setClosedBy(?string $closedBy): static
  {
      $this->closedBy = $closedBy;

      return $this;
  }
}

<?php

namespace App\Entity\Chat;

use App\Entity\Traits\DescribableTrait;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\IndexableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Entity\Traits\UuidableTrait;
use App\Repository\Chat\DiscussionRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;

#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: "discussions")]
#[ORM\Entity(repositoryClass: DiscussionRepository::class)]
class Discussion
{
  use IDableTrait, UuidableTrait, DescribableTrait, TimestampableTrait, IndexableTrait;

  #[ORM\Column(length: 255)]
  private ?string $title = null;

  #[ORM\Column(length: 255)]
  private ?string $status = null;

  #[ORM\ManyToOne]
  private ?DiscussionMessage $lastMessage = null;

  #[ORM\OneToMany(mappedBy: 'discussion', targetEntity: DiscussionMember::class, orphanRemoval: true)]
  private Collection $members;

  #[ORM\OneToMany(mappedBy: 'discussion', targetEntity: DiscussionMessage::class, orphanRemoval: true)]
  private Collection $messages;

  public function __construct()
  {
    $this->members = new ArrayCollection();
    $this->messages = new ArrayCollection();
  }

  public function getTitle(): ?string
  {
    return $this->title;
  }

  public function setTitle(string $title): static
  {
    $this->title = $title;

    return $this;
  }

  public function getStatus(): ?string
  {
    return $this->status;
  }

  public function setStatus(string $status): static
  {
    $this->status = $status;

    return $this;
  }

  public function getLastMessage(): ?DiscussionMessage
  {
    return $this->lastMessage;
  }

  public function setLastMessage(?DiscussionMessage $lastMessage): static
  {
    $this->lastMessage = $lastMessage;

    return $this;
  }

  /**
   * @return Collection<int, DiscussionMember>
   */
  public function getMembers(): Collection
  {
    return $this->members;
  }

  public function addMember(DiscussionMember $member): static
  {
    if (!$this->members->contains($member)) {
      $this->members->add($member);
      $member->setDiscussion($this);
    }

    return $this;
  }

  public function removeMember(DiscussionMember $member): static
  {
    if ($this->members->removeElement($member)) {
      // set the owning side to null (unless already changed)
      if ($member->getDiscussion() === $this) {
        $member->setDiscussion(null);
      }
    }

    return $this;
  }

  /**
   * @return Collection<int, DiscussionMessage>
   */
  public function getMessages(): Collection
  {
      return $this->messages;
  }

  public function addMessage(DiscussionMessage $message): static
  {
      if (!$this->messages->contains($message)) {
          $this->messages->add($message);
          $message->setDiscussion($this);
      }

      return $this;
  }

  public function removeMessage(DiscussionMessage $message): static
  {
      if ($this->messages->removeElement($message)) {
          // set the owning side to null (unless already changed)
          if ($message->getDiscussion() === $this) {
              $message->setDiscussion(null);
          }
      }

      return $this;
  }
}

<?php

namespace App\Entity\Chat;

use App\Entity\Security\User;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Entity\Traits\UuidableTrait;
use App\Repository\Chat\DiscussionMemberRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: "discussion_members")]
#[ORM\Entity(repositoryClass: DiscussionMemberRepository::class)]
class DiscussionMember
{
  use IDableTrait, UuidableTrait, TimestampableTrait;

  #[ORM\Column(length: 255)]
  private ?string $status = null;

  #[ORM\ManyToOne(inversedBy: 'members')]
  #[ORM\JoinColumn(nullable: false)]
  private ?Discussion $discussion = null;

  #[ORM\ManyToOne]
  #[ORM\JoinColumn(nullable: false)]
  private ?User $user = null;

  #[ORM\ManyToOne]
  private ?DiscussionMessage $lastReadMessage = null;

  public function getStatus(): ?string
  {
    return $this->status;
  }

  public function setStatus(string $status): static
  {
    $this->status = $status;

    return $this;
  }

  public function getDiscussion(): ?Discussion
  {
    return $this->discussion;
  }

  public function setDiscussion(?Discussion $discussion): static
  {
    $this->discussion = $discussion;

    return $this;
  }

  public function getUser(): ?User
  {
    return $this->user;
  }

  public function setUser(?User $user): static
  {
    $this->user = $user;

    return $this;
  }

  public function getLastReadMessage(): ?DiscussionMessage
  {
    return $this->lastReadMessage;
  }

  public function setLastReadMessage(?DiscussionMessage $lastReadMessage): static
  {
    $this->lastReadMessage = $lastReadMessage;

    return $this;
  }
}

<?php

namespace App\Entity\Chat;

use App\Entity\Security\User;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Entity\Traits\UuidableTrait;
use App\Repository\Chat\DiscussionMessageRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: "discussion_messages")]
#[ORM\Entity(repositoryClass: DiscussionMessageRepository::class)]
class DiscussionMessage
{
  use IDableTrait, UuidableTrait, TimestampableTrait;

  #[ORM\Column(type: Types::TEXT, nullable: true)]
  private ?string $content = null;

  #[ORM\ManyToOne]
  #[ORM\JoinColumn(nullable: false)]
  private ?User $owner = null;

  #[ORM\OneToMany(mappedBy: 'message', targetEntity: DiscussionMessageAttachment::class, orphanRemoval: true)]
  private Collection $attachments;

  #[ORM\ManyToOne(inversedBy: 'messages')]
  #[ORM\JoinColumn(nullable: false)]
  private ?Discussion $discussion = null;

  public function __construct()
  {
      $this->attachments = new ArrayCollection();
  }

  public function getContent(): ?string
  {
    return $this->content;
  }

  public function setContent(?string $content): static
  {
    $this->content = $content;

    return $this;
  }

  public function getOwner(): ?User
  {
    return $this->owner;
  }

  public function setOwner(?User $owner): static
  {
    $this->owner = $owner;

    return $this;
  }

  /**
   * @return Collection<int, DiscussionMessageAttachment>
   */
  public function getAttachments(): Collection
  {
      return $this->attachments;
  }

  public function addAttachment(DiscussionMessageAttachment $attachment): static
  {
      if (!$this->attachments->contains($attachment)) {
          $this->attachments->add($attachment);
          $attachment->setMessage($this);
      }

      return $this;
  }

  public function removeAttachment(DiscussionMessageAttachment $attachment): static
  {
      if ($this->attachments->removeElement($attachment)) {
          // set the owning side to null (unless already changed)
          if ($attachment->getMessage() === $this) {
              $attachment->setMessage(null);
          }
      }

      return $this;
  }

  public function getDiscussion(): ?Discussion
  {
      return $this->discussion;
  }

  public function setDiscussion(?Discussion $discussion): static
  {
      $this->discussion = $discussion;

      return $this;
  }
}

<?php

namespace App\Entity\Chat;

use App\Entity\Shared\File;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Entity\Traits\UuidableTrait;
use App\Repository\Chat\DiscussionMessageAttachmentRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: "discussion_message_attachments")]
#[ORM\Entity(repositoryClass: DiscussionMessageAttachmentRepository::class)]
class DiscussionMessageAttachment
{
  use IDableTrait, UuidableTrait, TimestampableTrait;

  #[ORM\ManyToOne]
  #[ORM\JoinColumn(nullable: false)]
  private ?File $file = null;

  #[ORM\ManyToOne(inversedBy: 'attachments')]
  #[ORM\JoinColumn(nullable: false)]
  private ?DiscussionMessage $message = null;

  public function getFile(): ?File
  {
    return $this->file;
  }

  public function setFile(?File $file): static
  {
    $this->file = $file;

    return $this;
  }

  public function getMessage(): ?DiscussionMessage
  {
    return $this->message;
  }

  public function setMessage(?DiscussionMessage $message): static
  {
    $this->message = $message;

    return $this;
  }
}

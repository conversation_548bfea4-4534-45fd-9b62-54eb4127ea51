<?php

namespace App\Entity\Chat;

use App\Entity\Security\User;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Repository\Chat\ResponseRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: "responses")]
#[ORM\Entity(repositoryClass: ResponseRepository::class)]
class Response
{
  use IDableTrait, TimestampableTrait;

  #[ORM\Column(type: Types::TEXT)]
  private ?string $message = null;

  #[ORM\ManyToOne]
  #[ORM\JoinColumn(nullable: false)]
  private ?User $editor = null;

  #[ORM\ManyToOne(inversedBy: 'responses')]
  #[ORM\JoinColumn(nullable: false)]
  private ?Request $request = null;

  #[ORM\OneToMany(mappedBy: 'response', targetEntity: Attachment::class)]
  private Collection $attachments;

  #[ORM\Column(nullable: true)]
  private ?bool $isAdmin = null;

  #[ORM\Column(length: 255, nullable: true)]
  private ?string $userTimestamp = null;

  #[ORM\Column(length: 255, nullable: true)]
  private ?string $userName = null;

  #[ORM\Column(length: 255, nullable: true)]
  private ?string $userUuid = null;

  #[ORM\Column(length: 255, nullable: true)]
  private ?string $messageUuid = null;

  #[ORM\Column(length: 255, nullable: true)]
  private ?string $user = null;

  #[ORM\Column(length: 255, nullable: true)]
  private ?string $userAvatar = null;

  public function __construct()
  {
    $this->attachments = new ArrayCollection();
  }

  public function getMessage(): ?string
  {
    return $this->message;
  }

  public function setMessage(string $message): static
  {
    $this->message = $message;

    return $this;
  }

  public function getEditor(): ?User
  {
    return $this->editor;
  }

  public function setEditor(?User $editor): static
  {
    $this->editor = $editor;

    return $this;
  }

  public function getRequest(): ?Request
  {
    return $this->request;
  }

  public function setRequest(?Request $request): static
  {
    $this->request = $request;

    return $this;
  }

  /**
   * @return Collection<int, Attachment>
   */
  public function getAttachments(): Collection
  {
    return $this->attachments;
  }

  public function addAttachment(Attachment $attachment): static
  {
    if (!$this->attachments->contains($attachment)) {
      $this->attachments->add($attachment);
      $attachment->setResponse($this);
    }

    return $this;
  }

  public function removeAttachment(Attachment $attachment): static
  {
    if ($this->attachments->removeElement($attachment)) {
      // set the owning side to null (unless already changed)
      if ($attachment->getResponse() === $this) {
        $attachment->setResponse(null);
      }
    }

    return $this;
  }

  public function isIsAdmin(): ?bool
  {
      return $this->isAdmin;
  }

  public function setIsAdmin(?bool $isAdmin): static
  {
      $this->isAdmin = $isAdmin;

      return $this;
  }

  public function getUserTimestamp(): ?string
  {
      return $this->userTimestamp;
  }

  public function setUserTimestamp(?string $userTimestamp): static
  {
      $this->userTimestamp = $userTimestamp;

      return $this;
  }

  public function getUserName(): ?string
  {
      return $this->userName;
  }

  public function setUserName(?string $userName): static
  {
      $this->userName = $userName;

      return $this;
  }

  public function getUserUuid(): ?string
  {
      return $this->userUuid;
  }

  public function setUserUuid(?string $userUuid): static
  {
      $this->userUuid = $userUuid;

      return $this;
  }

  public function getMessageUuid(): ?string
  {
      return $this->messageUuid;
  }

  public function setMessageUuid(?string $messageUuid): static
  {
      $this->messageUuid = $messageUuid;

      return $this;
  }

  public function getUser(): ?string
  {
      return $this->user;
  }

  public function setUser(?string $user): static
  {
      $this->user = $user;

      return $this;
  }

  public function getUserAvatar(): ?string
  {
      return $this->userAvatar;
  }

  public function setUserAvatar(?string $userAvatar): static
  {
      $this->userAvatar = $userAvatar;

      return $this;
  }
}

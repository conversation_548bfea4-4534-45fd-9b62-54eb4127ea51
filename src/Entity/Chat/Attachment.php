<?php

namespace App\Entity\Chat;

use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Repository\Chat\AttachmentRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\HasLifecycleCallbacks]
#[ORM\Table(name: "attachments")]
#[ORM\Entity(repositoryClass: AttachmentRepository::class)]
class Attachment
{
  use IDableTrait, TimestampableTrait;

  #[ORM\Column(length: 255)]
  private ?string $link = null;

  #[ORM\ManyToOne(inversedBy: 'attachments')]
  private ?Request $request = null;

  #[ORM\ManyToOne(inversedBy: 'attachments')]
  private ?Response $response = null;

  public function getLink(): ?string
  {
    return $this->link;
  }

  public function setLink(string $link): static
  {
    $this->link = $link;

    return $this;
  }

  public function getRequest(): ?Request
  {
    return $this->request;
  }

  public function setRequest(?Request $request): static
  {
    $this->request = $request;

    return $this;
  }

  public function getResponse(): ?Response
  {
    return $this->response;
  }

  public function setResponse(?Response $response): static
  {
    $this->response = $response;

    return $this;
  }
}

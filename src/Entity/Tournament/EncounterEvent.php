<?php

namespace App\Entity\Tournament;

use App\Entity\Player\Player;
use App\Entity\Traits\IDableTrait;
use App\Entity\Traits\TimestampableTrait;
use App\Entity\Traits\UuidableTrait;
use App\Repository\Tournament\EncounterEventRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: EncounterEventRepository::class)]
#[ORM\Table(name: 'encounter_events')]
class EncounterEvent
{
    use IDableTrait;
    use UuidableTrait;
    use TimestampableTrait;

    #[ORM\ManyToOne(inversedBy: 'events')]
    #[ORM\JoinColumn(nullable: false)]
    private ?Encounter $encounter = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    private ?Player $player = null;

    #[ORM\Column(length: 50)]
    private ?string $type = null; // GOAL, YELLOW_CARD, RED_CARD, ASSIST

    #[ORM\Column(type: Types::INTEGER, nullable: true)]
    private ?int $minute = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $description = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: true)]
    private ?Player $assistPlayer = null; // For goals with assists

    public function getEncounter(): ?Encounter
    {
        return $this->encounter;
    }

    public function setEncounter(?Encounter $encounter): static
    {
        $this->encounter = $encounter;
        return $this;
    }

    public function getPlayer(): ?Player
    {
        return $this->player;
    }

    public function setPlayer(?Player $player): static
    {
        $this->player = $player;
        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): static
    {
        $this->type = $type;
        return $this;
    }

    public function getMinute(): ?int
    {
        return $this->minute;
    }

    public function setMinute(?int $minute): static
    {
        $this->minute = $minute;
        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): static
    {
        $this->description = $description;
        return $this;
    }

    public function getAssistPlayer(): ?Player
    {
        return $this->assistPlayer;
    }

    public function setAssistPlayer(?Player $assistPlayer): static
    {
        $this->assistPlayer = $assistPlayer;
        return $this;
    }

    public function isGoal(): bool
    {
        return $this->type === 'GOAL';
    }

    public function isYellowCard(): bool
    {
        return $this->type === 'YELLOW_CARD';
    }

    public function isRedCard(): bool
    {
        return $this->type === 'RED_CARD';
    }

    public function isAssist(): bool
    {
        return $this->type === 'ASSIST';
    }
}

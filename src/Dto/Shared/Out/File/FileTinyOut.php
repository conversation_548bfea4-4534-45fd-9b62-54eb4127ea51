<?php

namespace App\Dto\Shared\Out\File;

use OpenApi\Attributes as OA;

class FileTinyOut
{
  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?string $usage = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?string $url = null;

  public function getUsage(): ?string
  {
    return $this->usage;
  }

  public function setUuid(?string $usage): static
  {
    $this->usage = $usage;

    return $this;
  }

  public function getUrl(): ?string
  {
    return $this->url;
  }

  public function setUrl(?string $url): static
  {
    $this->url = $url;

    return $this;
  }
}

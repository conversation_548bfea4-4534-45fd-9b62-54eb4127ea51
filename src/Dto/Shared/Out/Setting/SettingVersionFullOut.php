<?php

namespace App\Dto\Shared\Out\Setting;

use App\Dto\Security\Out\User\UserTinyOut;
use OpenApi\Attributes as OA;

class SettingVersionFullOut
{
  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?int $id = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?string $oldValue = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?string $newValue = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?\DateTimeImmutable $createdAt = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?\DateTimeImmutable $updatedAt = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?UserTinyOut $editor = null;

  public function getId(): ?int
  {
    return $this->id;
  }

  public function setId(?int $id): static
  {
    $this->id = $id;

    return $this;
  }

  public function getOldValue(): ?string
  {
    return $this->oldValue;
  }

  public function setOldValue(?string $oldValue): static
  {
    $this->oldValue = $oldValue;

    return $this;
  }

  public function getNewValue(): ?string
  {
    return $this->newValue;
  }

  public function setNewValue(?string $newValue): static
  {
    $this->newValue = $newValue;

    return $this;
  }

  public function getEditor(): ?UserTinyOut
  {
    return $this->editor;
  }

  public function setEditor(?UserTinyOut $editor): static
  {
    $this->editor = $editor;

    return $this;
  }


  public function getCreatedAt(): ?\DateTimeImmutable
  {
    return $this->createdAt;
  }

  public function setCreatedAt(\DateTimeImmutable $createdAt): static
  {
    $this->createdAt = $createdAt;

    return $this;
  }

  public function getUpdatedAt(): ?\DateTimeImmutable
  {
    return $this->updatedAt;
  }

  public function setUpdatedAt(?\DateTimeImmutable $updatedAt): static
  {
    $this->updatedAt = $updatedAt;

    return $this;
  }
}

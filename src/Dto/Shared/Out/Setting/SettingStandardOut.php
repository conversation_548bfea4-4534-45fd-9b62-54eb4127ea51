<?php

namespace App\Dto\Shared\Out\Setting;

use App\Dto\Security\Out\User\UserTinyOut;
use OpenApi\Attributes as OA;

class SettingStandardOut extends SettingMinimalOut
{
  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?string $description = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?\DateTimeImmutable $updatedAt = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?UserTinyOut $owner = null;

  public function getDescription(): ?string
  {
    return $this->description;
  }

  public function setDescription(?string $description): static
  {
    $this->description = $description;

    return $this;
  }

  public function getOwner(): ?UserTinyOut
  {
    return $this->owner;
  }

  public function setOwner(?UserTinyOut $owner): static
  {
    $this->owner = $owner;

    return $this;
  }

  public function getUpdatedAt(): ?\DateTimeImmutable
  {
    return $this->updatedAt;
  }

  public function setUpdatedAt(?\DateTimeImmutable $updatedAt): static
  {
    $this->updatedAt = $updatedAt;

    return $this;
  }
}

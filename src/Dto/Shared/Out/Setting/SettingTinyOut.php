<?php

namespace App\Dto\Shared\Out\Setting;

use OpenApi\Attributes as OA;

class SettingTinyOut
{
  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?string $code = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?string $value = null;

  public function getCode(): ?string
  {
    return $this->code;
  }

  public function setCode(?string $code): static
  {
    $this->code = $code;

    return $this;
  }

  public function getValue(): ?string
  {
    return $this->value;
  }

  public function setValue(?string $value): static
  {
    $this->value = $value;

    return $this;
  }
}

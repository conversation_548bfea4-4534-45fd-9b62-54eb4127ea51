<?php

namespace App\Dto\Shared\Out\Setting;

use OpenApi\Attributes as OA;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\ArrayCollection;

class SettingFullOut extends SettingStandardOut
{
  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?\DateTimeImmutable $createdAt = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected Collection $settingVersions;

  public function __construct()
  {
    $this->settingVersions = new ArrayCollection();
  }

  /**
   * @return Collection<int, SettingVersionFullOut>
   */
  public function getSettingVersions(): Collection
  {
    return $this->settingVersions;
  }

  public function setSettingVersions(Collection $settingVersions): static
  {
    $this->settingVersions = $settingVersions;

    return $this;
  }

  public function addSettingVersion(SettingVersionFullOut $settingVersion): static
  {
    if (!$this->settingVersions->contains($settingVersion)) {
      $this->settingVersions->add($settingVersion);
    }

    return $this;
  }

  public function removeSettingVersion(SettingVersionFullOut $settingVersion): static
  {
    $this->settingVersions->removeElement($settingVersion);

    return $this;
  }

  public function getCreatedAt(): ?\DateTimeImmutable
  {
    return $this->createdAt;
  }

  public function setCreatedAt(\DateTimeImmutable $createdAt): static
  {
    $this->createdAt = $createdAt;

    return $this;
  }
}

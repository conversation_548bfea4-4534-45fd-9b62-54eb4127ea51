<?php

namespace App\Dto\Shared\In;

class QueryFilterArrayIn
{
  private ?string $field = null;

  private ?array $values = [];


  public function getField(): ?string
  {
    return $this->field;
  }

  public function setField(?string $field): static
  {
    $this->field = $field;

    return $this;
  }

  public function getValues(): ?array
  {
    return $this->values;
  }

  public function setValues(string ...$values): static
  {
    $this->values = $values;

    return $this;
  }
}

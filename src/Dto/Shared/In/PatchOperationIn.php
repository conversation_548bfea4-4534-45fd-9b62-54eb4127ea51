<?php

namespace App\Dto\Shared\In;

use Symfony\Component\Validator\Constraints as Assert;
use OpenApi\Attributes as OA;

class PatchOperationIn
{
  #[Assert\NotNull]
  #[Assert\NotBlank]
  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?string $type = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private array $payload;


  public function getType(): ?string
  {
    return $this->type;
  }

  public function setType(?string $type): static
  {
    $this->type = $type;

    return $this;
  }


  public function getPayload(): array
  {
    return $this->payload;
  }


  public function setPayload(array $payload): static
  {
    $this->payload = $payload;

    return $this;
  }
}

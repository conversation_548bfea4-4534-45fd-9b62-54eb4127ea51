<?php

namespace App\Dto\Shared\In;

use OpenApi\Attributes as OA;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\ArrayCollection;

class FindAllFilterIn
{

  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?int $limit = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?int $offset = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?string $search = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?string $output = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected Collection $filters;

  public function __construct()
  {
    $this->filters = new ArrayCollection();
  }

  public function getLimit(): ?int
  {
    return $this->limit;
  }

  public function setLimit(?int $limit): static
  {
    $this->limit = $limit;

    return $this;
  }

  public function getOffset(): ?int
  {
    return $this->offset;
  }

  public function setOffset(?int $offset): static
  {
    $this->offset = $offset;

    return $this;
  }

  public function getSearch(): ?string
  {
    return $this->search;
  }

  public function setSearch(?string $search): static
  {
    $this->search = $search;

    return $this;
  }

  public function getOutput(): ?string
  {
    return $this->output;
  }

  public function setOutput(?string $output): static
  {
    $this->output = $output;

    return $this;
  }

  /**
   * @return Collection<int, QueryFilterArrayIn>
   */
  public function getFilters(): Collection
  {
    return $this->filters;
  }


  public function setFilters(Collection $filters): static
  {
    $this->filters = $filters;

    return $this;
  }

  public function addFilters(QueryFilterArrayIn $filter): static
  {
    if (!$this->filters->contains($filter)) {
      $this->filters->add($filter);
    }

    return $this;
  }

  public function removeFilters(QueryFilterArrayIn $filter): static
  {
    $this->filters->removeElement($filter);

    return $this;
  }
}

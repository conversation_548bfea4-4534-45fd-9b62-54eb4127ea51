<?php

namespace App\Dto\Shared\In;

use Symfony\Component\Validator\Constraints as Assert;
use OpenApi\Attributes as OA;

class QueryFilterIn
{
  #[Assert\NotNull]
  #[Assert\NotBlank]
  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?string $field = null;

  #[Assert\NotNull]
  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?string $values = null;


  public function getField(): ?string
  {
    return $this->field;
  }

  public function setField(?string $field): static
  {
    $this->field = $field;

    return $this;
  }

  public function getValues(): ?string
  {
    return $this->values;
  }

  public function setValues(?string $values): static
  {
    $this->values = $values;

    return $this;
  }
}

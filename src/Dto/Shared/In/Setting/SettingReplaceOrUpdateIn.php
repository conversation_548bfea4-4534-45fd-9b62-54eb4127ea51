<?php

namespace App\Dto\Shared\In\Setting;

use OpenApi\Attributes as OA;

class SettingReplaceOrUpdateIn
{
  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?string $value = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?string $description = null;

  public function getValue(): ?string
  {
    return $this->value;
  }

  public function setValue(?string $value): static
  {
    $this->value = $value;

    return $this;
  }

  public function getDescription(): ?string
  {
    return $this->description;
  }

  public function setDescription(?string $description): static
  {
    $this->description = $description;

    return $this;
  }
}

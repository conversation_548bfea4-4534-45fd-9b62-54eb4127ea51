<?php

namespace App\Dto\Shared\In\Setting;

use Symfony\Component\Validator\Constraints as Assert;
use OpenApi\Attributes as OA;

class SettingCreateIn
{
  #[Assert\NotNull]
  #[Assert\NotBlank]
  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?string $code = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?string $value = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?string $description = null;

  public function getCode(): ?string
  {
    return $this->code;
  }

  public function setCode(?string $code): static
  {
    $this->code = $code;

    return $this;
  }

  public function getValue(): ?string
  {
    return $this->value;
  }

  public function setValue(?string $value): static
  {
    $this->value = $value;

    return $this;
  }

  public function getDescription(): ?string
  {
    return $this->description;
  }

  public function setDescription(?string $description): static
  {
    $this->description = $description;

    return $this;
  }
}

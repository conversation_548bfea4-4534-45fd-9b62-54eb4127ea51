<?php

namespace App\Dto\Traits;

use OpenApi\Attributes as OA;

trait TimestampTrait
{
  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?\DateTimeImmutable $createdAt = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?\DateTimeImmutable $updatedAt = null;

  public function getCreatedAt(): ?\DateTimeImmutable
  {
    return $this->createdAt;
  }

  public function setCreatedAt(\DateTimeImmutable $createdAt): static
  {
    $this->createdAt = $createdAt;

    return $this;
  }

  public function getUpdatedAt(): ?\DateTimeImmutable
  {
    return $this->updatedAt;
  }

  public function setUpdatedAt(?\DateTimeImmutable $updatedAt): static
  {
    $this->updatedAt = $updatedAt;

    return $this;
  }
}

<?php

namespace App\Dto\Traits;

use OpenApi\Attributes as OA;

trait DescriptionTrait
{
  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?string $description = null;

  public function getDescription(): ?string
  {
    return $this->description;
  }

  public function setDescription(string $description): static
  {
    $this->description = $description;

    return $this;
  }
}

<?php


namespace App\Dto;


use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class ErrorResponse extends JsonResponse
{

  public function __construct(\Exception $error, int $status = Response::HTTP_INTERNAL_SERVER_ERROR, array $headers = [])
  {
    $ResponseDto = (new ResponseDtoBuilder())
      ->withStatus($status)
      ->withMessage($error->getMessage())
      ->build();

    parent::__construct($ResponseDto->toArray(), $status, $headers);
  }
}

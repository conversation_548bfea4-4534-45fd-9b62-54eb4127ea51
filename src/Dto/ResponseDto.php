<?php

namespace App\Dto;

class ResponseDto
{
  /**
   * @var integer
   */
  private $status;

  /**
   * @var string|null
   */
  private $message;

  /**
   * @var array|null
   */
  private $data;

  /**
   * @var ErrorDetailDto|null
   */
  private $errorDetail;

  /**
   * ResponseDTO constructor.
   * @param int $status
   * @param mixed $data
   * @param string $message
   * @param ErrorDetailDto $errorDetail
   */
  public function __construct(int $status, $data = array(), string $message = '', ErrorDetailDto $errorDetail = null)
  {
    $this->status = $status;
    $this->data = $data;
    $this->message = $message;
    $this->errorDetail = $errorDetail;
  }

  /**
   * @return ResponseDtoBuilder
   */
  public static function builder(): ResponseDtoBuilder
  {
    return new ResponseDtoBuilder();
  }

  public function toArray(): array
  {
    $arrayResponse = array();
    $arrayResponse['status'] = $this->status;
    if ($this->message !== '') {
      $arrayResponse['message'] = $this->message;
    }
    if ($this->message === 'SUCCESS') {
      $arrayResponse['data'] = $this->data;
    }
    if ($this->errorDetail !== null) {
      $arrayResponse['error'] = $this->errorDetail->toArray();
    }
    return $arrayResponse;
  }
}

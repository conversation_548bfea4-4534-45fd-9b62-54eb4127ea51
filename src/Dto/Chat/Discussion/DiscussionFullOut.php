<?php

namespace App\Dto\Chat\Out\Discussion;

use App\Dto\Chat\Out\DiscussionFullOut\DiscussionMemberFullOut;
use App\Dto\Chat\Out\DiscussionMessage\DiscussionMessageFullOut;
use App\Dto\Traits\DescriptionTrait;
use App\Dto\Traits\IDTrait;
use App\Dto\Traits\TimestampTrait;
use App\Dto\Traits\UuidTrait;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use OpenApi\Attributes as OA;

class DiscussionFullOut
{
  use IDTrait, UuidTrait, DescriptionTrait, TimestampTrait;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?string $title = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?string $status = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?DiscussionMessageFullOut $lastMessage = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private Collection $members;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private Collection $messages;

  public function __construct()
  {
    $this->members = new ArrayCollection();
    $this->messages = new ArrayCollection();
  }


  public function getLastMessage(): ?DiscussionMessageFullOut
  {
    return $this->lastMessage;
  }

  public function setLastMessage(?DiscussionMessageFullOut $lastMessage): static
  {
    $this->lastMessage = $lastMessage;

    return $this;
  }

  public function getTitle(): ?string
  {
    return $this->title;
  }

  public function setTitle(string $title): static
  {
    $this->title = $title;

    return $this;
  }

  public function getStatus(): ?string
  {
    return $this->status;
  }

  public function setStatus(string $status): static
  {
    $this->status = $status;

    return $this;
  }

  /**
   * @return Collection<int, DiscussionMemberFullOut>
   */
  public function getMembers(): Collection
  {
    return $this->members;
  }

  public function addMember(DiscussionMemberFullOut $member): static
  {
    if (!$this->members->contains($member)) {
      $this->members->add($member);
    }

    return $this;
  }

  public function removeMember(DiscussionMemberFullOut $member): static
  {
    $this->members->removeElement($member);

    return $this;
  }


  /**
   * @return Collection<int, DiscussionMessageFullOut>
   */
  public function getMessages(): Collection
  {
    return $this->messages;
  }

  public function addMessage(DiscussionMessageFullOut $message): static
  {
    if (!$this->messages->contains($message)) {
      $this->messages->add($message);
    }

    return $this;
  }

  public function removeMessage(DiscussionMessageFullOut $message): static
  {
    $this->messages->removeElement($message);

    return $this;
  }
}

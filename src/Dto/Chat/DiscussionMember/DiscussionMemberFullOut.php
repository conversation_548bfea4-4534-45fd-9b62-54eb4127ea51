<?php

namespace App\Dto\Chat\Out\DiscussionFullOut;

use App\Dto\Chat\Out\DiscussionMessage\DiscussionMessageFullOut;
use App\Dto\Security\Out\User\UserTinyOut;
use App\Dto\Traits\IDTrait;
use App\Dto\Traits\TimestampTrait;
use App\Dto\Traits\UuidTrait;
use OpenApi\Attributes as OA;

class DiscussionMemberFullOut
{
  use IDTrait, UuidTrait, TimestampTrait;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?string $status = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?UserTinyOut $user = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?DiscussionMessageFullOut $lastReadMessage = null;

  public function getStatus(): ?string
  {
    return $this->status;
  }

  public function setStatus(string $status): static
  {
    $this->status = $status;

    return $this;
  }

  public function getUser(): ?UserTinyOut
  {
    return $this->user;
  }

  public function setUser(?UserTinyOut $user): static
  {
    $this->user = $user;

    return $this;
  }

  public function getLastReadMessage(): ?DiscussionMessageFullOut
  {
    return $this->lastReadMessage;
  }

  public function setLastReadMessage(?DiscussionMessageFullOut $lastReadMessage): static
  {
    $this->lastReadMessage = $lastReadMessage;

    return $this;
  }
}

<?php

namespace App\Dto\Security\In\Address;

use Symfony\Component\Validator\Constraints as Assert;

class AddressCreateIn
{
  private ?string $website = null;

  #[Assert\Email(message: "ADDRESS_EMAIL_NOT_VALID")]
  private ?string $email = null;

  private ?string $phoneNumber = null;

  private ?string $streetAddress = null;

  private ?string $city = null;

  private ?int $zipCode = null;

  private ?string $country = null;

  private ?string $longitude = null;

  private ?string $latitude = null;

  public function getWebsite(): ?string
  {
    return $this->website;
  }

  public function setWebsite(?string $website): static
  {
    $this->website = $website;

    return $this;
  }

  public function getEmail(): ?string
  {
    return $this->email;
  }

  public function setEmail(?string $email): static
  {
    $this->email = $email;

    return $this;
  }


  public function getPhoneNumber(): ?string
  {
    return $this->phoneNumber;
  }

  public function setPhoneNumber(?string $phoneNumber): static
  {
    $this->phoneNumber = $phoneNumber;

    return $this;
  }

  public function getStreetAddress(): ?string
  {
    return $this->streetAddress;
  }

  public function setStreetAddress(?string $streetAddress): static
  {
    $this->streetAddress = $streetAddress;

    return $this;
  }

  public function getCity(): ?string
  {
    return $this->city;
  }

  public function setCity(?string $city): static
  {
    $this->city = $city;

    return $this;
  }

  public function getZipCode(): ?int
  {
    return $this->zipCode;
  }

  public function setZipCode(?int $zipCode): static
  {
    $this->zipCode = $zipCode;

    return $this;
  }

  public function getCountry(): ?string
  {
    return $this->country;
  }

  public function setCountry(?string $country): static
  {
    $this->country = $country;

    return $this;
  }

  public function getLongitude(): ?string
  {
    return $this->longitude;
  }

  public function setLongitude(?string $longitude): static
  {
    $this->longitude = $longitude;

    return $this;
  }

  public function getLatitude(): ?string
  {
    return $this->latitude;
  }

  public function setLatitude(?string $latitude): static
  {
    $this->latitude = $latitude;

    return $this;
  }
}

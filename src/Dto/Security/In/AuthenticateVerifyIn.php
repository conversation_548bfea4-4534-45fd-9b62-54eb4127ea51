<?php

namespace App\Dto\Security\In;

use Symfony\Component\Validator\Constraints as Assert;

class AuthenticateVerifyIn
{

  #[Assert\NotNull]
  #[Assert\NotBlank]
  private ?string $login = null;

  #[Assert\NotNull]
  #[Assert\NotBlank]
  private ?string $code = null;


  public function getLogin(): ?string
  {
    return $this->login;
  }

  public function setLogin(?string $login): static
  {
    $this->login = $login;

    return $this;
  }

  public function getCode(): ?string
  {
    return $this->code;
  }

  public function setCode(?string $code): static
  {
    $this->code = $code;

    return $this;
  }
}

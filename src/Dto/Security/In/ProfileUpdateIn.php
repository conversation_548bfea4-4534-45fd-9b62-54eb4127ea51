<?php

namespace App\Dto\Security\In;

use Symfony\Component\Validator\Constraints as Assert;
use OpenApi\Attributes as OA;

class ProfileUpdateIn
{
  #[Assert\NotNull]
  #[Assert\NotBlank]
  #[OA\Property(description: 'The unique identifier of the role.')]
  private ?string $firstName = null;

  #[OA\Property(description: 'The unique identifier of the role.')]
  private ?string $lastName = null;

  #[OA\Property(description: 'The unique identifier of the role.')]
  private ?string $loginType = null;

  #[OA\Property(description: 'The unique identifier of the role.')]
  private ?string $email = null;

  #[OA\Property(description: 'The unique identifier of the role.')]
  private ?string $phoneNumber = null;
  #[OA\Property(description: 'The unique identifier of the role.')]
  private ?bool $active = null;

  #[OA\Property(description: 'The unique identifier of the role.')]
  private ?\DateTimeInterface $birthDate = null;

  public function getBirthDate(): ?\DateTimeInterface
  {
    return $this->birthDate;
  }

  public function setBirthDate(?\DateTimeInterface $birthDate): static
  {
    $this->birthDate = $birthDate;

    return $this;
  }

  public function getPhoneNumber(): ?string
  {
    return $this->phoneNumber;
  }

  public function setPhoneNumber(?string $phoneNumber): static
  {
    $this->phoneNumber = $phoneNumber;

    return $this;
  }

  public function getEmail(): ?string
  {
    return $this->email;
  }

  public function setEmail(?string $email): static
  {
    $this->email = $email;

    return $this;
  }

  public function getLoginType(): ?string
  {
    return $this->loginType;
  }

  public function setLoginType(?string $loginType): static
  {
    $this->loginType = $loginType;

    return $this;
  }

  public function getFirstName(): ?string
  {
    return $this->firstName;
  }

  public function setFirstName(?string $firstName): static
  {
    $this->firstName = $firstName;

    return $this;
  }

  public function getLastName(): ?string
  {
    return $this->lastName;
  }

  public function setLastName(?string $lastName): static
  {
    $this->lastName = $lastName;

    return $this;
  }

  /**
   * Get the value of active
   */ 
  public function getActive():bool
  {
    return $this->active;
  }

  /**
   * Set the value of active
   *
   * @return  self
   */ 
  public function setActive($active)
  {
    $this->active = $active;

    return $this;
  }
}

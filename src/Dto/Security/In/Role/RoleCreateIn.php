<?php

namespace App\Dto\Security\In\Role;

use Symfony\Component\Validator\Constraints as Assert;
use OpenApi\Attributes as OA;

class RoleCreateIn
{
  #[Assert\NotNull]
  #[Assert\NotBlank]
  #[OA\Property(description: 'The unique identifier of the role.')]
  private ?string $code = null;

  #[OA\Property(description: 'The unique identifier of the role.')]
  private ?string $description = null;

  public function getCode(): ?string
  {
    return $this->code;
  }

  public function setCode(?string $code): static
  {
    $this->code = $code;

    return $this;
  }

  public function getDescription(): ?string
  {
    return $this->description;
  }

  public function setDescription(?string $description): static
  {
    $this->description = $description;

    return $this;
  }
}

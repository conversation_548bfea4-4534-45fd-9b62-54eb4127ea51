<?php

namespace App\Dto\Security\In\Role;

use OpenApi\Attributes as OA;

class RoleReplaceOrUpdateIn
{
  #[OA\Property(description: 'The unique identifier of the role.')]
  private ?string $description = null;

  public function getDescription(): ?string
  {
    return $this->description;
  }

  public function setDescription(?string $description): static
  {
    $this->description = $description;

    return $this;
  }
}

<?php

namespace App\Dto\Security\In;

use App\Enum\LoginTypes;
use OpenApi\Attributes as OA;
use App\Validator as BusinessAssert;
use Symfony\Component\Validator\Constraints as Assert;

#[BusinessAssert\IsValidLogin]
class AuthenticateIn
{
  #[Assert\NotNull]
  #[Assert\NotBlank]
  #[OA\Property(description: 'The unique identifier of the role.')]
  private ?string $login = null;

  #[Assert\NotNull]
  #[Assert\NotBlank]
  #[OA\Property(description: 'The unique identifier of the role.')]
  private ?string $type = null;

  public function getLogin(): ?string
  {
    return $this->login;
  }

  public function setLogin(?string $login): static
  {
    $this->login = $login;

    return $this;
  }

  public function getType(): ?string
  {
    return $this->type;
  }

  public function setType(?string $type): static
  {
    $this->type = $type;

    return $this;
  }

  public function getPhoneNumber(): ?string
  {
    if ($this->isPhoneType()) {
      return $this->login;
    }
    return null;
  }

  public function getEmail(): ?string
  {
    if ($this->isEmailType()) {
      return $this->login;
    }
    return null;
  }

  public function isEmailType(): bool
  {
    return $this->type === LoginTypes::EMAIL->value;
  }

  public function isPhoneType(): bool
  {
    return $this->type === LoginTypes::PHONE->value;
  }
}

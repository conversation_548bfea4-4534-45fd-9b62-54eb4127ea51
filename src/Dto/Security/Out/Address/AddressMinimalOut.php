<?php

namespace App\Dto\Security\Out\Address;

use OpenApi\Attributes as OA;

class AddressMinimalOut extends AddressTinyOut
{
  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?string $country = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?string $city = null;

  public function getCountry(): ?string
  {
    return $this->country;
  }

  public function setCountry(?string $country): static
  {
    $this->country = $country;

    return $this;
  }

  public function getCity(): ?string
  {
    return $this->city;
  }

  public function setCity(?string $city): static
  {
    $this->city = $city;

    return $this;
  }
}

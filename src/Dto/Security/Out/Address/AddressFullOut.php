<?php

namespace App\Dto\Security\Out\Address;

use OpenApi\Attributes as OA;

class AddressFullOut extends AddressStandardOut
{
  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?string $email = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?string $phoneNumber = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?string $longitude = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?string $latitude = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?\DateTimeImmutable $createdAt = null;


  public function getEmail(): ?string
  {
    return $this->email;
  }

  public function setEmail(?string $email): static
  {
    $this->email = $email;

    return $this;
  }


  public function getPhoneNumber(): ?string
  {
    return $this->phoneNumber;
  }

  public function setPhoneNumber(?string $phoneNumber): static
  {
    $this->phoneNumber = $phoneNumber;

    return $this;
  }


  public function getLongitude(): ?string
  {
    return $this->longitude;
  }

  public function setLongitude(?string $longitude): static
  {
    $this->longitude = $longitude;

    return $this;
  }

  public function getLatitude(): ?string
  {
    return $this->latitude;
  }

  public function setLatitude(?string $latitude): static
  {
    $this->latitude = $latitude;

    return $this;
  }

  public function getCreatedAt(): ?\DateTimeImmutable
  {
    return $this->createdAt;
  }

  public function setCreatedAt(\DateTimeImmutable $createdAt): static
  {
    $this->createdAt = $createdAt;

    return $this;
  }
}

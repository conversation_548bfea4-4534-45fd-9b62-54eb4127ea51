<?php

namespace App\Dto\Security\Out\Address;

use OpenApi\Attributes as OA;

class AddressTinyOut
{
  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?string $streetAddress = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?int $id = null;

  public function getId(): ?int
  {
    return $this->id;
  }

  public function setId(?int $id): static
  {
    $this->id = $id;

    return $this;
  }

  public function getStreetAddress(): ?string
  {
    return $this->streetAddress;
  }

  public function setStreetAddress(?string $streetAddress): static
  {
    $this->streetAddress = $streetAddress;

    return $this;
  }
}

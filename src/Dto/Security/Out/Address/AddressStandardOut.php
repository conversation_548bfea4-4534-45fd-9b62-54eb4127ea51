<?php

namespace App\Dto\Security\Out\Address;

use OpenApi\Attributes as OA;

class AddressStandardOut extends AddressMinimalOut
{
  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?int $zipCode = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?\DateTimeImmutable $updatedAt = null;

  public function getZipCode(): ?int
  {
    return $this->zipCode;
  }

  public function setZipCode(?int $zipCode): static
  {
    $this->zipCode = $zipCode;

    return $this;
  }

  public function getUpdatedAt(): ?\DateTimeImmutable
  {
    return $this->updatedAt;
  }

  public function setUpdatedAt(?\DateTimeImmutable $updatedAt): static
  {
    $this->updatedAt = $updatedAt;

    return $this;
  }
}

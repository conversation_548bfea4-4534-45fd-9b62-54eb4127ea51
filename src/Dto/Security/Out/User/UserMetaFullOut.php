<?php

namespace App\Dto\Security\Out\User;

use OpenApi\Attributes as OA;

class UserMetaFullOut
{
  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?string $name = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?string $value = null;


  public function getName(): ?string
  {
    return $this->name;
  }

  public function setName(string $name): static
  {
    $this->name = $name;

    return $this;
  }

  public function getValue(): ?string
  {
    return $this->value;
  }

  public function setValue(?string $value): static
  {
    $this->value = $value;

    return $this;
  }
}

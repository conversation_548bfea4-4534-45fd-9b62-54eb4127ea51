<?php

namespace App\Dto\Security\Out\User;

use OpenApi\Attributes as OA;

class UserTinyOut
{
  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?string $uuid = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?string $displayName = null;

  public function getUuid(): ?string
  {
    return $this->uuid;
  }

  public function setUuid(?string $uuid): static
  {
    $this->uuid = $uuid;

    return $this;
  }

  public function getDisplayName(): ?string
  {
    return $this->displayName;
  }

  public function setDisplayName(?string $displayName): static
  {
    $this->displayName = $displayName;

    return $this;
  }
}

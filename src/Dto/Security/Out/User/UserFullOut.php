<?php

namespace App\Dto\Security\Out\User;

use OpenApi\Attributes as OA;

class UserFullOut extends UserStandardOut
{
  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?int $weight = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?int $height = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?bool $emailVerified = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?bool $phoneNumberVerified = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?string $username = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private array $metas;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private array $addresses;

  public function getMetas(): ?array
  {
    return $this->metas;
  }

  public function setMetas(?array $metas): static
  {
    $this->metas = $metas;

    return $this;
  }

  public function getAddresses(): ?array
  {
    return $this->addresses;
  }

  public function setAddresses(?array $addresses): static
  {
    $this->addresses = $addresses;

    return $this;
  }

  public function getWeight(): ?string
  {
    return $this->weight;
  }

  public function setWeight(?int $weight): static
  {
    $this->weight = $weight;

    return $this;
  }

  public function getHeight(): ?int
  {
    return $this->height;
  }

  public function setHeight(?int $height): static
  {
    $this->height = $height;

    return $this;
  }

  public function isEmailVerified(): ?bool
  {
    return $this->emailVerified;
  }

  public function setEmailVerified(bool $emailVerified): static
  {
    $this->emailVerified = $emailVerified;

    return $this;
  }

  public function isPhoneNumberVerified(): ?bool
  {
    return $this->phoneNumberVerified;
  }

  public function setPhoneNumberVerified(bool $phoneNumberVerified): static
  {
    $this->phoneNumberVerified = $phoneNumberVerified;

    return $this;
  }

  public function getUsername(): ?string
  {
    return $this->username;
  }

  public function setUsername(?string $username): static
  {
    $this->username = $username;

    return $this;
  }
}

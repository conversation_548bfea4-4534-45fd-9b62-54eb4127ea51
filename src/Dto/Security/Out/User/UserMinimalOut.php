<?php

namespace App\Dto\Security\Out\User;

use OpenApi\Attributes as OA;

class UserMinimalOut extends UserTinyOut
{
  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?int $id = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?string $phoneNumber = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?string $email = null;

  public function getId(): ?int
  {
    return $this->id;
  }

  public function setId(?int $id): static
  {
    $this->id = $id;

    return $this;
  }

  public function getPhoneNumber(): ?string
  {
    return $this->phoneNumber;
  }

  public function setPhoneNumber(?string $phoneNumber): static
  {
    $this->phoneNumber = $phoneNumber;

    return $this;
  }

  public function getEmail(): ?string
  {
    return $this->email;
  }

  public function setEmail(?string $email): static
  {
    $this->email = $email;

    return $this;
  }
}

<?php

namespace App\Dto\Security\Out\User;

use OpenApi\Attributes as OA;

class UserStandardOut extends UserMinimalOut
{
  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?string $firstName = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?string $lastName = null;


  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?string $gender = null;

  public function getFirstName(): ?string
  {
    return $this->firstName;
  }

  public function setFirstName(?string $firstName): static
  {
    $this->firstName = $firstName;

    return $this;
  }

  public function getLastName(): ?string
  {
    return $this->lastName;
  }

  public function setLastName(?string $lastName): static
  {
    $this->lastName = $lastName;

    return $this;
  }

  public function getGender(): ?string
  {
    return $this->gender;
  }

  public function setGender(?string $gender): static
  {
    $this->gender = $gender;

    return $this;
  }
}

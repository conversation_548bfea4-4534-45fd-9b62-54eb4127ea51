<?php

namespace App\Dto\Security\Out\Role;

use OpenApi\Attributes as OA;

class RoleFullOut extends RoleStandardOut
{
  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?\DateTimeImmutable $createdAt = null;

  public function getCreatedAt(): ?\DateTimeImmutable
  {
    return $this->createdAt;
  }

  public function setCreatedAt(\DateTimeImmutable $createdAt): static
  {
    $this->createdAt = $createdAt;

    return $this;
  }
}

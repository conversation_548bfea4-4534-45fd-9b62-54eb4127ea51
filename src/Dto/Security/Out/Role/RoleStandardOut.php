<?php

namespace App\Dto\Security\Out\Role;

use OpenApi\Attributes as OA;

class RoleStandardOut extends RoleMinimalOut
{
  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?string $description = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?\DateTimeImmutable $updatedAt = null;

  public function getDescription(): ?string
  {
    return $this->description;
  }

  public function setDescription(?string $description): static
  {
    $this->description = $description;

    return $this;
  }

  public function getUpdatedAt(): ?\DateTimeImmutable
  {
    return $this->updatedAt;
  }

  public function setUpdatedAt(?\DateTimeImmutable $updatedAt): static
  {
    $this->updatedAt = $updatedAt;

    return $this;
  }
}

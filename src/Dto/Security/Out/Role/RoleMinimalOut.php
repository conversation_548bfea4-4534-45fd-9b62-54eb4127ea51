<?php

namespace App\Dto\Security\Out\Role;

use OpenApi\Attributes as OA;

class RoleMinimalOut extends RoleTinyOut
{
  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?int $id = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?string $uuid = null;

  public function getId(): ?int
  {
    return $this->id;
  }

  public function setId(?int $id): static
  {
    $this->id = $id;

    return $this;
  }

  public function getUuid(): ?string
  {
    return $this->uuid;
  }

  public function setUuid(?string $uuid): static
  {
    $this->uuid = $uuid;

    return $this;
  }
}

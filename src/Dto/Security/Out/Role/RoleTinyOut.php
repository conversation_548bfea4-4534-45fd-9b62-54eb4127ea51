<?php

namespace App\Dto\Security\Out\Role;

use OpenApi\Attributes as OA;

class RoleTinyOut
{
  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?string $uuid = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  protected ?string $code = null;

  public function getCode(): ?string
  {
    return $this->code;
  }

  public function setCode(?string $code): static
  {
    $this->code = $code;

    return $this;
  }

  public function getUuid(): ?string
  {
    return $this->uuid;
  }

  public function setUuid(?string $uuid): static
  {
    $this->uuid = $uuid;

    return $this;
  }
}

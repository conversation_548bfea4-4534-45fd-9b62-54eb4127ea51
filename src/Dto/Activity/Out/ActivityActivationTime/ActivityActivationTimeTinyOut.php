<?php

namespace App\Dto\Activity\Out\ActivityActivationTime;

use App\Dto\Activity\Out\ActivityActivationTimeType\ActivityActivationTimeTypeTinyOut;
use App\Dto\Shared\Out\ActiveDayType\ActiveDayTypeTinyOut;
use App\Dto\Traits\IDTrait;
use App\Dto\Traits\TimestampTrait;
use App\Dto\Traits\UuidTrait;
use App\Entity\Traits\DeActivateTrait;
use OpenApi\Attributes as OA;

class ActivityActivationTimeTinyOut
{
  use IDTrait, UuidTrait, DeActivateTrait, TimestampTrait;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?\DateTimeInterface $activationStartDateTime = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?\DateTimeInterface $activationEndDateTime = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?\DateTimeInterface $startDateTime = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?\DateTimeInterface $endDateTime = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?ActiveDayTypeTinyOut $activeDayType = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?ActivityActivationTimeTypeTinyOut $activationTimeType = null;


  public function getActivationStartDateTime(): ?\DateTimeInterface
  {
    return $this->activationStartDateTime;
  }

  public function setActivationStartDateTime(?\DateTimeInterface $activationStartDateTime): static
  {
    $this->activationStartDateTime = $activationStartDateTime;

    return $this;
  }

  public function getActivationEndDateTime(): ?\DateTimeInterface
  {
    return $this->activationEndDateTime;
  }

  public function setActivationEndDateTime(?\DateTimeInterface $activationEndDateTime): static
  {
    $this->activationEndDateTime = $activationEndDateTime;

    return $this;
  }

  public function getStartDateTime(): ?\DateTimeInterface
  {
    return $this->startDateTime;
  }

  public function setStartDateTime(\DateTimeInterface $startDateTime): static
  {
    $this->startDateTime = $startDateTime;

    return $this;
  }

  public function getEndDateTime(): ?\DateTimeInterface
  {
    return $this->endDateTime;
  }

  public function setEndDateTime(?\DateTimeInterface $endDateTime): static
  {
    $this->endDateTime = $endDateTime;

    return $this;
  }

  public function getActiveDayTypeTinyOut(): ?ActiveDayTypeTinyOut
  {
    return $this->activeDayType;
  }

  public function setActiveDayTypeTinyOut(?ActiveDayTypeTinyOut $activeDayType): static
  {
    $this->activeDayType = $activeDayType;

    return $this;
  }

  public function getActivationTimeType(): ?ActivityActivationTimeTypeTinyOut
  {
    return $this->activationTimeType;
  }

  public function setActivationTimeType(?ActivityActivationTimeTypeTinyOut $activationTimeType): static
  {
    $this->activationTimeType = $activationTimeType;

    return $this;
  }
}

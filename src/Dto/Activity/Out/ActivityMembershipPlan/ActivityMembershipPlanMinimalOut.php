<?php

namespace App\Dto\Activity\Out\ActivityMembershipPlan;

use App\Dto\Traits\CodeTrait;
use App\Dto\Traits\DescriptionTrait;
use App\Dto\Traits\IDTrait;
use App\Dto\Traits\TimestampTrait;
use App\Dto\Traits\UuidTrait;
use App\Entity\Traits\DeActivateTrait;
use OpenApi\Attributes as OA;

class ActivityMembershipPlanMinimalOut
{
  use IDTrait, UuidTrait, CodeTrait, DescriptionTrait, DeActivateTrait, TimestampTrait;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?int $duration = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?string $durationType = null;

  public function getDuration(): ?int
  {
    return $this->duration;
  }

  public function setDuration(int $duration): static
  {
    $this->duration = $duration;

    return $this;
  }

  public function getDurationType(): ?string
  {
    return $this->durationType;
  }

  public function setDurationType(string $durationType): static
  {
    $this->durationType = $durationType;

    return $this;
  }
}

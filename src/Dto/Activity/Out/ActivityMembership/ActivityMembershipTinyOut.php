<?php

namespace App\Dto\Activity\Out\ActivityMembership;

use App\Dto\Activity\Out\ActivityMembershipPlan\ActivityMembershipPlanFullOut;
use App\Dto\Traits\CodeTrait;
use App\Dto\Traits\DescriptionTrait;
use App\Dto\Traits\IDTrait;
use App\Dto\Traits\TimestampTrait;
use App\Dto\Traits\UuidTrait;
use App\Entity\Traits\DeActivateTrait;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use OpenApi\Attributes as OA;

class ActivityMembershipTinyOut
{
  use IDTrait, UuidTrait, CodeTrait, DescriptionTrait, DeActivateTrait, TimestampTrait;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private Collection $plans;

  public function __construct()
  {
    $this->plans = new ArrayCollection();
  }

  /**
   * @return Collection<int, ActivityMembershipPlanFullOut>
   */
  public function getPlans(): Collection
  {
    return $this->plans;
  }

  public function addPlan(ActivityMembershipPlanFullOut $plan): static
  {
    if (!$this->plans->contains($plan)) {
      $this->plans->add($plan);
    }

    return $this;
  }

  public function removePlan(ActivityMembershipPlanFullOut $plan): static
  {
    $this->plans->removeElement($plan);

    return $this;
  }
}

<?php

namespace App\Dto\Activity\Out\Activity;

use App\Dto\Activity\Out\ActivityActivationTime\ActivityActivationTimeFullOut;
use App\Dto\Activity\Out\ActivityCategory\ActivityCategoryTinyOut;
use App\Dto\Activity\Out\ActivityMembership\ActivityMembershipFullOut;
use App\Dto\Security\Out\User\UserTinyOut;
use App\Dto\Shared\Out\File\FileMinimalOut;
use App\Dto\Traits\IDTrait;
use App\Dto\Traits\TimestampTrait;
use App\Dto\Traits\UuidTrait;
use App\Entity\Traits\DeActivateTrait;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use OpenApi\Attributes as OA;

class ActivityTinyOut
{
  use IDTrait, DeActivateTrait, UuidTrait, TimestampTrait;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?string $title = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?string $shortDescription = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?string $longDescription = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?string $placeName = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?string $streetName = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?string $city = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?int $zipCode = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?string $longitude = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?string $latitude = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?string $country = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?string $price = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?UserTinyOut $owner = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?UserTinyOut $creator = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?string $status = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private Collection $partners;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private Collection $files;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private Collection $activationTimes;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?ActivityCategoryTinyOut $category = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private Collection $memberships;


  public function __construct()
  {
    $this->files = new ArrayCollection();
    $this->partners = new ArrayCollection();
    $this->activationTimes = new ArrayCollection();
    $this->memberships = new ArrayCollection();
  }

  public function getTitle(): ?string
  {
    return $this->title;
  }

  public function setTitle(string $title): static
  {
    $this->title = $title;

    return $this;
  }

  public function getShortDescription(): ?string
  {
    return $this->shortDescription;
  }

  public function setShortDescription(?string $shortDescription): static
  {
    $this->shortDescription = $shortDescription;

    return $this;
  }

  public function getLongDescription(): ?string
  {
    return $this->longDescription;
  }

  public function setLongDescription(?string $longDescription): static
  {
    $this->longDescription = $longDescription;

    return $this;
  }

  public function getPlaceName(): ?string
  {
    return $this->placeName;
  }

  public function setPlaceName(?string $placeName): static
  {
    $this->placeName = $placeName;

    return $this;
  }

  public function getStreetName(): ?string
  {
    return $this->streetName;
  }

  public function setStreetName(?string $streetName): static
  {
    $this->streetName = $streetName;

    return $this;
  }

  public function getCity(): ?string
  {
    return $this->city;
  }

  public function setCity(?string $city): static
  {
    $this->city = $city;

    return $this;
  }

  public function getZipCode(): ?int
  {
    return $this->zipCode;
  }

  public function setZipCode(?int $zipCode): static
  {
    $this->zipCode = $zipCode;

    return $this;
  }

  public function getLongitude(): ?string
  {
    return $this->longitude;
  }

  public function setLongitude(?string $longitude): static
  {
    $this->longitude = $longitude;

    return $this;
  }

  public function getLatitude(): ?string
  {
    return $this->latitude;
  }

  public function setLatitude(?string $latitude): static
  {
    $this->latitude = $latitude;

    return $this;
  }

  public function getCountry(): ?string
  {
    return $this->country;
  }

  public function setCountry(?string $country): static
  {
    $this->country = $country;

    return $this;
  }

  public function getPrice(): ?string
  {
    return $this->price;
  }

  public function setPrice(?string $price): static
  {
    $this->price = $price;

    return $this;
  }

  public function getOwner(): ?UserTinyOut
  {
    return $this->owner;
  }

  public function setOwner(?UserTinyOut $owner): static
  {
    $this->owner = $owner;

    return $this;
  }

  public function getCreator(): ?UserTinyOut
  {
    return $this->creator;
  }

  public function setCreator(?UserTinyOut $creator): static
  {
    $this->creator = $creator;

    return $this;
  }

  public function getStatus(): ?string
  {
    return $this->status;
  }

  public function setStatus(string $status): static
  {
    $this->status = $status;

    return $this;
  }


  /**
   * @return Collection<int, FileMinimalOut>
   */
  public function getFiles(): Collection
  {
    return $this->files;
  }

  public function addFile(FileMinimalOut $file): static
  {
    if (!$this->files->contains($file)) {
      $this->files->add($file);
    }

    return $this;
  }

  public function removeFile(FileMinimalOut $file): static
  {
    $this->files->removeElement($file);

    return $this;
  }

  /**
   * @return Collection<int, UserTinyOut>
   */
  public function getPartners(): Collection
  {
    return $this->partners;
  }

  public function addPartner(UserTinyOut $partner): static
  {
    if (!$this->partners->contains($partner)) {
      $this->partners->add($partner);
    }

    return $this;
  }

  public function removePartner(UserTinyOut $partner): static
  {
    $this->partners->removeElement($partner);

    return $this;
  }

  /**
   * @return Collection<int, ActivityActivationTimeFullOut>
   */
  public function getActivationTimes(): Collection
  {
    return $this->activationTimes;
  }

  public function addActivationTime(ActivityActivationTimeFullOut $activationTime): static
  {
    if (!$this->activationTimes->contains($activationTime)) {
      $this->activationTimes->add($activationTime);
    }

    return $this;
  }

  public function removeActivationTime(ActivityActivationTimeFullOut $activationTime): static
  {
    $this->activationTimes->removeElement($activationTime);

    return $this;
  }

  public function getCategory(): ?ActivityCategoryTinyOut
  {
    return $this->category;
  }

  public function setCategory(?ActivityCategoryTinyOut $category): static
  {
    $this->category = $category;

    return $this;
  }

  /**
   * @return Collection<int, ActivityMembershipFullOut>
   */
  public function getMemberships(): Collection
  {
    return $this->memberships;
  }

  public function addMembership(ActivityMembershipFullOut $membership): static
  {
    if (!$this->memberships->contains($membership)) {
      $this->memberships->add($membership);
    }

    return $this;
  }

  public function removeMembership(ActivityMembershipFullOut $membership): static
  {
    $this->memberships->removeElement($membership);

    return $this;
  }
}

<?php

namespace App\Dto\Activity\Out\ActivityMembershipPlan;

use App\Dto\Activity\Out\ActivityMembership\ActivityMembershipTinyOut;
use App\Dto\Payment\Out\Payment\PaymentFullOut;
use App\Dto\Security\Out\User\UserTinyOut;
use App\Dto\Traits\IDTrait;
use App\Dto\Traits\TimestampTrait;
use App\Dto\Traits\UuidTrait;
use OpenApi\Attributes as OA;

class ActivityReservationFullOut
{
  use IDTrait, UuidTrait, TimestampTrait;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?UserTinyOut $user = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?string $status = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?\DateTimeInterface $startActivationDate = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?\DateTimeInterface $endActivationDate = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?bool $autoRenew = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?PaymentFullOut $payment = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?ActivityMembershipTinyOut $membership = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?ActivityMembershipPlanTinyOut $plan = null;

  public function getId(): ?int
  {
    return $this->id;
  }

  public function getUser(): ?UserTinyOut
  {
    return $this->user;
  }

  public function setUser(?UserTinyOut $user): static
  {
    $this->user = $user;

    return $this;
  }

  public function getStatus(): ?string
  {
    return $this->status;
  }

  public function setStatus(string $status): static
  {
    $this->status = $status;

    return $this;
  }

  public function getStartActivationDate(): ?\DateTimeInterface
  {
    return $this->startActivationDate;
  }

  public function setStartActivationDate(\DateTimeInterface $startActivationDate): static
  {
    $this->startActivationDate = $startActivationDate;

    return $this;
  }

  public function getEndActivationDate(): ?\DateTimeInterface
  {
    return $this->endActivationDate;
  }

  public function setEndActivationDate(?\DateTimeInterface $endActivationDate): static
  {
    $this->endActivationDate = $endActivationDate;

    return $this;
  }

  public function isAutoRenew(): ?bool
  {
    return $this->autoRenew;
  }

  public function setAutoRenew(bool $autoRenew): static
  {
    $this->autoRenew = $autoRenew;

    return $this;
  }

  public function getPaymentFullOut(): ?PaymentFullOut
  {
    return $this->payment;
  }

  public function setPaymentFullOut(?PaymentFullOut $payment): static
  {
    $this->payment = $payment;

    return $this;
  }

  public function getMembership(): ?ActivityMembershipTinyOut
  {
    return $this->membership;
  }

  public function setMembership(?ActivityMembershipTinyOut $membership): static
  {
    $this->membership = $membership;

    return $this;
  }

  public function getPlan(): ?ActivityMembershipPlanTinyOut
  {
    return $this->plan;
  }

  public function setPlan(?ActivityMembershipPlanTinyOut $plan): static
  {
    $this->plan = $plan;

    return $this;
  }
}

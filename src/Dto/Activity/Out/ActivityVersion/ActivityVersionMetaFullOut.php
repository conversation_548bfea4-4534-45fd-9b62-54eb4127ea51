<?php

namespace App\Dto\Activity\Out\ActivityVersion;

use App\Dto\Traits\IDTrait;
use App\Dto\Traits\TimestampTrait;
use OpenApi\Attributes as OA;

class ActivityVersionMetaFullOut
{
  use IDTrait, TimestampTrait;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?string $oldValue = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?string $newValue = null;

  public function getOldValue(): ?string
  {
    return $this->oldValue;
  }

  public function setOldValue(?string $oldValue): static
  {
    $this->oldValue = $oldValue;

    return $this;
  }

  public function getNewValue(): ?string
  {
    return $this->newValue;
  }

  public function setNewValue(?string $newValue): static
  {
    $this->newValue = $newValue;

    return $this;
  }
}

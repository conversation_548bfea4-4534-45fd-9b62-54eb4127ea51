<?php

namespace App\Dto\Activity\Out\ActivityVersion;

use App\Dto\Activity\Out\ActivityVersion\ActivityVersionMetaFullOut;
use App\Dto\Security\Out\User\UserTinyOut;
use App\Dto\Traits\IDTrait;
use App\Dto\Traits\TimestampTrait;
use App\Dto\Traits\UuidTrait;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use OpenApi\Attributes as OA;

class ActivityVersionFullOut
{
  use IDTrait, UuidTrait, TimestampTrait;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?UserTinyOut $editor = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private Collection $metas;

  public function __construct()
  {
    $this->metas = new ArrayCollection();
  }


  public function getEditor(): ?UserTinyOut
  {
    return $this->editor;
  }

  public function setEditor(?UserTinyOut $editor): static
  {
    $this->editor = $editor;

    return $this;
  }

  /**
   * @return Collection<int, ActivityVersionMetaFullOut>
   */
  public function getMetas(): Collection
  {
    return $this->metas;
  }

  public function addMeta(ActivityVersionMetaFullOut $meta): static
  {
    if (!$this->metas->contains($meta)) {
      $this->metas->add($meta);
    }

    return $this;
  }

  public function removeMeta(ActivityVersionMetaFullOut $meta): static
  {
    $this->metas->removeElement($meta);

    return $this;
  }
}

<?php

namespace App\Dto;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class SuccessResponse extends JsonResponse
{
  const SUCCESS = "SUCCESS";

  public function __construct($data = null, int $status = Response::HTTP_OK, array $headers = [], bool $json = false)
  {
    $responseDto = (new ResponseDtoBuilder())->withStatus($status)
      ->withMessage(self::SUCCESS)
      ->withData($data)
      ->build();

    parent::__construct($responseDto->toArray(), $status, $headers, $json);
  }
}

<?php

namespace App\Dto;

use App\Constant\GenericResponseMessageTypes;

class GenericResponseMessage
{
  protected ?string $messageKey = null;

  protected ?string $messageType = null;

  protected ?string $messageContent = null;

  public function __construct($messageContent, $messageKey = "server_error", $messageType = GenericResponseMessageTypes::ERROR)
  {
    $this->messageKey = $messageKey;
    $this->messageType = $messageType;
    $this->messageContent = $messageContent;
  }

  public function getMessageKey()
  {
    return $this->messageKey;
  }

  public function setMessageKey($messageKey): static
  {
    $this->messageKey = $messageKey;

    return $this;
  }

  public function getMessageType()
  {
    return $this->messageType;
  }

  public function setMessageType(int $messageType): static
  {
    $this->messageType = $messageType;

    return $this;
  }

  public function getMessageContent()
  {
    return $this->messageContent;
  }

  public function setMessageContent(string $messageContent): static
  {
    $this->messageContent = $messageContent;

    return $this;
  }

  public function toArray(): array
  {
    return [
      "messageKey" => $this->messageKey,
      "messageType" => $this->messageType,
      "messageContent" => $this->messageContent
    ];
  }
}

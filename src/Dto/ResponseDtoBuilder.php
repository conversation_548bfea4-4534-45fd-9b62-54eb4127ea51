<?php

namespace App\Dto;


class ResponseDtoBuilder
{

  private $status = 500;
  private $data = array();
  private $message = '';
  private $errorDetail = null;

  /**
   * @param string $status
   * @return ResponseDtoBuilder
   */
  public function withStatus($status): ResponseDtoBuilder
  {
    $this->status = $status;
    return $this;
  }


  /**
   * @param string $message
   * @return ResponseDtoBuilder
   */
  public function withMessage($message): ResponseDtoBuilder
  {
    $this->message = $message;
    return $this;
  }


  /**
   * @param mixed $data
   * @return ResponseDtoBuilder
   */
  public function withData($data): ResponseDtoBuilder
  {
    $this->data = $data;
    return $this;
  }

  /**
   * @param ErrorDetailDto $errorDetail
   * @return ResponseDtoBuilder
   */
  public function withError(ErrorDetailDto $errorDetail): ResponseDtoBuilder
  {
    $this->errorDetail = $errorDetail;
    return $this;
  }

  /**
   * @return ResponseDto
   */
  public function build(): ResponseDto
  {
    return new ResponseDto(
      $this->status,
      $this->data,
      $this->message,
      $this->errorDetail
    );
  }
}

<?php

namespace App\Dto;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;

/**
 * @template T
 */
class GenericResponse
{
  /** @var T */
  private $data = null;

  private ?int $code = null;

  private ?string $status = null;

  protected Collection $messages;

  /** @param $data T */
  public function __construct($data, $status, $code, $messages)
  {
    $this->data = $data;
    $this->status = $status;
    $this->code = $code;
    $this->messages = new ArrayCollection($messages);
  }

  /** @return T */
  public function getData()
  {
    return $this->data;
  }

  public function setData($data): static
  {
    $this->data = $data;

    return $this;
  }

  public function getCode()
  {
    return $this->code;
  }

  public function setCode(int $code): static
  {
    $this->code = $code;

    return $this;
  }

  public function getStatus()
  {
    return $this->status;
  }

  public function setStatus(string $status): static
  {
    $this->status = $status;

    return $this;
  }

  /**
   * @return Collection<int, GenericResponseMessage>
   */
  public function getMessages(): Collection
  {
    return $this->messages;
  }

  public function setMessages(Collection $messages): static
  {
    $this->messages = $messages;

    return $this;
  }

  public function addGenericResponseMessage(GenericResponseMessage $message): static
  {
    if (!$this->messages->contains($message)) {
      $this->messages->add($message);
    }

    return $this;
  }

  public function removeGenericResponseMessage(GenericResponseMessage $message): static
  {
    $this->messages->removeElement($message);

    return $this;
  }

  public function toArray(): array
  {
    return [
      "code" => $this->code,
      "status" => $this->status,
      "messages" => array_map(function ($m) {
        return $m->toArray();
      }, $this->messages->toArray()),
      "data" => $this->data
    ];
  }

  public static function formatResponse($content): ?GenericResponse
  {
    $keys = array_keys($content);
    if (in_array("code", $keys) && in_array("message", $keys)) {
      $message = new GenericResponseMessage($content["message"], "auth_error");
      return new GenericResponse(null, "nok", $content["code"], [$message]);
    } else if (in_array("token", $keys)) {
      return new GenericResponse($content, "ok", 200, []);
    }
    return null;
  }
}

<?php

namespace App\Dto\Company\Out\Company;

use App\Dto\Traits\IDTrait;
use App\Dto\Traits\UuidTrait;
use OpenApi\Attributes as OA;
use App\Dto\Traits\TimestampTrait;
use App\Dto\Traits\DescriptionTrait;
use App\Dto\Traits\DeActivateTrait;
use App\Dto\Security\Out\User\UserTinyOut;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\ArrayCollection;
use App\Dto\Company\Out\CompanyMeta\CompanyMetaTinyOut;
use App\Dto\Company\Out\CompanyType\CompanyTypeTinyOut;
use App\Dto\Company\Out\CompanyCategory\CompanyCategoryTinyOut;

class CompanyFullOut
{
  use IDTrait, UuidTrait, DescriptionTrait, DeActivateTrait, TimestampTrait;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private ?string $name = null;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private Collection $users;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private Collection $metas;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private CompanyTypeTinyOut $type;

  #[OA\Property(description: 'The unique identifier of the setting.')]
  private CompanyCategoryTinyOut $category;

  public function __construct()
  {
    $this->users = new ArrayCollection();
    $this->metas = new ArrayCollection();
  }

  public function getName(): ?string
  {
    return $this->name;
  }

  public function setName(?string $name): static
  {
    $this->name = $name;

    return $this;
  }

  /**
   * @return Collection<int, UserTinyOut>
   */
  public function getUsers(): Collection
  {
    return $this->users;
  }

  public function addUser(UserTinyOut $user): static
  {
    if (!$this->users->contains($user)) {
      $this->users->add($user);
    }

    return $this;
  }

  public function removeUser(UserTinyOut $user): static
  {
    $this->users->removeElement($user);

    return $this;
  }


  /**
   * @return Collection<int, CompanyMetaTinyOut>
   */
  public function getMetas(): Collection
  {
    return $this->metas;
  }

  public function addMeta(CompanyMetaTinyOut $meta): static
  {
    if (!$this->metas->contains($meta)) {
      $this->metas->add($meta);
    }

    return $this;
  }

  public function removeMeta(CompanyMetaTinyOut $meta): static
  {
    $this->metas->removeElement($meta);

    return $this;
  }
}

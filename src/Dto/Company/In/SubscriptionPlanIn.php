<?php

namespace App\Dto\Company\In;

use Symfony\Component\Validator\Constraints as Assert;

class SubscriptionPlanIn
{
    #[Assert\NotBlank()]
    #[Assert\Type('string')]
    public string $name;

    #[Assert\NotBlank()]
    #[Assert\Type('float')]
    public float $price;

    #[Assert\NotBlank()]
    #[Assert\Type('string')]
    public string $duration;

    #[Assert\NotBlank()]
    #[Assert\Type('string')]
    public string $description;

    #[Assert\NotBlank()]
    #[Assert\Type('boolean')]
    public bool $active;

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;
        return $this;
    }

    public function getPrice(): float
    {
        return $this->price;
    }

    public function setPrice(float $price): self
    {
        $this->price = $price;
        return $this;
    }

    public function getDuration(): string
    {
        return $this->duration;
    }

    public function setDuration(string $duration): self
    {
        $this->duration = $duration;
        return $this;
    }

    public function getDescription(): string
    {
        return $this->description;
    }

    public function setDescription(string $description): self
    {
        $this->description = $description;
        return $this;
    }

    public function getActive(): bool
    {
        return $this->active;
    }

    public function setActive(bool $active): self
    {
        $this->active = $active;
        return $this;
    }
}
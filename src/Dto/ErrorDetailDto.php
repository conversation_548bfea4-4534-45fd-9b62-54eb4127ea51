<?php

namespace App\Dto;


class ErrorDetailDto
{

  /**
   * @var mixed
   */
  private $code;

  /**
   * @var string
   */
  private $type;

  /**
   * @var array
   */
  private $stack;

  /**
   * ResponseDTO constructor.
   * @param int $code
   * @param string $type
   * @param array $stack
   */
  public function __construct(int $code = 0, string $type = '', array $stack = array())
  {
    $this->code = $code;
    $this->type = $type;
    $this->stack = $stack;
  }

  public function toArray(): array
  {
    $arrayError = array();
    if ($this->code !== 0) {
      $arrayError['code'] = $this->code;
    }
    if ($this->type !== '') {
      $arrayError['type'] = $this->type;
    }
    if (count($this->stack) > 0) {
      $arrayError['stack'] = $this->stack;
    }

    return $arrayError;
  }
}

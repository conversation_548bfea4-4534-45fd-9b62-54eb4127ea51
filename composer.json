{"type": "project", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=8.1", "ext-ctype": "*", "ext-iconv": "*", "aws/aws-sdk-php": "^3.282", "camoo/sms": "^4.0", "doctrine/doctrine-bundle": "^2.10", "doctrine/doctrine-migrations-bundle": "^3.2", "doctrine/orm": "^2.15", "friendsofsymfony/rest-bundle": "^3.5", "giggsey/libphonenumber-for-php": "^8.13", "guzzlehttp/guzzle": "^7.8", "jms/serializer-bundle": "^5.3", "kreait/firebase-bundle": "^5.0", "lexik/jwt-authentication-bundle": "^2.19", "mailgun/mailgun-php": "^3.6", "nelmio/api-doc-bundle": "^4.12", "nelmio/cors-bundle": "^2.3", "nyholm/psr7": "^1.8", "sentry/sentry-symfony": "^5.0", "symfony/asset": "6.3.*", "symfony/console": "6.3.*", "symfony/dotenv": "6.3.*", "symfony/flex": "^2", "symfony/form": "6.3.*", "symfony/framework-bundle": "6.3.*", "symfony/intl": "6.3.*", "symfony/monolog-bundle": "^3.10", "symfony/runtime": "6.3.*", "symfony/twig-bundle": "6.3.*", "symfony/uid": "6.3.*", "symfony/validator": "6.3.*", "symfony/webpack-encore-bundle": "^2.1", "symfony/yaml": "6.3.*"}, "config": {"allow-plugins": {"php-http/discovery": true, "symfony/flex": true, "symfony/runtime": true, "cweagans/composer-patches": true}, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/", "libphonenumber\\": "vendor/giggsey/libphonenumber-for-php/src/", "Mailgun\\": "vendor/mailgun/mailgun-php/src/", "Aws\\": "vendor/aws/aws-sdk-php/src/", "Kreait\\": "vendor/kreait/firebase-php/src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*"}, "scripts": {"start": "symfony serve --port=8011", "seed": "bin/console doctrine:fixtures:load --append", "sql": "bin/console dbal:run-sql --", "patches:generate": "vendor/bin/vendor-patches generate", "auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": true, "require": "6.3.*", "docker": true}}, "require-dev": {"doctrine/doctrine-fixtures-bundle": "^3.4", "phpunit/phpunit": "^9.5", "symfony/browser-kit": "6.3.*", "symfony/css-selector": "6.3.*", "symfony/maker-bundle": "^1.49", "symfony/phpunit-bridge": "^6.3", "symplify/vendor-patches": "^11.2"}}
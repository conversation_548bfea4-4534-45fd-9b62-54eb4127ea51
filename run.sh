if [ -z $1 ]; then
  echo "Error: This script expect at least one argument which is the name of the command !!!"
  exit 1
fi

if [ $1 = "server:start" ] || [ $1 = "s:s" ]; then
  symfony serve --port=8011
  symfony server:log
  exit 0
elif [ $1 = "server:stop" ]; then
  symfony serve:stop
  exit 0
elif [ $1 = "seed" ]; then
  bin/console doctrine:fixtures:load --append
  exit 0
else
  echo "Error: Invalid argument was passed to the script !!!"
  exit 1
fi

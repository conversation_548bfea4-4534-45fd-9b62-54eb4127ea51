# Uncomment version if using an older version of docker compose
# version: "3.8"
x-environment:
  &default-environment
  DATABASE_URL: ******************************************/postgres
  SECRET_KEY: change_me_to_something_random # best to run openssl rand -hex 32
  PORT: 8000
  EMAIL_URL: smtp://mailer:1025 # Example smtp://email:password@smtp_url:port https://glitchtip.com/documentation/install#configuration
  GLITCHTIP_DOMAIN: http://127.0.0.1:8089 # Change this to your domain
  DEFAULT_FROM_EMAIL: <EMAIL> # Change this to your email
  CELERY_WORKER_AUTOSCALE: "1,3"  # Scale between 1 and 3 to prevent excessive memory usage. Change it or remove to set it to the number of cpu cores.
  CELERY_WORKER_MAX_TASKS_PER_CHILD: "10000"

x-depends_on:
  &default-depends_on
  - postgres
  - redis

services:
  postgres:
    image: postgres:16
    environment:
      POSTGRES_HOST_AUTH_METHOD: "trust"  # Consider removing this and setting a password
    restart: unless-stopped
    volumes:
      - pg-data:/var/lib/postgresql/data
  redis:
    image: redis
    restart: unless-stopped
  web:
    image: glitchtip/glitchtip
    depends_on: *default-depends_on
    ports:
      - "8089:8000"
    environment: *default-environment
    restart: unless-stopped
    volumes: 
      - uploads:/code/uploads
  worker:
    image: glitchtip/glitchtip
    command: ./bin/run-celery-with-beat.sh
    depends_on: *default-depends_on
    environment: *default-environment
    restart: unless-stopped
    volumes: 
      - uploads:/code/uploads

  mailer:
    image: schickling/mailcatcher
    ports: [1025, 1080]

  migrate:
    image: glitchtip/glitchtip
    depends_on: *default-depends_on
    command: "./manage.py migrate"
    environment: *default-environment

volumes:
  pg-data:
  uploads:

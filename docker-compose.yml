version: '3'

networks:
    mysql-phpmyadmin:
        name: mysql-phpmyadmin
        # use the bridge driver
        driver: bridge

volumes:
    mysqldata:
        

services:
    mysql:
        image: mysql:8.0
        container_name: mysql
        environment:
            MYSQL_ROOT_PASSWORD: root_password
            MYSQL_DATABASE: sportaabe_docker
            MYSQL_USER: user_name
            MYSQL_PASSWORD: user_password
        volumes:
            - mysqldata:/var/lib/mysql
        networks:
            mysql-phpmyadmin:
                aliases:
                - mysql

    phpmyadmin:
        image: phpmyadmin:5.2.0
        container_name: phpmyadmin
        links:
            - mysql
        environment:
            PMA_HOST: mysql
            PMA_PORT: 3306
        ports:
            - 8081:80
        networks:
            mysql-phpmyadmin:
                aliases:
                - phpmyadmin
    app:
        image: sportaabe
        ports:
          - "8011:80"
        environment:
          DATABASE_URL : mysql://user_name:user_password@mysql:3306/sportaabe_docker?serverVersion=8.0.33&charset=utf8mb4
        networks:
          mysql-phpmyadmin:
              aliases:
                - app
        command: composer install --ignore-platform-reqs

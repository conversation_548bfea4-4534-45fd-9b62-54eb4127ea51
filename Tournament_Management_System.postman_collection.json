{"info": {"_postman_id": "tournament-management-system", "name": "Tournament Management System", "description": "Complete API collection for testing the tournament management system with tournaments, encounters, teams, and player management.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:8000/api/v2", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}, {"key": "tournament_id", "value": "", "type": "string"}, {"key": "encounter_id", "value": "", "type": "string"}, {"key": "team_id", "value": "", "type": "string"}, {"key": "company_id", "value": "", "type": "string"}, {"key": "player_id", "value": "", "type": "string"}, {"key": "category_id", "value": "", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.token) {", "        pm.collectionVariables.set('auth_token', response.data.token);", "        console.log('Auth token set:', response.data.token);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}}, {"name": "Login Company Owner", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.token) {", "        pm.collectionVariables.set('auth_token', response.data.token);", "        console.log('Auth token set:', response.data.token);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}}]}, {"name": "Tournaments", "item": [{"name": "Create Tournament (Admin)", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.tournament && response.data.tournament.uuid) {", "        pm.collectionVariables.set('tournament_id', response.data.tournament.uuid);", "        console.log('Tournament ID set:', response.data.tournament.uuid);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Summer Championship 2024\",\n    \"description\": \"Annual summer football tournament\",\n    \"categoryId\": \"{{category_id}}\",\n    \"startDate\": \"2024-06-01\",\n    \"endDate\": \"2024-06-30\",\n    \"location\": \"Main Stadium Complex\",\n    \"rules\": \"Standard FIFA rules apply. 90 minutes per match.\",\n    \"maxTeams\": 16,\n    \"status\": \"DRAFT\"\n}"}, "url": {"raw": "{{base_url}}/tournaments/create", "host": ["{{base_url}}"], "path": ["tournaments", "create"]}}}, {"name": "Get All Tournaments", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/tournaments/list?status=DRAFT&categoryId={{category_id}}", "host": ["{{base_url}}"], "path": ["tournaments", "list"], "query": [{"key": "status", "value": "DRAFT"}, {"key": "categoryId", "value": "{{category_id}}"}]}}}, {"name": "Get Tournament Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/tournaments/show/{{tournament_id}}", "host": ["{{base_url}}"], "path": ["tournaments", "show", "{{tournament_id}}"]}}}, {"name": "Update Tournament (Admin)", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Summer Championship 2024 - Updated\",\n    \"status\": \"ACTIVE\",\n    \"maxTeams\": 20\n}"}, "url": {"raw": "{{base_url}}/tournaments/update/{{tournament_id}}", "host": ["{{base_url}}"], "path": ["tournaments", "update", "{{tournament_id}}"]}}}, {"name": "Add Company to Tournament (Admin)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"tournamentId\": \"{{tournament_id}}\",\n    \"companyId\": \"{{company_id}}\"\n}"}, "url": {"raw": "{{base_url}}/tournaments/add-company", "host": ["{{base_url}}"], "path": ["tournaments", "add-company"]}}}, {"name": "Get Tournament Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/tournaments/statistics/{{tournament_id}}", "host": ["{{base_url}}"], "path": ["tournaments", "statistics", "{{tournament_id}}"]}}}, {"name": "Get Tournament Standings", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/tournaments/standings/{{tournament_id}}", "host": ["{{base_url}}"], "path": ["tournaments", "standings", "{{tournament_id}}"]}}}]}, {"name": "Team Categories", "item": [{"name": "Create Team Category (Admin)", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.data && response.data.category && response.data.category.uuid) {", "        pm.collectionVariables.set('category_id', response.data.category.uuid);", "        console.log('Category ID set:', response.data.category.uuid);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"U19\",\n    \"code\": \"U19\",\n    \"description\": \"Under 19 category for players 18 years and under\",\n    \"maxAge\": 18,\n    \"minPlayersPerTeam\": 11,\n    \"maxPlayersPerTeam\": 25,\n    \"gender\": \"MIXED\",\n    \"rules\": \"Standard youth rules apply\",\n    \"sortOrder\": 70\n}"}, "url": {"raw": "{{base_url}}/team-categories/create", "host": ["{{base_url}}"], "path": ["team-categories", "create"]}}}, {"name": "Get All Team Categories", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/team-categories/list?includeStats=true", "host": ["{{base_url}}"], "path": ["team-categories", "list"], "query": [{"key": "includeStats", "value": "true"}]}}}, {"name": "Get Team Category Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/team-categories/show/{{category_id}}", "host": ["{{base_url}}"], "path": ["team-categories", "show", "{{category_id}}"]}}}, {"name": "Update Team Category (Admin)", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"description\": \"Updated description for U19 category\",\n    \"maxAge\": 19,\n    \"rules\": \"Updated rules for U19 category\"\n}"}, "url": {"raw": "{{base_url}}/team-categories/update/{{category_id}}", "host": ["{{base_url}}"], "path": ["team-categories", "update", "{{category_id}}"]}}}, {"name": "Get Categories by Gender", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/team-categories/gender/MIXED", "host": ["{{base_url}}"], "path": ["team-categories", "gender", "MIXED"]}}}, {"name": "Get Categories for Age", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/team-categories/age/15", "host": ["{{base_url}}"], "path": ["team-categories", "age", "15"]}}}, {"name": "Get Categories with Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/team-categories/stats", "host": ["{{base_url}}"], "path": ["team-categories", "stats"]}}}, {"name": "Reorder Categories (Admin)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"categories\": [\n        {\n            \"uuid\": \"{{category_id}}\",\n            \"sortOrder\": 5\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/team-categories/reorder", "host": ["{{base_url}}"], "path": ["team-categories", "reorder"]}}}, {"name": "Delete Team Category (Admin)", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/team-categories/delete/{{category_id}}", "host": ["{{base_url}}"], "path": ["team-categories", "delete", "{{category_id}}"]}}}]}, {"name": "Teams", "item": [{"name": "Create Team", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.team && response.data.team.uuid) {", "        pm.collectionVariables.set('team_id', response.data.team.uuid);", "        console.log('Team ID set:', response.data.team.uuid);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Lions FC\",\n    \"category\": \"Senior\"\n}"}, "url": {"raw": "{{base_url}}/teams/create", "host": ["{{base_url}}"], "path": ["teams", "create"]}}}, {"name": "Get All Teams", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/teams/list?category=Senior", "host": ["{{base_url}}"], "path": ["teams", "list"], "query": [{"key": "category", "value": "Senior"}]}}}, {"name": "Get Team Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/teams/show/{{team_id}}", "host": ["{{base_url}}"], "path": ["teams", "show", "{{team_id}}"]}}}, {"name": "Get Team Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/teams/statistics/{{team_id}}", "host": ["{{base_url}}"], "path": ["teams", "statistics", "{{team_id}}"]}}}, {"name": "Get Team Players", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/teams/players/{{team_id}}", "host": ["{{base_url}}"], "path": ["teams", "players", "{{team_id}}"]}}}]}, {"name": "Players", "item": [{"name": "Create Player", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.player && response.data.player.uuid) {", "        pm.collectionVariables.set('player_id', response.data.player.uuid);", "        console.log('Player ID set:', response.data.player.uuid);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"firstName\": \"<PERSON>\",\n    \"lastName\": \"Doe\",\n    \"category\": \"Senior\",\n    \"position\": \"Forward\",\n    \"jerseyNumber\": \"10\",\n    \"teamId\": \"{{team_id}}\",\n    \"birthDate\": \"1995-05-15\"\n}"}, "url": {"raw": "{{base_url}}/players/create", "host": ["{{base_url}}"], "path": ["players", "create"]}}}, {"name": "Get All Players", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/players/list", "host": ["{{base_url}}"], "path": ["players", "list"]}}}, {"name": "Get Player Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/players/show/{{player_id}}", "host": ["{{base_url}}"], "path": ["players", "show", "{{player_id}}"]}}}, {"name": "Update Player", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"firstName\": \"<PERSON>\",\n    \"lastName\": \"<PERSON>\",\n    \"position\": \"Midfielder\",\n    \"jerseyNumber\": \"8\"\n}"}, "url": {"raw": "{{base_url}}/players/update/{{player_id}}", "host": ["{{base_url}}"], "path": ["players", "update", "{{player_id}}"]}}}, {"name": "Record Player Statistics", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"playerId\": \"{{player_id}}\",\n    \"encounterId\": \"{{encounter_id}}\",\n    \"encounterName\": \"Lions FC vs Eagles FC\",\n    \"encounterDate\": \"2024-06-15\",\n    \"goals\": 2,\n    \"yellowCards\": 1,\n    \"redCards\": 0,\n    \"assists\": 1,\n    \"notes\": \"Excellent performance\"\n}"}, "url": {"raw": "{{base_url}}/players/record-statistics", "host": ["{{base_url}}"], "path": ["players", "record-statistics"]}}}, {"name": "Get Player Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/players/statistics/{{player_id}}", "host": ["{{base_url}}"], "path": ["players", "statistics", "{{player_id}}"]}}}]}, {"name": "Encounters", "item": [{"name": "Create Encounter (Admin)", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.encounter && response.data.encounter.uuid) {", "        pm.collectionVariables.set('encounter_id', response.data.encounter.uuid);", "        console.log('Encounter ID set:', response.data.encounter.uuid);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"homeTeamId\": \"{{team_id}}\",\n    \"awayTeamId\": \"{{team_id}}\",\n    \"encounterDate\": \"2024-06-15 15:00:00\",\n    \"location\": \"Stadium A\",\n    \"tournamentId\": \"{{tournament_id}}\",\n    \"status\": \"SCHEDULED\",\n    \"notes\": \"Quarter-final match\"\n}"}, "url": {"raw": "{{base_url}}/encounters/create", "host": ["{{base_url}}"], "path": ["encounters", "create"]}}}, {"name": "Get All Encounters", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/encounters/list?status=SCHEDULED&tournamentId={{tournament_id}}", "host": ["{{base_url}}"], "path": ["encounters", "list"], "query": [{"key": "status", "value": "SCHEDULED"}, {"key": "tournamentId", "value": "{{tournament_id}}"}]}}}, {"name": "Get Encounter Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/encounters/show/{{encounter_id}}", "host": ["{{base_url}}"], "path": ["encounters", "show", "{{encounter_id}}"]}}}, {"name": "Add Goal Event (Admin)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"encounterId\": \"{{encounter_id}}\",\n    \"playerId\": \"{{player_id}}\",\n    \"type\": \"GOAL\",\n    \"minute\": 45,\n    \"description\": \"Header from corner kick\",\n    \"assistPlayerId\": \"{{player_id}}\"\n}"}, "url": {"raw": "{{base_url}}/encounters/add-event", "host": ["{{base_url}}"], "path": ["encounters", "add-event"]}}}, {"name": "Add Yellow Card Event (Admin)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"encounterId\": \"{{encounter_id}}\",\n    \"playerId\": \"{{player_id}}\",\n    \"type\": \"YELLOW_CARD\",\n    \"minute\": 67,\n    \"description\": \"Unsporting behavior\"\n}"}, "url": {"raw": "{{base_url}}/encounters/add-event", "host": ["{{base_url}}"], "path": ["encounters", "add-event"]}}}, {"name": "Add Red Card Event (Admin)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"encounterId\": \"{{encounter_id}}\",\n    \"playerId\": \"{{player_id}}\",\n    \"type\": \"RED_CARD\",\n    \"minute\": 89,\n    \"description\": \"Serious foul play\"\n}"}, "url": {"raw": "{{base_url}}/encounters/add-event", "host": ["{{base_url}}"], "path": ["encounters", "add-event"]}}}, {"name": "Get Upcoming Encounters", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/encounters/upcoming", "host": ["{{base_url}}"], "path": ["encounters", "upcoming"]}}}, {"name": "Get Completed Encounters", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/encounters/completed", "host": ["{{base_url}}"], "path": ["encounters", "completed"]}}}, {"name": "Get Encounters by Team", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/encounters/team/{{team_id}}", "host": ["{{base_url}}"], "path": ["encounters", "team", "{{team_id}}"]}}}, {"name": "Get Encounters by Tournament", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/encounters/tournament/{{tournament_id}}", "host": ["{{base_url}}"], "path": ["encounters", "tournament", "{{tournament_id}}"]}}}, {"name": "Update Encounter (Admin)", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"encounterDate\": \"2024-06-15 16:00:00\",\n    \"location\": \"Stadium B\",\n    \"status\": \"ONGOING\",\n    \"notes\": \"Match moved to Stadium B due to weather\"\n}"}, "url": {"raw": "{{base_url}}/encounters/update/{{encounter_id}}", "host": ["{{base_url}}"], "path": ["encounters", "update", "{{encounter_id}}"]}}}]}, {"name": "Companies", "item": [{"name": "Get All Companies", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.companies && response.data.companies.length > 0) {", "        pm.collectionVariables.set('company_id', response.data.companies[0].uuid);", "        console.log('Company ID set:', response.data.companies[0].uuid);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/companies/list", "host": ["{{base_url}}"], "path": ["companies", "list"]}}}]}, {"name": "Statistics & Reports", "item": [{"name": "Get Top Scorers", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/players/top-scorers?limit=10", "host": ["{{base_url}}"], "path": ["players", "top-scorers"], "query": [{"key": "limit", "value": "10"}]}}}, {"name": "Get Team Statistics by Company", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/players/team-statistics/{{company_id}}", "host": ["{{base_url}}"], "path": ["players", "team-statistics", "{{company_id}}"]}}}]}]}
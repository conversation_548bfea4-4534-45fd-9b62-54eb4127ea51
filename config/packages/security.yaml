security:
  enable_authenticator_manager: true
  providers:
    app_user_provider:
      entity:
        class: App\Entity\Security\User
  password_hashers:
    App\Entity\Security\User:
      algorithm: plaintext
  firewalls:
    api_doc:
      pattern: ^/api/doc
      security: false
    api_public:
      pattern: ^/api/v2/public
      security: false
    login:
      pattern: ^/api/v2/users/authenticate/verify
      stateless: true
      json_login:
        check_path: api_login_check
        username_path: login
        password_path: code
        success_handler: lexik_jwt_authentication.handler.authentication_success
        failure_handler: lexik_jwt_authentication.handler.authentication_failure
    api:
      pattern: ^/api
      stateless: true
      provider: app_user_provider
      jwt: ~
  access_control:
    - { path: ^/api/v2/users/authenticate, roles: PUBLIC_ACCESS }
    - { path: ^/api, roles: IS_AUTHENTICATED_FULLY }
when@test:
  security:
    password_hashers:
      Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface:
        algorithm: auto
        cost: 4
        time_cost: 3
        memory_cost: 10

doctrine:
  dbal:
    url: "%env(resolve:DATABASE_URL)%"

    # IMPORTANT: You MUST configure your server version,
    # either here or in the DATABASE_URL env var (see .env file)
    #server_version: '15'
    mapping_types:
      enum: string
    types:
      userType: App\Enum\DBAL\DoctrineUserType
      userStatusType: App\Enum\DBAL\DoctrineUserStatusType
      loginType: App\Enum\DBAL\DoctrineLoginType
      genderType: App\Enum\DBAL\DoctrineGenderType
      addressType: App\Enum\DBAL\DoctrineAddressType
      fileVisibilityType: App\Enum\DBAL\DoctrineFileVisibilityType
  orm:
    auto_generate_proxy_classes: true
    enable_lazy_ghost_objects: true
    report_fields_where_declared: true
    validate_xml_mapping: true
    naming_strategy: doctrine.orm.naming_strategy.underscore_number_aware
    auto_mapping: true
    mappings:
      Security:
        is_bundle: false
        mapping: true
        type: attribute
        dir: "%kernel.project_dir%/src/Entity/Security"
        prefix: 'App\Entity\Security'
        alias: Security
      Company:
        is_bundle: false
        mapping: true
        type: attribute
        dir: "%kernel.project_dir%/src/Entity/Company"
        prefix: 'App\Entity\Company'
        alias: Company
      Shared:
        is_bundle: false
        mapping: true
        type: attribute
        dir: "%kernel.project_dir%/src/Entity/Shared"
        prefix: 'App\Entity\Shared'
        alias: Shared
      Chat:
        is_bundle: false
        mapping: true
        type: attribute
        dir: "%kernel.project_dir%/src/Entity/Chat"
        prefix: 'App\Entity\Chat'
        alias: Chat
      Activity:
        is_bundle: false
        mapping: true
        type: attribute
        dir: "%kernel.project_dir%/src/Entity/Activity"
        prefix: 'App\Entity\Activity'
        alias: Activity
      Payment:
        is_bundle: false
        mapping: true
        type: attribute
        dir: "%kernel.project_dir%/src/Entity/Payment"
        prefix: 'App\Entity\Payment'
        alias: Payment
      Player:
        is_bundle: false
        mapping: true
        type: attribute
        dir: "%kernel.project_dir%/src/Entity/Player"
        prefix: 'App\Entity\Player'
        alias: Player
      Team:
        is_bundle: false
        mapping: true
        type: attribute
        dir: "%kernel.project_dir%/src/Entity/Team"
        prefix: 'App\Entity\Team'
        alias: Team
      Tournament:
        is_bundle: false
        mapping: true
        type: attribute
        dir: "%kernel.project_dir%/src/Entity/Tournament"
        prefix: 'App\Entity\Tournament'
        alias: Tournament

when@test:
  doctrine:
    dbal:
      # "TEST_TOKEN" is typically set by ParaTest
      dbname_suffix: "_test%env(default::TEST_TOKEN)%"

when@prod:
  doctrine:
    orm:
      auto_generate_proxy_classes: false
      proxy_dir: "%kernel.build_dir%/doctrine/orm/Proxies"
      query_cache_driver:
        type: pool
        pool: doctrine.system_cache_pool
      result_cache_driver:
        type: pool
        pool: doctrine.result_cache_pool

  framework:
    cache:
      pools:
        doctrine.result_cache_pool:
          adapter: cache.app
        doctrine.system_cache_pool:
          adapter: cache.system

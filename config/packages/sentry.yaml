#when@prod:
#    sentry:
#        dsn: '%env(SENTRY_DSN)%'
#        options:
#            ignore_exceptions:
#                - 'Symfony\Component\ErrorHandler\Error\FatalError'
#                - 'Symfony\Component\Debug\Exception\FatalErrorException'

#        If you are using Monolog, you also need this additional configuration to log the errors correctly:
#        https://docs.sentry.io/platforms/php/guides/symfony/#monolog-integration
#        register_error_listener: false
#        register_error_handler: false

#    monolog:
#        handlers:
#            sentry:
#                type: sentry
#                level: !php/const Monolog\Logger::ERROR
#                hub_id: Sentry\State\HubInterface

#    Uncomment these lines to register a log message processor that resolves PSR-3 placeholders
#    https://docs.sentry.io/platforms/php/guides/symfony/#monolog-integration
#    services:
#        Monolog\Processor\PsrLogMessageProcessor:
#            tags: { name: monolog.processor, handler: sentry }

sentry:
  dsn: "%env(SENTRY_DSN)%"
  register_error_listener: false # Disables the ErrorListener to avoid duplicated log in sentry
  register_error_handler: false # Disables the ErrorListener, ExceptionListener and FatalErrorListener integrations of the base PHP SDK

  options:
    traces_sample_rate: 0.1

monolog:
  handlers:
    sentry:
      type: sentry
      level: !php/const Monolog\Logger::ERROR
      hub_id: Sentry\State\HubInterface
      fill_extra_context: true # Enables sending monolog context to Sentry
services:
  Monolog\Processor\PsrLogMessageProcessor:
    tags: { name: monolog.processor, handler: sentry }
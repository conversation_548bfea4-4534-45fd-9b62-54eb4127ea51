parameters:
  csv_fixtures_directory: "%kernel.project_dir%/src/DataFixtures/Data/"
services:
  _defaults:
    autowire: true
    autoconfigure: true
  App\:
    resource: "../src/"
    exclude:
      - "../src/DependencyInjection/"
      - "../src/Entity/"
      - "../src/Dto/"
      - "../src/Exception/"
      - "../src/Form/"
      - "../src/Kernel.php"
      - "../src/Constant"
  App\Service\Shared\Implementation\CsvService:
    arguments:
      $csvDirectory: "%csv_fixtures_directory%"
  global_exception_subscriber:
    class: App\EventSubscriber\GlobalExceptionSubscriber
    arguments: []
    tags:
      - { name: kernel.event_subscriber }

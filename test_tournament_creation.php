<?php

// Simple test script to verify tournament creation works
// Run this from the project root: php test_tournament_creation.php

require_once 'vendor/autoload.php';

use App\Entity\Tournament\Tournament;
use App\Repository\Tournament\TournamentRepository;
use Symfony\Component\Dotenv\Dotenv;

// Load environment variables
$dotenv = new Dotenv();
$dotenv->load('.env.local', '.env');

// Create a simple tournament to test the entity
$tournament = new Tournament();
$tournament->setName('Test Tournament')
           ->setDescription('Test Description')
           ->setCategory('Senior')
           ->setStartDate(new DateTime('2024-06-01'))
           ->setEndDate(new DateTime('2024-06-30'))
           ->setLocation('Test Stadium')
           ->setStatus('DRAFT')
           ->setActive(true);

echo "Tournament entity created successfully!\n";
echo "Name: " . $tournament->getName() . "\n";
echo "Category: " . $tournament->getCategory() . "\n";
echo "Status: " . $tournament->getStatus() . "\n";
echo "Start Date: " . $tournament->getStartDate()->format('Y-m-d') . "\n";
echo "End Date: " . $tournament->getEndDate()->format('Y-m-d') . "\n";

echo "\nTournament system entities are working correctly!\n";
echo "You can now test the API endpoints using the Postman collection.\n";

import mysql.connector
from env import configs


def get_db(host, user, password, database):
    return mysql.connector.connect(
        host=host,
        user=user,
        password=password,
        database=database
    )


def get_db_connection(version):
    return get_db(
        configs["db"][version]["hostname"],
        configs["db"][version]["user"],
        configs["db"][version]["password"],
        configs["db"][version]["name"]
    )


def execute(conn, sql):
    with conn.cursor(buffered=True) as db:
        db.execute(sql)
        conn.commit()


def fetchall(conn, sql):
    with conn.cursor(buffered=True) as db:
        db.execute(sql)
        return db.fetchall()


v1Db = get_db_connection("v1")
v2Db = get_db_connection("v2")

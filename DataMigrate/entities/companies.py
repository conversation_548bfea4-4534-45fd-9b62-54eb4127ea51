import datetime

import db

from util import myStr
from entities.users import link_user_to_company

dbV1 = db.v1Db
dbV2 = db.v2Db


def get_v1_companies():
    query = "SELECT * FROM company"
    return db.fetchall(dbV1, query)


def get_company_in_v2_by_phone_or_email(phone_number, email):
    try:
        query = " SELECT * FROM companies WHERE (phone_number IS NOT NULL AND phone_number = '" + \
            phone_number + \
                "') OR (email IS NOT NULL AND email = '" + email + "')"

        return db.fetchall(dbV2, query)[0]
    except:
        return None


def get_company_in_v2_by_uuid(uuid):
    try:
        query = "SELECT * FROM companies WHERE uuid = '"+uuid+"'"
        return db.fetchall(dbV2, query)[0]
    except:
        return None


def get_company_meta_in_v2_by_code_and_company_id(code, company_id):
    try:
        query = "SELECT * FROM company_metas WHERE company_id = '" + \
            myStr(company_id)+"' AND code = '" + code + "'"
        return db.fetchall(dbV2, query)[0]
    except:
        return None


def get_gender(val):
    if val:
        return myStr(val).upper()
    return ''


def get_created_at(val):
    if val:
        return val.strftime("%Y-%m-%d %H:%M:%S")
    return ''


def get_date_of_birth(val):
    if val:
        return val.strftime("%Y-%m-%d")
    return ''


def get_description(val):
    if val:
        return val.replace("'", "\\'")
    return ''


def create_company(companyV1):
    query = "INSERT INTO companies (name, uuid, description, active, created_at) VALUES ('" + \
        companyV1[3] + "', '" + companyV1[0]+"', '" + get_description(companyV1[14]) + \
            "', 1, '" + get_created_at(datetime.datetime.now()) + "')"

    db.execute(dbV2, query)

    return companyV1[0]


def update_company(companyV1, companyV2):
    query = "UPDATE companies SET description = '" + get_description(companyV1[14]) + \
            "' WHERE uuid = '" + companyV2[4] + "'"

    db.execute(dbV2, query)

    return companyV2[4]


def get_country(country):
    if country == "Algérie":
        return "DZ"
    if country == "Cameroun":
        return "CM"
    return country


def get_date_hour(hour):
    if hour:
        return datetime.datetime.now().strftime("%Y-%m-%d")+"T"+str(hour)+".000Z"
    return ''


def get_opening_hours(uuid):
    text = ""
    # try:
    query = "SELECT * FROM company_openhours WHERE company_id = '" + uuid + "'"
    openingHours = db.fetchall(dbV1, query)
    for openingHour in openingHours:
        text = text + "|"+get_date_hour(openingHour[3]) + ","+get_date_hour(
            openingHour[4])+","+openingHour[2]+","+myStr(openingHour[5])
    # except:
    #    pass
    return text


def save_company_metas(uuid, company_array):
    if uuid:
        company = get_company_in_v2_by_uuid(uuid)
        if company:
            meta = {
                "_address": company_array[4],
                "_siren": "",
                "_type": company_array[12],
                "_activity": company_array[13],
                "_coverImage": "",
                "_profileImage":  "",
                "_website": company_array[15],
                "_email": company_array[16],
                "_phoneNumber": company_array[17],
                "_zipCode": company_array[5],
                "_latitude": company_array[23],
                "_longitude": company_array[22],
                "_city": company_array[6],
                "_country": company_array[7],
                "_openingHours":  get_opening_hours(uuid),
                "_membership": company_array[1],
                "_backCardPicture": "",
                "_frontCardPicture": "",
                "_idProfessional": "",
            }
            for key, val in meta.items():
                # try:
                metaExists = get_company_meta_in_v2_by_code_and_company_id(
                    key, company[0])
                query = ""
                if not metaExists:
                    query = "INSERT INTO company_metas (company_id, code, created_at, value) VALUES (" + \
                        myStr(company[0]) + ", '" + key + "', '" + \
                        get_created_at(datetime.datetime.now()
                                       ) + "', '" + myStr(val)+"')"
                else:
                    query = "UPDATE company_metas SET value = '" + myStr(val) + \
                        "' WHERE id = '" + myStr(metaExists[0]) + "'"
                db.execute(dbV2, query)
                # except:
                #    pass


def get_v1_associate_by_company_uuid(company_uuid):
    try:
        query = "SELECT * FROM associate WHERE company_id = '"+company_uuid+"'"
        return db.fetchall(dbV1, query)[0]
    except:
        return None


def migrate_companies():
    v1companies = get_v1_companies()

    for company_array in v1companies:
        # try:
        companyV2 = get_company_in_v2_by_uuid(company_array[0])
        uuid = None
        if not companyV2:
            uuid = create_company(company_array)
        else:
            uuid = update_company(company_array, companyV2)

        if uuid:
            companyV2 = get_company_in_v2_by_uuid(uuid)
            save_company_metas(uuid, company_array)
            associate = get_v1_associate_by_company_uuid(uuid)
            if associate:
                link_user_to_company(companyV2, associate)
        # except:
        #    pass

import datetime

import db
from env import configs

from util import myStr, sanitizeData
from entities.companies import get_company_in_v2_by_uuid
from entities.users import get_user_in_v2_by_company_id
import os.path

dbV1 = db.v1Db
dbV2 = db.v2Db


def get_v1_activities():
    query = "SELECT * FROM instant"
    return db.fetchall(dbV1, query)


def get_activity_in_v2_by_uuid(uuid):
    try:
        query = " SELECT * FROM activities WHERE uuid = '" + uuid + "'"
        return db.fetchall(dbV2, query)[0]
    except:
        pass
    return None


def get_activity_category_in_v1_by_id(id):
    try:
        query = " SELECT * FROM instant_category WHERE id = '" + str(id) + "'"
        return db.fetchall(dbV1, query)[0]
    except:
        pass
    return None


def get_activity_times_in_v1_by_uuid(uuid):
    try:
        query = " SELECT * FROM instant_activation_time WHERE instant_id = '" + uuid + "'"
        return db.fetchall(dbV1, query)
    except:
        pass
    return []


def get_created_at(val):
    if val:
        return val.strftime("%Y-%m-%d %H:%M:%S")
    return '0001-01-01'


def get_time(val):
    if val:
        if isinstance(val, str):
            return val
        return val.strftime("%Y-%m-%dT%H:%M:%S.000Z")
    return '0001-01-01T00:00:00.000Z'


def create_activity(activityV1, owner_id, creator_id):
    query = "INSERT INTO activities (owner_id, creator_id, title, short_description, long_description, place_name, street_name, city, zip_code, latitude, longitude, country, price, status, active, uuid, created_at) VALUES ('"  \
        + myStr(owner_id) + "', '" + myStr(creator_id) + "', '" + myStr(activityV1[1]) + "', '"  \
        + myStr(activityV1[4]) + "', '" + myStr(activityV1[5]) + "', '" + myStr(activityV1[6]) + "', '"  \
        + myStr(activityV1[7]) + "', '" + myStr(activityV1[8]) + "', " + sanitizeData(myStr(activityV1[9])) + ", "  \
        + sanitizeData(myStr(activityV1[10])) + ", " + sanitizeData(myStr(activityV1[11])) + ", '" + myStr(activityV1[13]) + "', "  \
        + sanitizeData(myStr(activityV1[14])) + ", 'ACTIVE', " + sanitizeData(myStr(activityV1[16], '1')) + ", '"  \
        + myStr(activityV1[0]) + "', '" + myStr(get_created_at(datetime.datetime.now()))   \
        + "') "

    db.execute(dbV2, query)

    return activityV1[0]


def update_activity(activityV1):
    query = "UPDATE activities SET title = '" + \
        myStr(activityV1[1]) + "', short_description = '" + \
        myStr(activityV1[4]) + "', long_description = '" + myStr(activityV1[5]) + \
        "' WHERE uuid = '" + activityV1[0] + "'"

    db.execute(dbV2, query)

    return activityV1[0]


def get_activity_in_v1_by_id(id):
    try:
        query = "SELECT * FROM instant WHERE id = '" + str(id) + "'"
        return db.fetchall(dbV1, query)[0]
    except:
        pass
    return None


def get_activity_meta_in_v2_by_code_and_activity_id(code, activity_id):
    try:
        query = "SELECT * FROM activity_metas WHERE activity_id = '" + \
            myStr(activity_id)+"' AND code = '" + code + "'"
        return db.fetchall(dbV2, query)[0]
    except:
        return None


def get_picture(fileIdInV1):
    try:
        query = "SELECT * FROM file WHERE id = '" + str(fileIdInV1) + "'"
        file = db.fetchall(dbV1, query)[0]
        if file:
            filename = file[1]
            if os.path.isfile(f"{configs['upload_dir']}/instant/{filename}"):
                return f"{configs['api_url']}/upload/instant/{filename}"
    except:
        pass
    return 'TEST'


def save_activity_metas(uuid, activity_array):
    if uuid:
        activity = get_activity_in_v2_by_uuid(uuid)
        category = get_activity_category_in_v1_by_id(activity_array[2])
        times = get_activity_times_in_v1_by_uuid(activity_array[0])

        meta = {
            "_picture": get_picture(activity_array[3]),
            "_category": '',
            "_instantActivationTimes": '',
            "_instantPartner": activity_array[15],
        }

        if category:
            meta["_category"] = category[1]

        instantActivationTimes = ""

        for time in times:
            instantActivationTimes += "|" + \
                time[3]+","+get_time(time[7])+","+get_time(time[8]) + \
                ","+myStr(time[2])+","+time[6]
        meta["_instantActivationTimes"] = instantActivationTimes

        for key, val in meta.items():
            # try:
            metaExists = get_activity_meta_in_v2_by_code_and_activity_id(
                key, activity[0])
            query = ""
            if not metaExists:
                query = "INSERT INTO activity_metas (activity_id, code, created_at, value) VALUES (" + \
                    sanitizeData(myStr(activity[0])) + ", '" + key + "', '" + \
                    get_created_at(datetime.datetime.now()) + \
                    "', " + sanitizeData(myStr(val))+")"
            else:
                query = "UPDATE activity_metas SET value = " + sanitizeData(myStr(val)) + \
                    " WHERE id = '" + myStr(metaExists[0]) + "'"
            db.execute(dbV2, query)
            # except:
            #    pass


def migrate_activities():
    v1activities = get_v1_activities()

    for activity_array in v1activities:

        # try:
        company = get_company_in_v2_by_uuid(activity_array[12])
        if company:
            user = get_user_in_v2_by_company_id(company[0])

            activityV2 = get_activity_in_v2_by_uuid(activity_array[0])
            if user:
                uuid = None
                if not activityV2:
                    uuid = create_activity(
                        activity_array, user[0], user[0])
                else:
                    uuid = update_activity(activity_array)

                if uuid:
                    save_activity_metas(uuid, activity_array)
        # except:
        #    pass

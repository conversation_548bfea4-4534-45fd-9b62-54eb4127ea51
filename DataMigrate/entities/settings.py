import datetime

import db
import uuid

from util import myStr

dbV1 = db.v1Db
dbV2 = db.v2Db


def get_v1_settings():
    query = "SELECT * FROM settings"
    return db.fetchall(dbV1, query)


def get_setting_in_v2_by_code(code):
    try:
        query = " SELECT * FROM settings WHERE code = '" + code + "'"
        return db.fetchall(dbV2, query)[0]
    except:
        pass
    return None


def get_created_at(val):
    if val:
        return val.strftime("%Y-%m-%d %H:%M:%S")
    return '0001-01-01'


def create_setting(settingV1):
    uuidV4 = myStr(uuid.uuid4())
    query = "INSERT INTO settings (code, value, created_at, uuid, progiciel, description) VALUES ('" + myStr(settingV1[1]) + "', '" + myStr(
        settingV1[3])+"', '" + get_created_at(datetime.datetime.now()) + "', '" + uuidV4 + "', '" + '0' + "', '" + myStr(settingV1[2]) + "')"

    db.execute(dbV2, query)

    return uuidV4


def update_setting(settingV1, settingV2):
    query = "UPDATE settings SET value = '" + \
        myStr(settingV1[3]) + "', description = '" + \
        myStr(settingV1[2]) + "' WHERE uuid = '" + settingV2[6] + "'"

    db.execute(dbV2, query)

    return settingV2[6]


def migrate_settings():
    v1Settings = get_v1_settings()

    for setting_array in v1Settings:
        # try:
        settingV2 = get_setting_in_v2_by_code(setting_array[1])
        if not settingV2:
            create_setting(setting_array)
        else:
            update_setting(setting_array, settingV2)

        # except:
        #    pass

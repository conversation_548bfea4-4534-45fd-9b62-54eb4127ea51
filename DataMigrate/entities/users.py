import datetime

import db
import uuid

from util import myStr, sanitizeData

dbV1 = db.v1Db
dbV2 = db.v2Db


def get_v1_users():
    query = "SELECT * FROM profile"
    return db.fetchall(dbV1, query)


def get_rgpd_in_v1_by_phone(phone_number):
    try:
        query = "SELECT * FROM rgpd WHERE phone_number = '" + phone_number + "'"
        return db.fetchall(dbV1, query)[0]
    except:
        return None


def get_user_in_v2_by_phone_or_email(phone_number, email):
    try:
        query = " SELECT * FROM users WHERE phone_number = '" + phone_number + "'"
        return db.fetchall(dbV2, query)[0]
    except:
        try:
            query = " SELECT * FROM users WHERE email = '" + email + "'"
            return db.fetchall(dbV2, query)[0]
        except:
            pass
    return None


def get_user_in_v2_by_uuid(uuid):
    try:
        query = "SELECT * FROM users WHERE uuid = '"+uuid+"'"
        return db.fetchall(dbV2, query)[0]
    except:
        return None


def get_user_in_v2_by_company_id(company_id):
    try:
        query = "SELECT * FROM users WHERE company_id = '" + \
            myStr(company_id)+"'"
        return db.fetchall(dbV2, query)[0]
    except:
        return None


def get_user_meta_in_v2_by_code_and_user_id(code, user_id):
    try:
        query = "SELECT * FROM user_metas WHERE user_id = '" + \
            myStr(user_id)+"' AND code = '" + code + "'"
        return db.fetchall(dbV2, query)[0]
    except:
        return None


def get_gender(val):
    if val:
        return myStr(val).upper()
    return 'UNKNOWN'


def get_created_at(val):
    if val:
        return val.strftime("%Y-%m-%d %H:%M:%S")
    return ''


def get_date_of_birth(val):
    if val:
        return val.strftime("%Y-%m-%d")
    return ''


def create_user(userV1):
    uuidV4 = myStr(uuid.uuid4())
    query = "INSERT INTO users (phone_number, first_name, last_name, gender, date_of_birth, weight, " + \
        "height, email, email_verified, phone_number_verified, type, status, created_at, " + \
        "active, uuid) VALUES (" + sanitizeData(myStr(userV1[0])) + ", " + sanitizeData(myStr(userV1[1])) + ", " + sanitizeData(myStr(userV1[2])) + ", " + sanitizeData(get_gender(userV1[3])) + \
        ", " + sanitizeData(get_date_of_birth(userV1[4])) + ", " + myStr(userV1[7], '0') + ", " + myStr(userV1[8], '0') + ", " + \
            sanitizeData(myStr(userV1[12])) + ", 1, 1, 'PHYSICAL', 'ACTIVE', '" + \
        get_created_at(userV1[5]) + "', 1, '" + uuidV4 + "')"

    db.execute(dbV2, query)

    return uuidV4


def update_user(userV1, userV2):
    query = "UPDATE users SET first_name = " + sanitizeData(myStr(userV1[1])) + ", last_name = " + sanitizeData(myStr(userV1[2])) + ", gender = '" + get_gender(userV1[3]) + "', date_of_birth = " + sanitizeData(get_date_of_birth(
        userV1[4])) + ", weight = '" + myStr(userV1[7], '0') + "', height = '" + myStr(userV1[8], '0') + "' WHERE uuid = '" + userV2[20] + "'"

    db.execute(dbV2, query)

    return userV2[20]


def get_country(country):
    if country == "Algérie":
        return "DZ"
    if country == "Cameroun":
        return "CM"
    return country


def save_user_metas(uuid, user_array):
    if uuid:
        user = get_user_in_v2_by_uuid(uuid)
        rgpd = get_rgpd_in_v1_by_phone(user[3])

        meta = {
            "country": get_country(user_array[11]),
            "city": user_array[10],
            "address": user_array[9],
            "logged_in_type": "PHONE",
            "is_new": "0",
            "is_admin":  user_array[14],
            "spent_amount": '0.00',
            "fidelity_balance": myStr(user_array[15], '0.00'),
            "spent_fidelity_amount": myStr(user_array[16], '0.00'),
        }
        if rgpd:
            meta["ip"] = rgpd[5]
            meta["device_id"] = rgpd[3]
            meta["cgu"] = '1'
            meta["pc"] = '1'

        for key, val in meta.items():
            # try:
            metaExists = get_user_meta_in_v2_by_code_and_user_id(
                key, user[0])
            query = ""
            if not metaExists:
                query = "INSERT INTO user_metas (user_id, code, created_at, value) VALUES (" + \
                    sanitizeData(myStr(user[0])) + ", '" + key + "', '" + \
                    get_created_at(datetime.datetime.now()) + \
                    "', " + sanitizeData(myStr(val))+")"
            else:
                query = "UPDATE user_metas SET value = " + sanitizeData(myStr(val)) + \
                    " WHERE id = '" + myStr(metaExists[0]) + "'"
            db.execute(dbV2, query)
            # except:
            #    pass


def link_user_to_company(companyV2, associate):
    if associate and companyV2:
        user = get_user_in_v2_by_phone_or_email(associate[1], associate[1])
        if user:
            query = "UPDATE users SET company_id = '" + \
                str(companyV2[0]) + "' WHERE uuid = '" + user[20] + "'"
            db.execute(dbV2, query)


def migrate_users():
    v1Users = get_v1_users()

    for user_array in v1Users:
        # try:
        userV2 = get_user_in_v2_by_phone_or_email(
            user_array[0], user_array[12])
        uuid = None
        if not userV2:
            uuid = create_user(user_array)
        else:
            uuid = update_user(user_array, userV2)

        if uuid:
            save_user_metas(uuid, user_array)
        # except:
        #    pass

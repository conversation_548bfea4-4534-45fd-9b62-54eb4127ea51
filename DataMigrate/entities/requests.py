import datetime

import db
import uuid

from util import myStr

dbV1 = db.v1Db
dbV2 = db.v2Db


def get_v1_requests():
    query = "SELECT * FROM requests"
    return db.fetchall(dbV1, query)


def get_request_in_v2_by_title_and_phone(titre, phone):
    try:
        query = " SELECT * FROM requests WHERE titre = '" + \
            titre + "' AND user = '" + phone + "'"
        return db.fetchall(dbV2, query)[0]
    except:
        pass
    return None


def get_created_at(val):
    if val:
        return val.strftime("%Y-%m-%d %H:%M:%S")
    return '0001-01-01'


def create_request(requestV1):
    uuidV4 = myStr(uuid.uuid4())
    query = "INSERT INTO requests (titre, description, created_at, closed, user) VALUES ('" + myStr(requestV1[1]) + "', '" + myStr(
        requestV1[2])+"', '" + get_created_at(datetime.datetime.now()) + "', '" + myStr(requestV1[4], '0') + "', '" + myStr(requestV1[5]) + "')"

    db.execute(dbV2, query)

    return uuidV4


def update_request(requestV1, requestV2):
    query = "UPDATE requests SET titre = '" + \
        myStr(requestV1[1]) + "', description = '" + \
        myStr(requestV1[2]) + "', closed = '" + myStr(requestV1[4], '0') + \
        "' WHERE titre = '" + requestV1[1] + \
            "' AND user = '" + myStr(requestV1[5]) + "'"

    db.execute(dbV2, query)

    return requestV2[5]


def get_request_in_v1_by_id(id):
    try:
        query = "SELECT * FROM requests WHERE id = '" + str(id) + "'"
        return db.fetchall(dbV1, query)[0]
    except:
        pass
    return None


def migrate_requests():
    v1Requests = get_v1_requests()

    for request_array in v1Requests:
        # try:
        requestV2 = get_request_in_v2_by_title_and_phone(
            request_array[1], request_array[5])
        if not requestV2:
            create_request(request_array)
        else:
            update_request(request_array, requestV2)

        # except:
        #    pass

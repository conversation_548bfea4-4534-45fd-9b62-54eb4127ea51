import datetime

import db
import uuid

from util import myStr, sanitizeData
from entities.requests import get_request_in_v1_by_id, get_request_in_v2_by_title_and_phone
from entities.users import get_user_in_v2_by_phone_or_email

dbV1 = db.v1Db
dbV2 = db.v2Db


def get_v1_responses():
    query = "SELECT * FROM responses"
    return db.fetchall(dbV1, query)


def get_response_in_v2_by_request_id_and_message_and_phone(request_id, message, phone):
    try:
        query = " SELECT * FROM responses WHERE message = '" + \
            message + "' AND user = '" + phone + "' AND request_id"
        return db.fetchall(dbV2, query)[0]
    except:
        pass
    return None


def get_created_at(val):
    if val:
        return val.strftime("%Y-%m-%d %H:%M:%S")
    return '0001-01-01'


def get_user_timestamp(val):
    if val:
        return val.strftime("%Y-%m-%dT%H:%M:%S.000Z")
    return '0001-01-01'


def get_user_name(user):
    if user:
        if user[4] and user[5]:
            return myStr(user[4]) + " " + myStr(user[5])
        elif user[4]:
            return myStr(user[4])
        elif user[5]:
            return myStr(user[5])
        elif user[10]:
            return myStr(user[10])
        elif user[3]:
            return myStr(user[3])
    return ''


def create_response(responseV1, requestV2, user):
    uuidV4 = myStr(uuid.uuid4())
    query = "INSERT INTO responses (message, requests_id, user, admin, created_at, user_uuid, user_timestamp, message_uuid, user_name) VALUES ('" + myStr(responseV1[1]) + "', '" + myStr(
        requestV2[0])+"', '" + myStr(responseV1[3]) + "', '" + myStr(responseV1[4], '0') + "', '" + get_created_at(responseV1[5]) + "', " + sanitizeData(user[20]) + ", '" + get_user_timestamp(responseV1[5]) + "', '" + uuidV4 + "', " + sanitizeData(get_user_name(user)) + ")"

    db.execute(dbV2, query)

    return uuidV4


def update_response(responseV1, requestV2, user):
    query = "UPDATE responses SET message = '" + \
        myStr(responseV1[1]) + "', user_name = '" + \
        myStr(get_user_name(user)) + "', admin = '" + myStr(responseV1[4], '0') + \
        "' WHERE message = '" + responseV1[1] + \
            "' AND user = '" + \
        myStr(responseV1[5]) + "' AND = '" + requestV2[0] + "'"

    db.execute(dbV2, query)

    return responseV1[5]


def migrateresponses():
    v1Responses = get_v1responses()

    for response_array in v1Responses:
        # try:
        requestV1 = get_request_in_v1_by_id(response_array[2])
        requestV2 = get_request_in_v2_by_title_and_phone(
            requestV1[1], requestV1[5])

        if requestV2:
            responseV2 = get_response_in_v2_by_request_id_and_message_and_phone(requestV2[0],
                                                                                response_array[1], response_array[5])

            user = get_user_in_v2_by_phone_or_email(requestV1[5], requestV1[5])

            if user:
                if not responseV2:
                    create_response(response_array, requestV2, user)
                else:
                    update_response(response_array, requestV2, user)

        # except:
        #    pass
